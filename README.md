# Dolze - AI-Powered Business Platform

<div align="center">
  <img src="public/images/logo.png" alt="Dolze Logo" width="200"/>
  <p><em>Transform your ideas into thriving businesses</em></p>
</div>

## Project Description

Dolze is an AI-powered platform designed to help entrepreneurs validate business ideas, build sustainable business models, and manage their online presence effectively. Built with Next.js and TypeScript, this application combines intelligent chatbot interactions, advanced analytics, customizable landing pages, and multi-platform social media integration to provide a comprehensive business development toolkit.

## Prerequisites

- Node.js (v20 or higher)
- pnpm (v8 or higher)
- Git for version control
- Modern web browser (Chrome, Firefox, Safari, Edge)

## Technologies Used

- **Framework**: Next.js 15.1.3 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom theme support
- **State Management**: React Context API
- **UI Components**: Custom component library with dark mode support
- **Testing**: Jest and React Testing Library
- **Code Quality**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, lint-staged, commitlint
- **Package Manager**: pnpm

## Installation Instructions

1. **Clone the Repository**

   ```bash
   git clone [repository-url]
   cd dolze-webapp-core
   ```

2. **Install Dependencies**

   ```bash
   pnpm install
   ```

3. **Environment Setup**
   - Copy the example environment file
   ```bash
   cp .env.example .env.local
   ```
   - Update the environment variables with your configuration

## Environment Variables

The application requires the following environment variables:

- `BACKEND_URL`: URL for the backend API
- `NEXT_PUBLIC_FIREBASE_API_KEY`: Firebase API key
- `NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN`: Firebase auth domain
- `NEXT_PUBLIC_FIREBASE_PROJECT_ID`: Firebase project ID
- `NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET`: Firebase storage bucket
- `NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID`: Firebase messaging sender ID
- `NEXT_PUBLIC_FIREBASE_APP_ID`: Firebase app ID

## Usage Instructions

1. **Start Development Server**

   ```bash
   pnpm dev
   ```

   This will start the development server on port 3000 and proxy HTTPS requests from port 443.

2. **Access the Application**

   - Open [https://localhost:443](https://localhost:443) in your browser
   - For HTTP access, use [http://localhost:3000](http://localhost:3000)

3. **Build for Production**

   ```bash
   pnpm build
   ```

4. **Start Production Server**
   ```bash
   pnpm start
   ```

## Project Structure

```
src/
├── app/                 # Next.js app router pages and layouts
│   ├── (admin)/        # Admin dashboard pages
│   ├── api/            # API routes
│   └── auth/           # Authentication pages
├── components/          # Reusable UI components
│   ├── auth/           # Authentication components
│   ├── landing/        # Landing page components
│   ├── socialMedia/    # Social media integration components
│   └── ui/             # Common UI elements
├── config/             # Configuration files
├── constants/          # Application constants
├── context/            # React Context providers
├── hooks/              # Custom React hooks
├── icons/              # SVG icons and assets
├── layout/             # Layout components
├── services/           # API services
├── store/              # State management
├── types/              # TypeScript type definitions
└── utils/              # Utility functions
```

## Scripts

- `pnpm dev`: Start development server with HTTPS proxy
- `pnpm build`: Build the application for production
- `pnpm start`: Start the production server
- `pnpm lint`: Run ESLint to check code quality
- `pnpm format`: Run Prettier to format code
- `pnpm format:check`: Check if code is properly formatted
- `pnpm test`: Run Jest tests
- `pnpm test:watch`: Run Jest tests in watch mode
- `pnpm test:coverage`: Generate test coverage report

## Core Features

### Social Media Integration

- Multi-platform management for Instagram, Twitter, and other social networks
- Unified dashboard for cross-platform insights
- Real-time performance metrics

### Analytics Dashboard

- Comprehensive analytics with platform-specific engagement metrics
- Audience growth and interaction tracking
- Custom report generation
- Performance trend analysis

### Landing Page Builder

- Professional page creation with drag-and-drop interface
- Mobile-responsive templates
- Custom domain integration
- SEO optimization tools

### AI-Powered Chatbot

- Interactive business idea validation flow
- Decision tree-based conversation management
- API integration for real-time business analysis
- Customizable UI with skip/edit functionality
- Context-aware responses based on user input
- Multi-stage business validation process
- Seamless transition between conversation stages

## Testing

The application uses Jest and React Testing Library for testing:

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Generate test coverage report
pnpm test:coverage
```

## Code Quality Tools

The project uses several tools to maintain code quality:

### Husky

- Automates the execution of scripts during Git hooks
- Ensures code quality by running linting and formatting checks before commits

### Lint-Staged

- Runs linters on staged files in Git
- Only checks files that are about to be committed

### Commitlint

- Enforces commit message conventions
- Helps maintain a consistent commit history

## Chatbot Component

The Dolze platform features a sophisticated chatbot component that guides users through business idea validation and development. This component is highly customizable and follows a decision tree architecture.

### Key Features

- **Decision Tree Architecture**: Conversation flow is managed through a configurable decision tree
- **API Integration**: Seamless integration with validation and analysis APIs
- **Customizable UI**: Fully customizable message bubbles, buttons, and input fields
- **Edit/Skip Functionality**: Users can edit responses or skip certain steps
- **Context Preservation**: Maintains conversation context throughout the flow

### Implementation

The chatbot is implemented as a modular React component with the following structure:

```
src/components/ui/chatbot/
├── Chatbot.tsx              # Main component wrapper
├── ChatbotConfig.tsx        # Configuration for conversation flow
├── ChatControls.tsx         # Input controls and action buttons
├── ChatInput.tsx            # Text input component
├── ChatMessage.tsx          # Message bubble component
├── hooks/                   # Custom hooks for chatbot functionality
│   ├── useChatbot.tsx       # Core chatbot state management
│   └── useNodeExecution.tsx # Node execution logic
└── types.ts                 # TypeScript type definitions
```

For detailed information on extending the chatbot, see the [chatbot.readme.md](./chatbot.readme.md) file.

## Known Issues

- The chatbot may experience performance issues with very long conversations
- Some social media platform integrations require periodic token refresh
- Image optimization can be improved for faster loading on slower connections

## Screenshots

<div align="center">
  <img src="public/images/screenshots/dashboard.png" alt="Dashboard" width="400"/>
  <p><em>Dashboard with analytics overview</em></p>

  <img src="public/images/screenshots/chatbot.png" alt="Chatbot" width="400"/>
  <p><em>Business idea validation chatbot</em></p>

  <img src="public/images/screenshots/social-media.png" alt="Social Media" width="400"/>
  <p><em>Social media management interface</em></p>
</div>

## Development Guidelines

### Code Standards

- Follow TypeScript strict mode
- Implement proper type definitions
- Use functional components
- Maintain consistent naming conventions

### Component Development

- Create reusable components
- Implement proper prop typing
- Use composition over inheritance
- Follow atomic design principles

### Performance Optimization

- Implement code splitting
- Use Next.js Image optimization
- Minimize bundle size
- Optimize API calls

### Pull Request Guidelines

- Follow the existing code style
- Update documentation as needed
- Add tests for new features
- Ensure all tests pass

## Contact Information

For questions or support, please contact:

- [Your Name/Team Name]
- [Email Address]
- [GitHub Profile/Organization]
