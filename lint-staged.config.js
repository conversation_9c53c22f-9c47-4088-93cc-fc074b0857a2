import { relative } from 'path';

const buildEslintCommand = (filenames) =>
  `pnpm eslint --fix ${filenames.map((f) => relative(process.cwd(), f)).join(' ')}`;

const buildTestCommand = (filenames) => {
  // If no files are passed, return a command that will always succeed
  if (filenames.length === 0) {
    return 'echo "No files to test"';
  }

  // Filter to only .ts and .tsx files
  const tsFiles = filenames.filter(
    (file) => file.endsWith('.ts') || file.endsWith('.tsx')
  );

  if (tsFiles.length === 0) {
    return 'echo "No TypeScript files to test"';
  }

  // Only test files that are components or have associated tests
  const testableFiles = tsFiles.filter((file) => {
    // Skip config files
    if (file.includes('config') || file.includes('setup')) {
      return false;
    }
    return true;
  });

  if (testableFiles.length === 0) {
    return 'echo "No testable files found"';
  }

  // Get the relative paths for the files
  const relativeFilePaths = testableFiles.map((file) =>
    relative(process.cwd(), file)
  );

  // Join the file paths with spaces for the command
  const filesArg = relativeFilePaths.join(' ');

  // Run tests with coverage only for the staged files
  return `pnpm jest --bail --findRelatedTests ${filesArg} --coverage --collectCoverageFrom='${filesArg}'`;
};

const config = {
  // Run TypeScript type checking on staged files
  '**/*.{ts,tsx}': (files) => {
    // Filter out test files
    const nonTestFiles = files.filter(
      (file) => !file.endsWith('.test.ts') && !file.endsWith('.test.tsx')
    );
    return nonTestFiles.length > 0
      ? 'pnpm tsc --noEmit'
      : 'echo "No non-test files to check"';
  },

  // Run ESLint on staged files (excluding test files)
  '**/*.{js,jsx,ts,tsx}': (files) => {
    // Filter out test files
    const nonTestFiles = files.filter(
      (file) =>
        !file.endsWith('.test.js') &&
        !file.endsWith('.test.jsx') &&
        !file.endsWith('.test.ts') &&
        !file.endsWith('.test.tsx')
    );
    return nonTestFiles.length > 0
      ? buildEslintCommand(nonTestFiles)
      : 'echo "No non-test files to lint"';
  },

  // Run Jest tests on staged test files and their corresponding implementation files
  '**/*.{ts,tsx}': [buildTestCommand],

  // Format staged files with Prettier
  '**/*.{js,jsx,ts,tsx,json,css,scss,md}': ['pnpm prettier --write'],
};

export default config;
