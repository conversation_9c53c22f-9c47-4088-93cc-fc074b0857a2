<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Typography System Preview</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            color: #101828;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            gap: 40px;
        }
        
        .section {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 24px;
            background-color: #fff;
        }
        
        .title {
            font-size: 40px;
            line-height: 48px;
            font-weight: 700;
            color: #FE8C00;
            margin-bottom: 16px;
        }
        
        .heading-1 {
            font-size: 24px;
            line-height: 32px;
            font-weight: 600;
            margin-bottom: 16px;
        }
        
        .heading-2 {
            font-size: 18px;
            line-height: 28px;
            font-weight: 400;
            margin-bottom: 16px;
        }
        
        .heading-3 {
            font-size: 14px;
            line-height: 20px;
            font-weight: 500;
            margin-bottom: 16px;
        }
        
        .info {
            font-size: 12px;
            line-height: 16px;
            font-weight: 400;
            color: #667085;
        }
        
        .button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 16px;
            background-color: #FE8C00;
            color: white;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border: none;
        }
        
        .button-outline {
            background-color: transparent;
            border: 1px solid #FE8C00;
            color: #FE8C00;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }
        
        .card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            background-color: #fff;
        }
        
        .dark-mode {
            background-color: #101828;
            color: #fff;
        }
        
        .dark-mode .card {
            background-color: #1D2939;
            border-color: #344054;
        }
        
        .dark-mode .section {
            background-color: #1D2939;
            border-color: #344054;
        }
        
        .dark-mode .info {
            color: #98A2B3;
        }
        
        .dark-mode .heading-2 {
            color: #E4E7EC;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="section">
            <h1 class="heading-1">Typography System</h1>
            <p class="heading-2">This preview shows the typography system with the new title size (40pt) and consistent font sizes throughout the application.</p>
        </div>
        
        <div class="comparison">
            <div class="section">
                <h2 class="heading-1">Light Mode</h2>
                <div class="card">
                    <h1 class="title">Transform Your Ideas Into Reality</h1>
                    <p class="heading-2">Validate concepts, gather feedback, and build your business with Dolze's powerful tools.</p>
                    <div style="display: flex; gap: 12px; margin-top: 16px;">
                        <button class="button">New Project</button>
                        <button class="button button-outline">Explore Samples</button>
                    </div>
                </div>
            </div>
            
            <div class="section dark-mode">
                <h2 class="heading-1">Dark Mode</h2>
                <div class="card">
                    <h1 class="title">Transform Your Ideas Into Reality</h1>
                    <p class="heading-2">Validate concepts, gather feedback, and build your business with Dolze's powerful tools.</p>
                    <div style="display: flex; gap: 12px; margin-top: 16px;">
                        <button class="button">New Project</button>
                        <button class="button button-outline">Explore Samples</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2 class="heading-1">Typography Scale</h2>
            <div style="display: flex; flex-direction: column; gap: 24px;">
                <div>
                    <h1 class="title">Title: 40pt</h1>
                    <p class="info">Used exclusively for main page titles and hero headings</p>
                </div>
                <div>
                    <h2 class="heading-1">Heading 1: 24pt</h2>
                    <p class="info">Used for section headings and important content</p>
                </div>
                <div>
                    <h3 class="heading-2">Heading 2/Running: 18pt</h3>
                    <p class="info">Used for subheadings and body text</p>
                </div>
                <div>
                    <h4 class="heading-3">Heading 3/CTA: 14pt</h4>
                    <p class="info">Used for buttons, navigation, and smaller headings</p>
                </div>
                <div>
                    <span class="info">Info: 12pt - Used for captions, footnotes, and supplementary information</span>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2 class="heading-1">Font Weights</h2>
            <div style="display: flex; flex-direction: column; gap: 16px;">
                <p style="font-weight: 700;" class="heading-2">Bold (700): Used for titles and primary headings</p>
                <p style="font-weight: 600;" class="heading-2">Semibold (600): Used for section headings</p>
                <p style="font-weight: 500;" class="heading-2">Medium (500): Used for buttons and emphasis</p>
                <p style="font-weight: 400;" class="heading-2">Regular (400): Used for body text and general content</p>
            </div>
        </div>
    </div>
</body>
</html>
