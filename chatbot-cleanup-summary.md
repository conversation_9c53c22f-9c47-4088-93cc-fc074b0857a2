# Chatbot Component Cleanup Summary

## Utility Files Created

1. **ChatbotApiUtils.ts**
   - Contains API-related utility functions
   - Functions: `validateIdea`, `getBusinessContext`, `saveBusinessContext`

2. **ChatbotMessageUtils.ts**
   - Contains message-related utility functions
   - Functions: `createUserMessage`, `createBotMessage`, `createSystemMessage`, `createLoadingMessage`, `createErrorMessage`

3. **Enhanced ChatbotUtils.ts**
   - Added more utility functions
   - New functions: `updateMessage`, `setShowButtons`, `isValidUrl`, `getNodeContent`

## Usage Examples

### Creating Messages

```tsx
// Before
const userMessage: ExtendedMessage = {
  id: Date.now().toString(),
  content: currentInputValue,
  sender: 'user',
  timestamp: new Date(),
  avatar: userPhotoURL || '/images/user/default-avatar.png',
  canEdit: false,
};

// After
const userMessage = createUserMessage(currentInputValue, userPhotoURL);
```

### Creating Error Messages

```tsx
// Before
const errorMessage: ExtendedMessage = {
  id: Date.now().toString(),
  content: (
    <>
      <p className="text-red-600 font-medium">
        🔗 Oops! That doesn't seem like a valid website link.
      </p>
      <p className="text-gray-600">
        Please enter the website link that starts with{' '}
        <strong>https://</strong> (e.g.,
        <a href="https://dolze.ai" className="text-blue-500 underline">
          https://dolze.ai
        </a>
        ).
      </p>
    </>
  ),
  sender: 'bot',
  timestamp: new Date(),
  avatar: '/images/bot-avatar.png',
  canEdit: false,
};

// After
const errorContent = (
  <>
    <p className="text-red-600 font-medium">
      🔗 Oops! That doesn't seem like a valid website link.
    </p>
    <p className="text-gray-600">
      Please enter the website link that starts with{' '}
      <strong>https://</strong> (e.g.,
      <a href="https://dolze.ai" className="text-blue-500 underline">
        https://dolze.ai
      </a>
      ).
    </p>
  </>
);
const errorMessage = createErrorMessage(errorContent);
```

### Validating URLs

```tsx
// Before
const urlPattern = /^https:\/\/(www\.)?[\w-]+\.[\w-]+(\/[\w- ./?%&=]*)?$/;
if (!urlPattern.test(currentInputValue)) {
  // Handle invalid URL
}

// After
if (!isValidUrl(currentInputValue)) {
  // Handle invalid URL
}
```

### Getting Node Content

```tsx
// Before
const content = typeof nextNode.content === 'function'
  ? nextNode.content(currentInputValue)
  : nextNode.content;

// After
const content = getNodeContent(nextNode, currentInputValue);
```

## Imports to Use

```tsx
// ChatbotUtils
import {
  addMessageWithDelay,
  transformFormData,
  isValidUrl,
  getNodeContent,
} from './ChatbotUtils';

// ChatbotMessageUtils
import {
  createUserMessage,
  createBotMessage,
  createErrorMessage,
} from './ChatbotMessageUtils';

// ChatbotApiUtils (when needed)
import {
  validateIdea,
  getBusinessContext,
  saveBusinessContext,
} from './ChatbotApiUtils';
```

## Next Steps

1. **Update Chatbot.tsx**
   - Replace all message creation with utility functions
   - Use API utility functions for API calls
   - Use validation utility functions for validation

2. **Refactor Large Functions**
   - Break down large functions into smaller, more focused ones
   - Move complex logic to utility files

3. **Add Error Handling**
   - Add try-catch blocks around API calls
   - Use error utility functions for error messages

4. **Remove Debug Code**
   - Remove console.log statements
   - Remove commented-out code
   - Clean up unnecessary blank lines
