{"name": "free-nextjs-admin-dashboard", "version": "2.0.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky install", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "lint-staged": {"**/*.{ts,tsx}": ["pnpm tsc --noEmit", "pnpm eslint --fix", "pnpm jest --bail --findRelatedTests --coverage"], "**/*.{js,jsx}": ["pnpm eslint --fix"], "**/*.{json,md}": ["pnpm prettier --write"]}, "dependencies": {"@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@react-jvectormap/core": "^1.0.4", "@react-jvectormap/world": "^1.1.2", "@tailwindcss/forms": "^0.5.9", "@vercel/speed-insights": "^1.2.0", "apexcharts": "^4.3.0", "autoprefixer": "^10.4.20", "axios": "^1.8.1", "clsx": "^2.1.1", "firebase": "^11.3.1", "flatpickr": "^4.6.13", "lucide-react": "^0.488.0", "next": "15.1.3", "razorpay": "^2.9.6", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-flatpickr": "^3.10.13", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-masonry-css": "^1.0.16", "swiper": "^11.2.0", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-flatpickr": "^3.8.11", "@types/react-transition-group": "^4.4.12", "commitlint": "^19.8.0", "eslint": "^9", "eslint-config-next": "15.1.3", "eslint-config-prettier": "^10.0.1", "eslint-plugin-compat": "^6.0.2", "eslint-plugin-prettier": "^5.2.3", "husky": "^8.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.5.0", "postcss": "^8", "prettier": "^3.5.1", "tailwindcss": "^3.4.1", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "typescript": "^5"}}