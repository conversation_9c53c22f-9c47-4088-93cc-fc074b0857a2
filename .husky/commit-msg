
echo "🔍 Checking commit message format..."

# Check if commitlint is installed
if ! npx --no-install commitlint --version >/dev/null 2>&1; then
  echo "❌ Error: commitlint is not installed."
  echo "Installing commitlint..."
  npm install --save-dev @commitlint/cli @commitlint/config-conventional
  
  # Check if installation was successful
  if ! npx --no-install commitlint --version >/dev/null 2>&1; then
    echo "❌ Failed to install commitlint. Please install it manually:"
    echo "npm install --save-dev @commitlint/cli @commitlint/config-conventional"
    exit 1
  fi
  echo "✅ commitlint installed successfully!"
fi

# Get commit message from file
MSG="$(cat $1)"

# Run commitlint directly without redirecting output
npx --no-install commitlint --edit "$1"
commit_lint_exit_code=$?
# If commitlint failed, show helpful message
if [ $commit_lint_exit_code -ne 0 ]; then
  echo ""
  echo "❌ Commit message format is invalid: '$MSG'"
  echo ""
  echo "✅ Please use one of these formats:"
  echo "   feat: add new feature"
  echo "   fix: resolve bug issue"
  echo "   docs: update documentation"
  echo "   style: format code (no production code change)"
  echo "   refactor: refactor code (no functional change)"
  echo "   perf: improve performance"
  echo "   test: add or update tests"
  echo "   chore: update build tasks, configs, etc."
  echo ""
  echo "📝 Example: feat: add user authentication feature"
  exit 1
fi

echo "✅ Commit message format is valid!"
exit 0
