# Chatbot Component Documentation

This document provides a comprehensive guide on how to add new features to the chatbot component in the Dolze application.

## Table of Contents

1. [Overview](#overview)
2. [Component Structure](#component-structure)
3. [Config-Driven Approach](#config-driven-approach)
4. [Adding New Features](#adding-new-features)
   - [Adding a New Node](#adding-a-new-node)
   - [Adding a New Action](#adding-a-new-action)
   - [Adding a New API Integration](#adding-a-new-api-integration)
   - [Adding a New UI Component](#adding-a-new-ui-component)
5. [Common Patterns](#common-patterns)
6. [Testing](#testing)
7. [Troubleshooting](#troubleshooting)

## Overview

The chatbot component is designed with a config-driven approach, making it highly extensible and customizable. The flow of the conversation is defined by a series of nodes, each representing a step in the conversation. Nodes can be of different types (message, input, validation) and can have different actions (submit, select, edit, proceed).

## Component Structure

The chatbot component consists of several key files:

- `src/components/ui/chatbot/Chatbot.tsx`: The main component that orchestrates the chatbot flow
- `src/components/ui/chatbot/ChatbotConfig.tsx`: Contains the configuration for the chatbot nodes and actions
- `src/components/ui/chatbot/ChatControls.tsx`: Handles the UI controls for the chatbot
- `src/components/ui/chatbot/ChatInput.tsx`: Handles user input
- `src/components/ui/chatbot/ChatMessage.tsx`: Renders individual chat messages
- `src/components/ui/chatbot/types.ts`: Contains TypeScript types for the chatbot
- `src/components/ui/chatbot/ChatbotUtils.ts`: Contains utility functions for the chatbot

## Config-Driven Approach

The chatbot uses a config-driven approach, where the flow of the conversation is defined in the `ChatbotConfig.tsx` file. Each node in the configuration represents a step in the conversation and can have different types, content, and actions.

### Node Types

- `message`: Displays a message to the user
- `input`: Requests input from the user
- `validation`: Performs validation on user input (usually via API call)

### Node Actions

- `submit`: Submits user input
- `select`: Selects an option
- `edit`: Edits previous input
- `proceed`: Proceeds to the next step
- `skip`: Skips the current step

## Adding New Features

### Adding a New Node

To add a new node to the chatbot flow:

1. Open `src/components/ui/chatbot/ChatbotConfig.tsx`
2. Add a new node to the `nodes` object:

```tsx
myNewNode: {
  id: 'myNewNode',
  type: 'message', // or 'input' or 'validation'
  content: 'This is my new node content',
  actions: [
    {
      label: 'Next',
      action: 'proceed',
      nextNodeId: 'nextNodeId',
      variant: 'primary',
    },
  ],
},
```

3. Connect the new node to the existing flow by updating the `nextNodeId` of an existing action or adding a new action to an existing node.

#### Example: Adding a Feedback Collection Node

Let's add a node that collects user feedback after business idea validation:

```tsx
// In ChatbotConfig.tsx
export const chatbotConfig = (firstname: string): ChatbotConfig => ({
  initialNodeId: 'welcome',
  nodes: {
    // ... existing nodes

    // After the validation success node
    validateIdeaSuccess: {
      // ... existing configuration
      actions: [
        {
          label: 'Continue',
          action: 'select',
          nextNodeId: 'feedbackRequest', // Point to our new node
          variant: 'primary',
        },
      ],
    },

    // Our new feedback node
    feedbackRequest: {
      id: 'feedbackRequest',
      type: 'message',
      content: (
        <div className="p-3 bg-gray-50 rounded-lg">
          <h3 className="font-medium text-gray-800 mb-2">
            How was your experience?
          </h3>
          <p className="text-gray-600 mb-3">
            We'd love to hear your thoughts on the business idea validation
            process.
          </p>
        </div>
      ),
      actions: [
        {
          label: 'Very Helpful',
          action: 'select',
          nextNodeId: 'feedbackThankYou',
          variant: 'outline',
        },
        {
          label: 'Somewhat Helpful',
          action: 'select',
          nextNodeId: 'feedbackThankYou',
          variant: 'outline',
        },
        {
          label: 'Not Helpful',
          action: 'select',
          nextNodeId: 'feedbackThankYou',
          variant: 'outline',
        },
        {
          label: 'Skip',
          action: 'select',
          nextNodeId: 'businessContextIntro',
          variant: 'link',
        },
      ],
    },

    // Thank you node after feedback
    feedbackThankYou: {
      id: 'feedbackThankYou',
      type: 'message',
      content: (
        <div className="p-3">
          <p className="text-gray-600">
            Thank you for your feedback! We're constantly working to improve our
            platform.
          </p>
        </div>
      ),
      actions: [
        {
          label: 'Continue',
          action: 'select',
          nextNodeId: 'businessContextIntro',
          variant: 'primary',
        },
      ],
    },

    // ... other nodes
  },
});
```

### Adding a New Action

To add a new action to an existing node:

1. Open `src/components/ui/chatbot/ChatbotConfig.tsx`
2. Find the node you want to add the action to
3. Add a new action to the `actions` array:

```tsx
{
  label: 'My New Action',
  action: 'myNewAction', // This should match a case in the handleAction function
  nextNodeId: 'nextNodeId',
  variant: 'outline', // or 'primary'
  icon: <MyIcon />, // Optional
},
```

4. Open `src/components/ui/chatbot/Chatbot.tsx`
5. Update the `handleAction` function to handle the new action type:

```tsx
const handleAction = (action: string, nextNodeId?: string) => {
  switch (action) {
    // ... existing cases
    case 'myNewAction':
      // Handle the new action
      if (nextNodeId) {
        executeNode(nextNodeId);
      }
      break;
    // ... other cases
  }
};
```

### Adding a New API Integration

To add a new API integration:

1. Create a new API endpoint in `src/app/api/` if needed
2. Open `src/components/ui/chatbot/ChatbotConfig.tsx`
3. Add a new validation node with the API configuration:

```tsx
myNewValidation: {
  id: 'myNewValidation',
  type: 'validation',
  content: 'Processing your request...',
  api: {
    endpoint: '/api/my-new-endpoint',
    method: 'POST',
    payloadKey: 'myData', // The key to use for the payload
    successNodeId: 'successNode',
    failureNodeId: 'failureNode',
  },
},
```

4. Update the `executeNode` function in `src/components/ui/chatbot/Chatbot.tsx` if needed to handle the new API call.

#### Example: Adding a Market Research API Integration

Let's add a complete API integration that performs market research for a business idea:

##### Step 1: Create the API Endpoint

First, create a new API endpoint in `src/app/api/market-research/route.ts`:

```typescript
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { businessIdea } = await req.json();

    if (!businessIdea) {
      return NextResponse.json(
        { success: false, message: 'Business idea is required' },
        { status: 400 }
      );
    }

    // In a real implementation, you would call an external API or service
    // This is a mock implementation for demonstration purposes
    const marketResearch = {
      success: true,
      data: {
        marketSize: '$' + (Math.floor(Math.random() * 900) + 100) + 'B',
        growthRate: Math.floor(Math.random() * 15) + 5 + '%',
        competitorCount: Math.floor(Math.random() * 20) + 3,
        topCompetitors: ['Company A', 'Company B', 'Company C'],
        targetDemographic: 'Adults 25-45',
        entryBarriers: [
          'Initial investment',
          'Regulatory compliance',
          'Brand recognition',
        ],
        opportunities: [
          'Growing market demand',
          'Technological innovation',
          'Underserved customer segments',
        ],
        challenges: [
          'Intense competition',
          'Changing regulations',
          'Customer acquisition costs',
        ],
        summary: `Based on our analysis, the ${businessIdea} market shows promising growth potential with a ${Math.floor(Math.random() * 15) + 5}% annual growth rate. While there are established competitors, there appear to be opportunities for new entrants with innovative approaches.`,
      },
    };

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1500));

    return NextResponse.json(marketResearch);
  } catch (error) {
    console.error('Market research error:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to process market research' },
      { status: 500 }
    );
  }
}
```

##### Step 2: Add Constants

Add the API endpoint to your constants file in `src/constants/index.ts`:

```typescript
// API Endpoints
export const API_MARKET_RESEARCH = '/api/market-research';
```

##### Step 3: Add Nodes to ChatbotConfig

Now, add the necessary nodes to `src/components/ui/chatbot/ChatbotConfig.tsx`:

```typescript
import { API_MARKET_RESEARCH } from '@/constants';

export const chatbotConfig = (firstname: string): ChatbotConfig => ({
  initialNodeId: 'welcome',
  nodes: {
    // ... existing nodes

    // Node that triggers market research
    marketResearchStart: {
      id: 'marketResearchStart',
      type: 'message',
      content: (
        <div className="p-3">
          <p className="text-gray-600">
            Let's analyze the market potential for your business idea.
          </p>
          <div className="mt-3 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-500"></div>
            <span className="ml-2 text-gray-600">Analyzing market data...</span>
          </div>
        </div>
      ),
      api: {
        endpoint: API_MARKET_RESEARCH,
        method: 'POST',
        payloadKey: 'businessIdea',
        successNodeId: 'marketResearchSuccess',
        failureNodeId: 'marketResearchFailure',
      },
    },

    // Success node for market research
    marketResearchSuccess: {
      id: 'marketResearchSuccess',
      type: 'message',
      content: (data) => {
        const research = data?.data || {};

        return (
          <div className="p-3 bg-white rounded-lg border border-gray-200">
            <h3 className="font-semibold text-gray-800 mb-3">Market Research Results</h3>

            <div className="grid grid-cols-2 gap-3 mb-4">
              <div className="bg-gray-50 p-2 rounded">
                <p className="text-xs text-gray-500">Market Size</p>
                <p className="text-lg font-medium text-brand-600">{research.marketSize || 'N/A'}</p>
              </div>

              <div className="bg-gray-50 p-2 rounded">
                <p className="text-xs text-gray-500">Growth Rate</p>
                <p className="text-lg font-medium text-green-600">{research.growthRate || 'N/A'}</p>
              </div>

              <div className="bg-gray-50 p-2 rounded">
                <p className="text-xs text-gray-500">Competitors</p>
                <p className="text-lg font-medium text-gray-700">{research.competitorCount || 'N/A'}</p>
              </div>

              <div className="bg-gray-50 p-2 rounded">
                <p className="text-xs text-gray-500">Target Market</p>
                <p className="text-lg font-medium text-gray-700">{research.targetDemographic || 'N/A'}</p>
              </div>
            </div>

            <div className="mb-3">
              <h4 className="font-medium text-gray-700 mb-1">Summary</h4>
              <p className="text-gray-600 text-sm">{research.summary || 'No summary available.'}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-700 mb-1 text-sm">Opportunities</h4>
                <ul className="text-xs text-gray-600 list-disc pl-4">
                  {research.opportunities?.map((item, index) => (
                    <li key={index}>{item}</li>
                  )) || <li>No data available</li>}
                </ul>
              </div>

              <div>
                <h4 className="font-medium text-gray-700 mb-1 text-sm">Challenges</h4>
                <ul className="text-xs text-gray-600 list-disc pl-4">
                  {research.challenges?.map((item, index) => (
                    <li key={index}>{item}</li>
                  )) || <li>No data available</li>}
                </ul>
              </div>
            </div>
          </div>
        );
      },
      actions: [
        {
          label: 'Continue',
          action: 'select',
          nextNodeId: 'nextStepAfterResearch',
          variant: 'primary',
        },
      ],
    },

    // Failure node for market research
    marketResearchFailure: {
      id: 'marketResearchFailure',
      type: 'message',
      content: (
        <div className="p-3">
          <p className="text-red-500 mb-2">
            We encountered an issue while analyzing the market for your business idea.
          </p>
          <p className="text-gray-600">
            This could be due to a temporary service disruption. Would you like to try again or skip this step?
          </p>
        </div>
      ),
      actions: [
        {
          label: 'Try Again',
          action: 'select',
          nextNodeId: 'marketResearchStart',
          variant: 'outline',
        },
        {
          label: 'Skip',
          action: 'select',
          nextNodeId: 'nextStepAfterResearch',
          variant: 'link',
        },
      ],
    },

    // ... other nodes
  },
});
```

##### Step 4: Connect to the Flow

Finally, connect the market research flow to your existing conversation flow by updating an existing node:

```typescript
// After business idea validation
validateIdeaSuccess: {
  // ... existing configuration
  actions: [
    {
      label: 'Analyze Market Potential',
      action: 'select',
      nextNodeId: 'marketResearchStart',
      variant: 'primary',
    },
    {
      label: 'Skip',
      action: 'select',
      nextNodeId: 'nextStepAfterResearch',
      variant: 'link',
    },
  ],
},
```

This complete example demonstrates how to add a new API integration to the chatbot, including creating the API endpoint, adding the necessary nodes to the configuration, and connecting the flow to the existing conversation.

### Adding a New UI Component

To add a new UI component for the chatbot:

1. Create a new component in `src/components/ui/chatbot/`
2. Import and use the component in `src/components/ui/chatbot/ChatMessage.tsx` or other relevant files
3. Update the `content` property of a node in `ChatbotConfig.tsx` to use the new component:

```tsx
content: (data) => (
  <MyNewComponent data={data} />
),
```

#### Example: Adding a Business Plan Summary Component

Let's create a custom UI component that displays a business plan summary with a progress tracker:

##### Step 1: Create the Component

Create a new file at `src/components/ui/chatbot/components/BusinessPlanSummary.tsx`:

```tsx
import React from 'react';
import { FiCheckCircle, FiCircle, FiArrowRight } from 'react-icons/fi';

interface BusinessPlanSummaryProps {
  businessName: string;
  businessType: string;
  completedSections: string[];
  totalSections: string[];
}

const BusinessPlanSummary: React.FC<BusinessPlanSummaryProps> = ({
  businessName,
  businessType,
  completedSections,
  totalSections,
}) => {
  const progress = Math.round(
    (completedSections.length / totalSections.length) * 100
  );

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="bg-brand-50 p-4 border-b border-gray-200">
        <h3 className="font-semibold text-gray-800">
          {businessName || 'Your Business'} Plan
        </h3>
        <p className="text-sm text-gray-600">{businessType || 'Business'}</p>
      </div>

      {/* Progress Bar */}
      <div className="px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between mb-1">
          <span className="text-xs font-medium text-gray-700">Progress</span>
          <span className="text-xs font-medium text-brand-600">
            {progress}%
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-brand-500 h-2 rounded-full transition-all duration-500 ease-in-out"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      </div>

      {/* Sections */}
      <div className="p-4">
        <h4 className="text-sm font-medium text-gray-700 mb-3">
          Business Plan Sections
        </h4>
        <ul className="space-y-2">
          {totalSections.map((section, index) => {
            const isCompleted = completedSections.includes(section);
            return (
              <li key={index} className="flex items-center">
                {isCompleted ? (
                  <FiCheckCircle className="text-green-500 mr-2 flex-shrink-0" />
                ) : (
                  <FiCircle className="text-gray-400 mr-2 flex-shrink-0" />
                )}
                <span
                  className={`text-sm ${isCompleted ? 'text-gray-800' : 'text-gray-500'}`}
                >
                  {section}
                </span>
              </li>
            );
          })}
        </ul>
      </div>

      {/* Footer */}
      <div className="bg-gray-50 p-3 border-t border-gray-200">
        <button className="w-full flex items-center justify-center py-2 px-4 bg-brand-50 hover:bg-brand-100 text-brand-600 rounded transition-colors duration-200 text-sm font-medium">
          <span>Continue Building Your Plan</span>
          <FiArrowRight className="ml-2" />
        </button>
      </div>
    </div>
  );
};

export default BusinessPlanSummary;
```

##### Step 2: Add the Component to ChatbotConfig

Now, use the component in your chatbot configuration:

```tsx
// In ChatbotConfig.tsx
import BusinessPlanSummary from './components/BusinessPlanSummary';

export const chatbotConfig = (firstname: string): ChatbotConfig => ({
  initialNodeId: 'welcome',
  nodes: {
    // ... existing nodes

    // Business plan summary node
    businessPlanSummary: {
      id: 'businessPlanSummary',
      type: 'message',
      content: (data) => {
        // Extract business data from previous steps
        const businessName = data?.businessName || 'Your Business';
        const businessType = data?.businessType || 'Startup';

        // Track completed sections
        const completedSections = [
          'Business Idea',
          'Market Research',
          'Target Audience',
        ];

        // All sections in the business plan
        const totalSections = [
          'Business Idea',
          'Market Research',
          'Target Audience',
          'Competitive Analysis',
          'Revenue Model',
          'Marketing Strategy',
          'Financial Projections',
          'Implementation Timeline',
        ];

        return (
          <div className="p-2">
            <p className="text-gray-600 mb-4">
              Here's a summary of your business plan so far. You've completed{' '}
              {completedSections.length} out of {totalSections.length} sections.
            </p>

            <BusinessPlanSummary
              businessName={businessName}
              businessType={businessType}
              completedSections={completedSections}
              totalSections={totalSections}
            />
          </div>
        );
      },
      actions: [
        {
          label: 'Continue Building Plan',
          action: 'select',
          nextNodeId: 'competitiveAnalysisIntro',
          variant: 'primary',
        },
        {
          label: 'Save & Exit',
          action: 'select',
          nextNodeId: 'saveAndExit',
          variant: 'outline',
        },
      ],
    },

    // ... other nodes
  },
});
```

##### Step 3: Connect to the Flow

Connect this node to your existing flow:

```typescript
// After completing the target audience section
targetAudienceComplete: {
  // ... existing configuration
  actions: [
    {
      label: 'View Business Plan Summary',
      action: 'select',
      nextNodeId: 'businessPlanSummary',
      variant: 'primary',
    },
  ],
},
```

This example demonstrates how to create a custom UI component for the chatbot that provides a visual representation of the user's progress through the business planning process. The component includes a progress bar, checklist of completed sections, and a call-to-action button.

## Common Patterns

### Conditional Flow

To create a conditional flow based on user input or API response:

```tsx
// In the executeNode function in Chatbot.tsx
if (someCondition) {
  executeNode('nodeForConditionTrue');
} else {
  executeNode('nodeForConditionFalse');
}
```

### Editing Previous Responses

To allow editing of previous responses:

1. Set `canEdit: true` in the message object
2. Use the `handleEdit` function to enable editing mode
3. Use the `handleSaveEdit` function to save the edited content

### API Integration with Validation

To integrate with an API and validate the response:

1. Create a validation node with the API configuration
2. Handle the API response in the `executeNode` function
3. Display the validation result to the user
4. Provide options to edit or proceed

## Testing

When adding new features to the chatbot, it's important to test the following:

1. **Flow Testing**: Test the entire conversation flow to ensure it works as expected
2. **Edge Cases**: Test edge cases such as empty inputs, invalid inputs, and API errors
3. **UI Testing**: Test the UI components to ensure they render correctly
4. **API Testing**: Test the API integrations to ensure they work correctly

## Troubleshooting

### Common Issues

1. **Node not found**: Check that the `nextNodeId` in your action matches an existing node ID in the config
2. **API call failing**: Check the API endpoint, method, and payload in the network tab of your browser's developer tools
3. **UI not updating**: Check that you're properly updating the state and dispatching actions
4. **Content not rendering**: Check that the content function is receiving the expected data

### Debugging Tips

1. Use `console.log` to debug the flow and data
2. Check the browser console for errors
3. Use the React Developer Tools to inspect the component state
4. Use the Network tab in the browser's developer tools to inspect API calls

---

By following this guide, you should be able to add new features to the chatbot component in a structured and maintainable way. The config-driven approach makes it easy to extend the chatbot without having to modify the core components.
