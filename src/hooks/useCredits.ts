'use client';

import { useState, useCallback, useEffect } from 'react';
import { creditService, CreditBalance, CreditFeature } from '@/services/credit';
import { useAuth } from '@/context/AuthContext';

export function useCredits() {
  const { user } = useAuth();
  const [balance, setBalance] = useState<CreditBalance | null>(null);
  const [features, setFeatures] = useState<CreditFeature[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // Fetch credit balance
  const fetchBalance = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);
      const balanceData = await creditService.getBalance();
      if (balanceData) {
        setBalance(balanceData);
      }
    } catch (err) {
      console.error('Error fetching credit balance:', err);
      setError('Failed to fetch credit balance');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Fetch credit features
  const fetchFeatures = useCallback(async () => {
    if (!user) return;

    try {
      const featuresData = await creditService.getFeatures();
      if (featuresData) {
        setFeatures(featuresData);
      }
    } catch (err) {
      console.error('Error fetching credit features:', err);
    }
  }, [user]);

  // Handle API error
  const handleApiError = useCallback(
    (error: any) => {
      const creditInfo = creditService.extractCreditInfoFromError(error);
      if (creditInfo) {
        return true;
      }

      return false;
    },
    [features]
  );

  // Fetch data on mount
  useEffect(() => {
    if (user) {
      fetchBalance();
      fetchFeatures();
    }
  }, [user, fetchBalance, fetchFeatures]);

  return {
    balance,
    features,
    loading,
    error,
    fetchBalance,
    fetchFeatures,
    handleApiError,
  };
}
