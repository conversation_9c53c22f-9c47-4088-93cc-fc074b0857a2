import { BiCopy } from 'react-icons/bi';
import { useState } from 'react';

export default function CopyText({ text }: { text: string }) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <div
      className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 cursor-pointer transition-colors"
      onClick={handleCopy}
    >
      <BiCopy size={16} />
      <span>{copied ? 'Copied!' : 'Copy text'}</span>
    </div>
  );
}
