'use client';

import React from 'react';
import { FiAlertCircle } from 'react-icons/fi';

interface SocialMediaPostsLoaderProps {
  platform: 'instagram' | 'twitter' | 'linkedin' | 'facebook' | 'whatsapp';
}

const platformConfig = {
  instagram: {
    name: 'Instagram',
    color: 'bg-pink-50',
    textColor: 'text-pink-600',
    borderColor: 'border-pink-200',
    icon: '📸',
  },
  twitter: {
    name: 'Twitter',
    color: 'bg-blue-50',
    textColor: 'text-blue-600',
    borderColor: 'border-blue-200',
    icon: '🐦',
  },
  linkedin: {
    name: 'LinkedIn',
    color: 'bg-blue-50',
    textColor: 'text-blue-700',
    borderColor: 'border-blue-200',
    icon: '💼',
  },
  facebook: {
    name: 'Facebook',
    color: 'bg-indigo-50',
    textColor: 'text-indigo-600',
    borderColor: 'border-indigo-200',
    icon: '👥',
  },
  whatsapp: {
    name: 'WhatsApp',
    color: 'bg-green-50',
    textColor: 'text-green-600',
    borderColor: 'border-green-200',
    icon: '💬',
  },
};

const SocialMediaPostsLoader: React.FC<SocialMediaPostsLoaderProps> = ({
  platform,
}) => {
  const config = platformConfig[platform];

  return (
    <div
      className={`col-span-full p-8 rounded-lg ${config.color} border ${config.borderColor} flex flex-col items-center justify-center text-center`}
    >
      <div className="text-4xl mb-4">{config.icon}</div>
      <h3 className={`text-xl font-semibold mb-2 ${config.textColor}`}>
        Generating {config.name} Posts
      </h3>
      <p className="text-gray-600 mb-6 max-w-md">
        We're currently generating engaging content for your {config.name}{' '}
        account. This may take a moment as we craft posts tailored to your
        business.
      </p>
      <p className="text-gray-600 mb-6 max-w-md">
        Alternatively click on generate more posts button above to generate more
        posts
      </p>

      <div className="flex flex-col sm:flex-row gap-4 items-center">
        <div className="flex items-center gap-2 text-gray-500 text-sm">
          <FiAlertCircle />
          <span>
            You can continue using other features while we generate your posts
          </span>
        </div>
      </div>
    </div>
  );
};

export default SocialMediaPostsLoader;
