'use client';

import React from 'react';
import { FiLayout } from 'react-icons/fi';

const LandingPageLoader: React.FC = () => {
  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-8 flex flex-col items-center">
        <div className="mb-8">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-600 mx-auto"></div>
        </div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Generating Your Landing Page
        </h2>
        <p className="text-gray-600 dark:text-gray-300 mb-6 text-center">
          We're currently crafting a beautiful landing page tailored to your
          business. This may take a moment as we design and optimize your page.
        </p>

        <div className="flex items-center gap-2 text-gray-500 text-sm mb-8">
          <span>
            It generally takes 2-3 minutes to generate your landing page after
            project creation. You can wait or click on generate landing page to
            get a new landing page
          </span>
        </div>
      </div>

      <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-6 mb-6 text-left">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
          <FiLayout className="mr-2" />
          How We Generate Your Landing Page:
        </h3>
        <ol className="list-decimal list-inside space-y-2 text-gray-600 dark:text-gray-300">
          <li>We analyze your project details and business objectives</li>
          <li>Our AI generates a custom landing page tailored to your brand</li>
          <li>
            The page includes sections for your value proposition, features, and
            call-to-action
          </li>
          <li>We optimize the layout for both desktop and mobile viewing</li>
          <li>You can edit the content directly in our built-in editor</li>
          <li>All changes are saved to your project</li>
        </ol>
      </div>
    </div>
  );
};

export default LandingPageLoader;
