'use client';

import React from 'react';
import { useGlobalContext } from '@/context/GlobalContext';
import { LOADING_STATE_CONTEXT_GENERATION } from '@/constants';

interface ContextGenerationLoaderProps {
  children: React.ReactNode;
}

const ContextGenerationLoader: React.FC<ContextGenerationLoaderProps> = ({
  children,
}) => {
  const { state } = useGlobalContext();
  const isGeneratingContext =
    state.loadingStates[LOADING_STATE_CONTEXT_GENERATION];

  if (!isGeneratingContext) {
    return <>{children}</>;
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center">
      <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
        <div className="mb-8">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-blue-600 mx-auto"></div>
        </div>
        <h2 className="text-2xl font-bold text-gray-800 mb-4">
          Generating Business Context
        </h2>
        <p className="text-gray-600 mb-6">
          We're analyzing your business idea and generating comprehensive
          context for your project. This may take a moment as we create detailed
          insights for your business.
        </p>
        <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mb-6">
          <div
            className="bg-blue-600 h-2.5 rounded-full animate-pulse"
            style={{ width: '100%' }}
          ></div>
        </div>
        <p className="text-sm text-gray-500">
          You'll be able to access all your project details once the context
          generation is complete.
        </p>
      </div>
    </div>
  );
};

export default ContextGenerationLoader;
