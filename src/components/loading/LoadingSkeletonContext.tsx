'use client';

import React from 'react';

interface LoadingSkeletonContextProps {
  showSidebar?: boolean;
  showHeader?: boolean;
  showContent?: boolean;
  sidebarWidth?: string;
  contentConfig?: {
    showTitle?: boolean;
    showDescription?: boolean;
    showCard?: boolean;
    cardItems?: number;
  };
}

export default function LoadingSkeletonContext({
  showSidebar = true,
  showHeader = true,
  showContent = true,
  sidebarWidth = '290px',
  contentConfig = {
    showTitle: true,
    showDescription: true,
    showCard: true,
    cardItems: 4,
  },
}: LoadingSkeletonContextProps = {}) {
  return (
    <div className="flex min-h-screen">
      {/* Sidebar Skeleton */}
      {showSidebar && (
        <aside className={`fixed mt-16 lg:mt-0 top-0 left-0 w-[${sidebarWidth}] h-screen bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800`}>
          {/* Logo Skeleton */}
          <div className="py-8 px-5">
            <div className="h-8 w-32 bg-gray-200 rounded-lg animate-pulse dark:bg-gray-700"></div>
          </div>
          
          {/* Navigation Items Skeleton */}
          <div className="px-5 space-y-6">
            {/* Menu Section */}
            <div>
              <div className="h-4 w-16 bg-gray-200 rounded animate-pulse dark:bg-gray-700 mb-4"></div>
              <div className="space-y-3">
                {[1, 2].map((item) => (
                  <div key={item} className="h-10 bg-gray-100 rounded-lg animate-pulse dark:bg-gray-800"></div>
                ))}
              </div>
            </div>
            
            {/* Tools Section */}
            <div>
              <div className="h-4 w-16 bg-gray-200 rounded animate-pulse dark:bg-gray-700 mb-4"></div>
              <div className="space-y-3">
                {[1, 2, 3].map((item) => (
                  <div key={item} className="h-10 bg-gray-100 rounded-lg animate-pulse dark:bg-gray-800"></div>
                ))}
              </div>
            </div>
          </div>
        </aside>
      )}

      {/* Main Content */}
      <div className={`flex-1 ${showSidebar ? `ml-[${sidebarWidth}]` : ''}`}>
        {/* Header Skeleton */}
        {showHeader && (
          <header className="fixed top-0 right-0 z-999 flex w-full bg-white drop-shadow-1 dark:bg-gray-900 dark:drop-shadow-none">
            <div className="flex flex-grow items-center justify-between px-4 py-4 shadow-2 md:px-6 2xl:px-11">
              <div className="flex items-center gap-4">
                <div className="h-8 w-8 bg-gray-200 rounded animate-pulse dark:bg-gray-700"></div>
                <div className="h-8 w-48 bg-gray-200 rounded animate-pulse dark:bg-gray-700"></div>
              </div>
              <div className="flex items-center gap-3">
                <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse dark:bg-gray-700"></div>
                <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse dark:bg-gray-700"></div>
                <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse dark:bg-gray-700"></div>
              </div>
            </div>
          </header>
        )}

        {/* Content Area */}
        {showContent && (
          <main className="w-full pt-20 p-4 sm:p-6 xl:p-12">
            {/* Welcome Page Skeleton */}
            <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 flex items-center justify-center transition-all duration-300">
              <div className="text-center max-w-2xl mx-auto px-4">
                {/* Title Skeleton */}
                {contentConfig.showTitle && (
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <div className="h-10 w-10 bg-gray-200 rounded animate-pulse dark:bg-gray-700"></div>
                    <div className="h-10 w-64 bg-gray-200 rounded-lg animate-pulse dark:bg-gray-700"></div>
                  </div>
                )}

                {/* Description Skeleton */}
                {contentConfig.showDescription && (
                  <div className="space-y-2 mb-6">
                    <div className="h-6 w-full bg-gray-200 rounded animate-pulse dark:bg-gray-700"></div>
                    <div className="h-6 w-5/6 mx-auto bg-gray-200 rounded animate-pulse dark:bg-gray-700"></div>
                  </div>
                )}

                {/* Getting Started Card Skeleton */}
                {contentConfig.showCard && (
                  <div className="bg-white dark:bg-gray-800/90 p-8 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700">
                    {/* Card Title Skeleton */}
                    <div className="flex items-center gap-2 mb-4">
                      <div className="h-8 w-8 bg-gray-200 rounded animate-pulse dark:bg-gray-700"></div>
                      <div className="h-8 w-48 bg-gray-200 rounded-lg animate-pulse dark:bg-gray-700"></div>
                    </div>

                    {/* List Items Skeleton */}
                    <div className="space-y-4">
                      {Array.from({ length: contentConfig.cardItems || 4 }).map((_, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div className="h-6 w-6 bg-gray-200 rounded animate-pulse dark:bg-gray-700"></div>
                          <div className="h-6 w-full bg-gray-200 rounded animate-pulse dark:bg-gray-700"></div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </main>
        )}
      </div>
    </div>
  );
}