import React from 'react';
import AppHeader from '@/layout/AppHeader';

const LoadingSkeleton = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <AppHeader />
      {/* Search Bar Skeleton */}
      <div className="p-t-16 w-full bg-gray-50 top-0 z-10 pt-24 px-8">
        <div className="max-w-6xl mx-auto flex align-items justify-center">
          <div className="w-full max-w-2xl h-14 bg-gray-200 rounded-lg animate-pulse" />
        </div>
      </div>

      <div className="flex-1 overflow-y-auto px-8 py-6">
        <div className="max-w-6xl mx-auto">
          <div className="space-y-8">
            {/* Recent Projects Section Skeleton */}
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="h-7 w-40 bg-gray-200 rounded mb-4 animate-pulse" />
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 w-full">
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className="p-4 border rounded-lg bg-white shadow-sm"
                  >
                    <div className="space-y-3">
                      <div className="h-48 bg-gray-200 rounded-lg animate-pulse mb-4" />
                      <div className="flex items-center justify-between">
                        <div className="h-6 w-32 bg-gray-200 rounded animate-pulse" />
                        <div className="h-6 w-20 bg-gray-200 rounded-full animate-pulse" />
                      </div>
                      <div className="h-4 w-full bg-gray-200 rounded animate-pulse" />
                      <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse" />
                      <div className="flex gap-2">
                        {[1, 2, 3].map((j) => (
                          <div
                            key={j}
                            className="h-6 w-16 bg-gray-200 rounded-full animate-pulse"
                          />
                        ))}
                      </div>
                      <div className="flex items-center justify-between mt-4">
                        <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
                        <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Sample Projects Section Skeleton */}
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="h-7 w-40 bg-gray-200 rounded mb-4 animate-pulse" />
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 w-full">
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className="p-4 border rounded-lg bg-white shadow-sm"
                  >
                    <div className="space-y-3">
                      <div className="h-48 bg-gray-200 rounded-lg animate-pulse mb-4" />
                      <div className="flex items-center justify-between">
                        <div className="h-6 w-32 bg-gray-200 rounded animate-pulse" />
                        <div className="h-6 w-20 bg-gray-200 rounded-full animate-pulse" />
                      </div>
                      <div className="h-4 w-full bg-gray-200 rounded animate-pulse" />
                      <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse" />
                      <div className="flex gap-2">
                        {[1, 2, 3].map((j) => (
                          <div
                            key={j}
                            className="h-6 w-16 bg-gray-200 rounded-full animate-pulse"
                          />
                        ))}
                      </div>
                      <div className="flex items-center justify-between mt-4">
                        <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
                        <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoadingSkeleton;
