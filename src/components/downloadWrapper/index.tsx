import { BiDownload } from 'react-icons/bi';

export default function DownloadWrapper(prop: { media_url: string }) {
  const media_url = prop.media_url;

  return (
    <div
      className="absolute top-2 right-2 bg-black bg-opacity-60 rounded-full p-2 opacity-0 group-hover:opacity-100 hover:bg-opacity-80 transition-all duration-200 cursor-pointer z-20"
      onClick={async (e) => {
        e.stopPropagation();
        const proxyUrl = `/api/download?url=${encodeURIComponent(media_url)}`;
        const link = document.createElement('a');
        link.href = proxyUrl;
        link.download = media_url.split('/').pop() || 'image-download';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }}
    >
      <BiDownload className="text-white" size={20} />
    </div>
  );
}
