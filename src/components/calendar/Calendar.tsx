'use client';
import React, { useState, useRef, useMemo } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import {
  EventInput,
  DateSelectArg,
  EventClickArg,
  EventContentArg,
} from '@fullcalendar/core';
import { useRouter } from 'next/navigation';
import { useModal } from '@/hooks/useModal';
import { Modal } from '@/components/ui/modal';
import {
  API_ENDPOINT_CREATE_CAMPAIGN,
  API_ENDPOINT_UPDATE_CAMPAIGN,
} from '@/constants';
import {
  FaTwitter,
  FaInstagram,
  FaLinkedin,
  FaFacebook,
  FaWhatsapp,
} from 'react-icons/fa';
import Button from '../ui/button/Button';
import { apiService } from '@/services/api';
import { useSearchParams } from 'next/navigation';
import { useToast } from '@/context/ToastContext';
import { useGlobalContext } from '@/context/GlobalContext';
import * as ActionTypes from '@/constants/actions/actions.constants';

export interface CampaignResponse {
  id: string;
  name: string;
  user_input: string;
  description: string | null;
  goals: string | null;
  number_of_days: number;
  posts_per_day: number;
  platforms: string[];
  post_types: string[];
  status: string;
  created_at: string;
  updated_at: string;
  schedule: {
    start_date?: string;
    end_date?: string;
  } | null;
  // These properties are added for FullCalendar compatibility
  start?: string;
  end?: string;
  title?: string;
  allDay?: boolean;
}

interface CalendarEvent extends EventInput {
  id: string;
  name: string;
  user_input: string;
  platforms: string[];
  status?: string;
  post_types?: string[];
  number_of_days?: number;
  posts_per_day?: number;
  created_at?: string;
  updated_at?: string;
  schedule?: any;
  extendedProps: {
    calendar: string;
    platforms?: string[];
    user_input?: string;
    schedule?: any;
    status?: string;
  };
}

const Calendar: React.FC = () => {
  const { state, dispatch } = useGlobalContext();
  const router = useRouter();

  // Memoize campaigns array to prevent unnecessary re-renders
  const campaigns = useMemo(() => {
    return Array.isArray(state.projectDetails?.data?.campaigns)
      ? state.projectDetails.data.campaigns
      : [];
  }, [state.projectDetails?.data?.campaigns]);
  // Map campaigns to calendar events with proper date handling from schedule
  const calendarEvents = useMemo(() => {
    return campaigns.map((campaign) => ({
      ...campaign,
      // Use schedule dates if available, otherwise fallback to start/end properties
      start: campaign.schedule?.start_date || campaign.start,
      end: campaign.schedule?.end_date || campaign.end,
      title: campaign.name,
      extendedProps: {
        calendar: ['danger', 'primary', 'success', 'warning'][
          Math.floor(Math.random() * 4)
        ],
        // Pass through all campaign properties to make them accessible in event rendering
        platforms: campaign.platforms,
        user_input: campaign.user_input,
        schedule: campaign.schedule,
        status: campaign.status,
      },
      allDay: true,
    }));
  }, [campaigns]);
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(
    null
  );
  const [eventTitle, setEventTitle] = useState('');
  const [eventDescription, setEventDescription] = useState('');
  const [eventStartDate, setEventStartDate] = useState('');
  const [eventEndDate, setEventEndDate] = useState('');
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [validationError, setValidationError] = useState<string>('');
  const calendarRef = useRef<FullCalendar>(null);
  const { isOpen, openModal, closeModal } = useModal();
  const projectId = useSearchParams().get('id') || '';
  const { showToast } = useToast();
  const handleDateSelect = (selectInfo: DateSelectArg) => {
    resetModalFields();
    setEventStartDate(selectInfo.startStr);
    setEventEndDate(selectInfo.endStr || selectInfo.startStr);
    openModal();
  };

  const handleEventClick = (clickInfo: EventClickArg) => {
    const event = clickInfo.event;
    const campaignId = event.id;

    // Navigate to the campaign detail page
    router.push(`/campaigns/${campaignId}?id=${projectId}`);
  };

  const handleAddOrUpdateEvent = async () => {
    if (!eventTitle || !eventStartDate || !eventEndDate || !eventDescription) {
      setValidationError('Please fill in all required fields');
      return;
    }

    if (selectedPlatforms.length === 0) {
      setValidationError('Please select at least one platform');
      return;
    }

    setValidationError('');

    if (selectedEvent) {
      try {
        // Update existing event via API
        const updatedSchedule = {
          ...selectedEvent.schedule,
          start_date: eventStartDate,
          end_date: eventEndDate,
        };

        const { success } = await apiService.put(API_ENDPOINT_UPDATE_CAMPAIGN, {
          id: projectId,
          campaign_id: selectedEvent.id,
          name: eventTitle,
          user_input: eventDescription,
          platforms: selectedPlatforms,
          schedule: updatedSchedule,
          start: eventStartDate,
          end: eventEndDate,
        });

        if (success) {
          showToast('Campaign updated successfully', 'success');

          // Update the campaign in the local state
          const updatedCampaigns = campaigns.map((event: CampaignResponse) =>
            event.id === selectedEvent.id
              ? {
                  ...event,
                  name: eventTitle,
                  user_input: eventDescription,
                  platforms: selectedPlatforms,
                  schedule: updatedSchedule,
                  start: eventStartDate,
                  end: eventEndDate,
                  title: eventTitle,
                  extendedProps: {
                    calendar: 'primary',
                  },
                }
              : event
          );

          // Update global state
          dispatch({
            type: ActionTypes.FETCH_CAMPAIGN_DATA_SUCCESS,
            payload: { campaigns: updatedCampaigns },
          });
        } else {
          setValidationError('Failed to update campaign. Please try again.');
          return;
        }
      } catch (error) {
        console.error('Error updating campaign:', error);
        setValidationError('Failed to update campaign. Please try again.');
        return;
      }
    } else {
      try {
        // Add new event via API
        const { data, success, message } =
          await apiService.post<CampaignResponse>(
            API_ENDPOINT_CREATE_CAMPAIGN,
            {
              id: projectId,
              name: eventTitle,
              user_input: eventDescription,
              platforms: selectedPlatforms,
              business_idea_id: projectId,
              start: eventStartDate,
              end: eventEndDate,
            }
          );
        if (success) {
          showToast('Campaign created successfully', 'success');
        } else {
          setValidationError(
            message || 'Failed to create campaign. Please try again.'
          );
          return;
        }
        // Create new event with API response format
        const newEvent: CalendarEvent = {
          id: data.id,
          name: data.name,
          user_input: data.user_input,
          platforms: data.platforms,
          status: data.status,
          post_types: data.post_types,
          number_of_days: data.number_of_days,
          posts_per_day: data.posts_per_day,
          created_at: data.created_at,
          updated_at: data.updated_at,
          schedule: data.schedule,
          title: data.name, // Set title for FullCalendar display
          // Use schedule dates if available, otherwise use the form dates
          start: data.schedule?.start_date || eventStartDate,
          end: data.schedule?.end_date || eventEndDate,
          allDay: true,
          extendedProps: {
            calendar: 'primary',
          },
        };

        // Update global state with the new campaign
        dispatch({
          type: ActionTypes.FETCH_CAMPAIGN_DATA_SUCCESS,
          payload: {
            campaigns: [...campaigns, newEvent] as CampaignResponse[],
          },
        });
      } catch (error) {
        console.error('Error creating campaign:', error);
        setValidationError('Failed to create campaign. Please try again.');
        return;
      }
    }
    closeModal();
    resetModalFields();
  };

  const resetModalFields = () => {
    setEventTitle('');
    setEventDescription('');
    setEventStartDate('');
    setEventEndDate('');
    setSelectedPlatforms([]);
    setSelectedEvent(null);
    setValidationError('');
  };

  const togglePlatform = (platform: string) => {
    setSelectedPlatforms((prev) =>
      prev.includes(platform)
        ? prev.filter((p) => p !== platform)
        : [...prev, platform]
    );
  };

  return (
    <div className="rounded-2xl mb-10 bg-white dark:border-gray-800 dark:bg-white/[0.03]">
      <div className="custom-calendar h-fit">
        <FullCalendar
          ref={calendarRef}
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
          initialView="dayGridMonth"
          height="auto" // Changed from "100%" to "auto" to allow natural height
          fixedWeekCount={false} // Ensures only the current month's weeks are shown
          headerToolbar={{
            left: 'addEventButton',
            center: 'title',
          }}
          events={calendarEvents}
          selectable={true}
          select={handleDateSelect}
          eventClick={handleEventClick}
          eventContent={renderEventContent}
          customButtons={{
            addEventButton: {
              text: 'Create Campaign',
              click: openModal,
            },
          }}
          dayMaxEvents={3}
          selectAllow={(selectInfo) => {
            const today = new Date();
            today.setHours(0, 0, 0, 0); // Reset time to start of the day
            return selectInfo.start >= today;
          }}
        />
      </div>

      <Modal
        isOpen={isOpen}
        onClose={closeModal}
        className="max-w-[700px] p-6 lg:p-10"
      >
        <div className="flex flex-col px-2 overflow-y-auto custom-scrollbar">
          <div>
            <h5 className="mb-2 font-semibold text-gray-800 modal-title text-theme-xl dark:text-white/90 lg:text-2xl">
              {selectedEvent ? 'Edit Campaign' : 'Add Campaign'}
            </h5>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Plan your next big moment: schedule or edit a campaign to stay on
              track
            </p>
          </div>
          <div className="mt-8">
            <div>
              <div>
                <label className="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400">
                  Campaign Title <span className="text-red-500">*</span>
                </label>
                <input
                  id="event-title"
                  type="text"
                  value={eventTitle}
                  onChange={(e) => setEventTitle(e.target.value)}
                  placeholder="Enter campaign title"
                  className="dark:bg-dark-900 h-11 w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800"
                  required
                />
              </div>
            </div>
            <div className="mt-6">
              <div>
                <label className="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400">
                  Description <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="event-description"
                  value={eventDescription}
                  onChange={(e) => setEventDescription(e.target.value)}
                  placeholder="Enter campaign description"
                  rows={3}
                  className="min-h-20 w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800"
                />
              </div>
            </div>
            <div className="mt-6">
              <label className="mb-3 block text-sm font-medium text-gray-700 dark:text-gray-400">
                Platforms <span className="text-red-500">*</span>
              </label>
              <div className="flex flex-wrap gap-4">
                <div
                  onClick={() => togglePlatform('twitter')}
                  className={`flex cursor-pointer items-center gap-2 rounded-lg border p-3 transition-all ${selectedPlatforms.includes('twitter') ? 'border-blue-500 bg-blue-50 text-blue-600 dark:border-blue-700 dark:bg-blue-900/30 dark:text-blue-400' : 'border-gray-300 dark:border-gray-700'}`}
                >
                  <input
                    type="checkbox"
                    checked={selectedPlatforms.includes('twitter')}
                    onChange={() => {}}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:focus:ring-blue-600"
                  />
                  <div className="flex items-center gap-2">
                    <FaTwitter className="text-[#1DA1F2]" />
                    <span>Twitter</span>
                  </div>
                </div>
                <div
                  onClick={() => togglePlatform('instagram')}
                  className={`flex cursor-pointer items-center gap-2 rounded-lg border p-3 transition-all ${selectedPlatforms.includes('instagram') ? 'border-purple-500 bg-purple-50 text-purple-600 dark:border-purple-700 dark:bg-purple-900/30 dark:text-purple-400' : 'border-gray-300 dark:border-gray-700'}`}
                >
                  <input
                    type="checkbox"
                    checked={selectedPlatforms.includes('instagram')}
                    onChange={() => {}}
                    className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500 dark:border-gray-600 dark:focus:ring-purple-600"
                  />
                  <div className="flex items-center gap-2">
                    <FaInstagram className="text-[#E1306C]" />
                    <span>Instagram</span>
                  </div>
                </div>
                <div
                  onClick={() => togglePlatform('linkedin')}
                  className={`flex cursor-pointer items-center gap-2 rounded-lg border p-3 transition-all ${selectedPlatforms.includes('linkedin') ? 'border-blue-700 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-900/30 dark:text-blue-400' : 'border-gray-300 dark:border-gray-700'}`}
                >
                  <input
                    type="checkbox"
                    checked={selectedPlatforms.includes('linkedin')}
                    onChange={() => {}}
                    className="h-4 w-4 rounded border-gray-300 text-blue-700 focus:ring-blue-700 dark:border-gray-600 dark:focus:ring-blue-700"
                  />
                  <div className="flex items-center gap-2">
                    <FaLinkedin className="text-[#0077B5]" />
                    <span>LinkedIn</span>
                  </div>
                </div>
                <div
                  onClick={() => togglePlatform('facebook')}
                  className={`flex cursor-pointer items-center gap-2 rounded-lg border p-3 transition-all ${selectedPlatforms.includes('facebook') ? 'border-blue-600 bg-blue-50 text-blue-600 dark:border-blue-700 dark:bg-blue-900/30 dark:text-blue-400' : 'border-gray-300 dark:border-gray-700'}`}
                >
                  <input
                    type="checkbox"
                    checked={selectedPlatforms.includes('facebook')}
                    onChange={() => {}}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600 dark:border-gray-600 dark:focus:ring-blue-600"
                  />
                  <div className="flex items-center gap-2">
                    <FaFacebook className="text-[#1877F2]" />
                    <span>Facebook</span>
                  </div>
                </div>
                <div
                  onClick={() => togglePlatform('whatsapp')}
                  className={`flex cursor-pointer items-center gap-2 rounded-lg border p-3 transition-all ${selectedPlatforms.includes('whatsapp') ? 'border-green-600 bg-green-50 text-green-600 dark:border-green-700 dark:bg-green-900/30 dark:text-green-400' : 'border-gray-300 dark:border-gray-700'}`}
                >
                  <input
                    type="checkbox"
                    checked={selectedPlatforms.includes('whatsapp')}
                    onChange={() => {}}
                    className="h-4 w-4 rounded border-gray-300 text-green-600 focus:ring-green-600 dark:border-gray-600 dark:focus:ring-green-600"
                  />
                  <div className="flex items-center gap-2">
                    <FaWhatsapp className="text-[#25D366]" />
                    <span>WhatsApp</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 flex items-between justify-around">
              <div className="w-1/2 p-2 pl-0">
                <label className="mb-1.5 block text-sm font-medium text-gray-700">
                  Enter Start Date <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    id="event-start-date"
                    type="date"
                    value={eventStartDate}
                    onChange={(e) => setEventStartDate(e.target.value)}
                    className="h-11 w-full appearance-none rounded-lg border border-gray-300 bg-transparent bg-none px-4 py-2.5 pl-4 pr-11 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800"
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>
              </div>
              <div className="w-1/2 p-2">
                <div>
                  <label className="mb-1.5 block text-sm font-medium text-gray-700">
                    Enter End Date <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      id="event-end-date"
                      type="date"
                      value={eventEndDate}
                      onChange={(e) => setEventEndDate(e.target.value)}
                      className="h-11 w-full appearance-none rounded-lg border border-gray-300 bg-transparent bg-none px-4 py-2.5 pl-4 pr-11 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:border-brand-300 focus:outline-none focus:ring focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800"
                      min={eventStartDate}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          {validationError && (
            <div className="mt-4 p-3 text-sm font-medium text-red-700 bg-red-100 rounded-lg border border-red-200 dark:text-red-400 dark:bg-red-900/30 dark:border-red-800">
              {validationError}
            </div>
          )}
          <div className="flex items-center gap-3 mt-6 modal-footer sm:justify-end">
            <Button onClick={closeModal} type="button" variant="outline">
              Close
            </Button>
            <Button
              variant="primary"
              onClick={handleAddOrUpdateEvent}
              type="button"
            >
              {selectedEvent ? 'Update Changes' : 'Add Event'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

const renderEventContent = (eventInfo: EventContentArg) => {
  const colorClass = `fc-bg-${eventInfo.event.extendedProps.calendar.toLowerCase()}`;

  // Extract platforms from the event properties
  let platforms: string[] = [];
  if (eventInfo.event.extendedProps?.platforms) {
    platforms = eventInfo.event.extendedProps.platforms;
  }

  return (
    <div
      className={`event-fc-color flex flex-col fc-event-main ${colorClass} p-1.5 rounded shadow-sm`}
    >
      <div className="flex items-center gap-1">
        <div className="fc-daygrid-event-dot"></div>
        {eventInfo.timeText && (
          <div className="fc-event-time text-xs">{eventInfo.timeText}</div>
        )}
        <div className="fc-event-title font-medium truncate">
          {eventInfo.event.title}
        </div>
      </div>
      {platforms && platforms.length > 0 && (
        <div className="flex mt-1 gap-1 items-center">
          {platforms.includes('twitter') && (
            <div className="bg-white p-0.5 rounded-full">
              <FaTwitter className="text-[#1DA1F2] text-xs" />
            </div>
          )}
          {platforms.includes('instagram') && (
            <div className="bg-white p-0.5 rounded-full">
              <FaInstagram className="text-[#E1306C] text-xs" />
            </div>
          )}
          {platforms.includes('linkedin') && (
            <div className="bg-white p-0.5 rounded-full">
              <FaLinkedin className="text-[#0077B5] text-xs" />
            </div>
          )}
          {platforms.includes('facebook') && (
            <div className="bg-white p-0.5 rounded-full">
              <FaFacebook className="text-[#1877F2] text-xs" />
            </div>
          )}
          {platforms.includes('whatsapp') && (
            <div className="bg-white p-0.5 rounded-full">
              <FaWhatsapp className="text-[#25D366] text-xs" />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Calendar;
