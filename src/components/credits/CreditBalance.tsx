'use client';

import React from 'react';
import { useCredit } from '@/context/CreditContext';
import ThreeDotLoader from '../loading/ThreeDotLoader';
import LowCreditIndicator from './LowCreditIndicator';

interface CreditBalanceProps {
  showDetails?: boolean;
  className?: string;
  showAsTag?: boolean;
}

const CreditBalance: React.FC<CreditBalanceProps> = ({
  showDetails = false,
  className = '',
  showAsTag = false,
}) => {
  const { balance, loading, error } = useCredit();

  if (loading) {
    return (
      <div className={`flex items-center ${className}`}>
        <ThreeDotLoader />
        <span>Loading credits...</span>
      </div>
    );
  }

  if (error || !balance) {
    return (
      <div className={`text-red-500 ${className}`}>
        {error || 'No credit data available'}
      </div>
    );
  }

  // Format the credit balance
  const formattedBalance = showAsTag
    ? balance.balance.toString() // No commas for tag display
    : new Intl.NumberFormat().format(balance.balance); // With commas for normal display

  if (showAsTag) {
    return (
      <div className={`flex items-center px-3 py-1.5 bg-indigo-50 text-indigo-800 rounded-full text-sm font-medium ${className}`}>
        <span className="font-bold text-indigo-900">{formattedBalance}</span>
        <span className="ml-1">Credits Remaining</span>
        <LowCreditIndicator />
      </div>
    );
  }

  if (!showDetails) {
    return (
      <div className={`flex items-center ${className}`}>
        <span className="font-medium">{formattedBalance}</span>
        <span className="ml-1 text-gray-500">credits</span>
        <LowCreditIndicator />
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      <div className="bg-white rounded-lg shadow p-4">
        <h3 className="text-lg font-semibold mb-2">Credit Balance</h3>
        <div className="text-3xl font-bold text-indigo-600 mb-4 flex items-center">
          {formattedBalance}{' '}
          <span className="text-sm font-normal text-gray-500">credits</span>
          <LowCreditIndicator />
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-500">Total Purchased</p>
            <p className="font-medium">
              {new Intl.NumberFormat().format(balance.total_purchased)}
            </p>
          </div>
          <div>
            <p className="text-gray-500">Total Used</p>
            <p className="font-medium">
              {new Intl.NumberFormat().format(balance.total_used)}
            </p>
          </div>
        </div>

        {balance.last_transaction && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <h4 className="text-sm font-medium mb-2">Last Transaction</h4>
            <div className="text-xs text-gray-600">
              <p className="flex justify-between">
                <span>Type:</span>
                <span className="font-medium capitalize">
                  {['purchase', 'bonus'].includes(balance.last_transaction.transaction_type) ? 'Purchase' : 'Usage'}
                </span>
              </p>
              <p className="flex justify-between">
                <span>Amount:</span>
                <span
                  className={`font-medium ${balance.last_transaction.amount > 0 ? 'text-green-600' : 'text-red-600'}`}
                >
                  {balance.last_transaction.amount > 0 ? '+' : ''}
                  {balance.last_transaction.amount}
                </span>
              </p>
              <p className="flex justify-between">
                <span>Date:</span>
                <span className="font-medium">
                  {new Date(
                    balance.last_transaction.created_at
                  ).toLocaleDateString()}
                </span>
              </p>
              <p
                className="text-xs mt-1 text-gray-500 truncate"
                title={balance.last_transaction.description}
              >
                {balance.last_transaction.description}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CreditBalance;
