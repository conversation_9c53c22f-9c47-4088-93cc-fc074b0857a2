'use client';

import React, { useState, useEffect, useRef } from 'react';
import { CreditPlan } from '@/services/credit';

interface PurchaseButtonProps {
  plan: CreditPlan;
  index: number;
  onPurchase: (plan: CreditPlan, setProcessing: (isProcessing: boolean) => void) => void;
}

const PurchaseButton: React.FC<PurchaseButtonProps> = ({ plan, index, onPurchase }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const isMounted = useRef(true);
  
  // Ensure we don't update state after unmounting
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  const handleClick = () => {
    setIsProcessing(true);
    onPurchase(plan, (processing) => {
      if (isMounted.current) {
        setIsProcessing(processing);
      }
    });
  };

  return (
    <button
      className={`w-full ${
        index === 1
          ? 'bg-indigo-600 text-white hover:bg-indigo-700 py-3.5'
          : 'bg-white border-2 border-indigo-600 text-gray-800 hover:bg-indigo-600 hover:text-white py-3'
      } transition-colors duration-300 rounded-lg px-4 font-medium whitespace-nowrap ${
        index === 1 ? 'shadow-lg shadow-indigo-200' : ''
      }`}
      onClick={handleClick}
      disabled={isProcessing}
    >
      {isProcessing ? (
        <span className="flex items-center justify-center">
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          Processing...
        </span>
      ) : (
        index === 1 ? 'Get Pro Access' : 'Buy Credits'
      )}
    </button>
  );
};

export default PurchaseButton;
