'use client';

import React from 'react';
import { FiAlertTriangle } from 'react-icons/fi';
import Tooltip from '@/components/ui/tooltip/Tooltip';
import { useCredit } from '@/context/CreditContext';

const LowCreditIndicator: React.FC = () => {
  const { balance, isLowBalance } = useCredit();

  if (!balance || !isLowBalance()) {
    return null;
  }

  const tooltipContent = (
    <div className="w-64">
      <h3 className="font-medium text-amber-800 mb-1">Low Credit Balance</h3>
      <p className="text-amber-700 mb-2">
        You have <span className="font-semibold">{balance.balance}</span>{' '}
        credits remaining.
        {balance.balance === 0 ? (
          <span className="block mt-1 text-red-600 font-medium">
            You need to purchase more credits to continue using premium
            features.
          </span>
        ) : (
          <span> Consider purchasing more credits to avoid interruptions.</span>
        )}
      </p>
    </div>
  );

  return (
    <Tooltip content={tooltipContent} position="bottom">
      <FiAlertTriangle
        className="ml-1.5 h-4 w-4 text-amber-500 flex-shrink-0"
        aria-hidden="true"
      />
    </Tooltip>
  );
};

export default LowCreditIndicator;
