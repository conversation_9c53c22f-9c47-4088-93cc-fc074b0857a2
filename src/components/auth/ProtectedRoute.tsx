'use client';
import { useAuth } from '@/context/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, Suspense } from 'react';
import LoadingSpinner from '@/components/loading/LoadingSpinner';

export function withAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>
) {
  return function WithAuthComponent(props: P) {
    return (
      <Suspense fallback={<LoadingSpinner />}>
        <AuthenticatedComponent {...props} />
      </Suspense>
    );
  };

  function AuthenticatedComponent(props: P) {
    const { user, loading } = useAuth();
    const router = useRouter();
    const searchParams = useSearchParams();

    useEffect(() => {
      if (!loading) {
        const currentPath = window.location.pathname;

        if (!user && currentPath !== '/signin' && currentPath !== '/signup') {
          router.push('/signin');
          return;
        }

        if (user && (currentPath === '/signin' || currentPath === '/signup')) {
          router.push('/');
          return;
        }
        const id = searchParams?.get('id');
        const isSocialCallback = currentPath.match(
          /\/socials\/(instagram|twitter|linkedin|facebook|whatsapp)\/callback/
        );
        if (!id && !isSocialCallback) {
          router.push('/');
          return;
        }
      }
    }, [user, loading, router, searchParams]);

    if (loading) {
      return <LoadingSpinner />;
    }

    if (!user) {
      return null;
    }

    return <WrappedComponent {...props} />;
  }
}
