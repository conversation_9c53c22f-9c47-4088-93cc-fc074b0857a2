import React, { useState, useEffect } from 'react';
import { MockPost } from '@/types/api';
import Button from '../ui/button/Button';
import { BiX, BiRefresh } from 'react-icons/bi';
import { useToast } from '@/context/ToastContext';

interface EditPostModalProps {
  post: MockPost | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedPost: MockPost) => Promise<void>;
}

const EditPostModal: React.FC<EditPostModalProps> = ({
  post,
  isOpen,
  onClose,
  onSave,
}) => {
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { showToast } = useToast();

  useEffect(() => {
    if (isOpen && post) {
      setContent(post.content_text || '');
    }
  }, [isOpen, post]);

  const handleSave = async () => {
    if (!content.trim()) {
      showToast('Post content cannot be empty', 'error');
      return;
    }

    setIsSubmitting(true);
    try {
      if (!post) {
        throw new Error('No post to update');
      }
      const updatedPost = { ...post, content_text: content };
      await onSave(updatedPost);
      showToast('Post updated successfully', 'success');
      onClose();
    } catch (error) {
      console.error('Error updating post:', error);
      showToast('Failed to update post', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRegenerateImage = () => {
    showToast('Image regeneration coming soon', 'info');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-xl mx-4 overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Edit Post
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <BiX size={24} />
          </button>
        </div>

        <div className="p-4">
          <div className="mb-4">
            <label
              htmlFor="post-content"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Post Content
            </label>
            <textarea
              id="post-content"
              rows={5}
              className="w-full h-64 px-3 py-2 text-gray-700 dark:text-gray-300 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 dark:border-gray-600"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="Edit your post content..."
            />
          </div>

          {post?.media_url && (
            <div className="mb-4">
              <button
                onClick={handleRegenerateImage}
                className="flex items-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
              >
                <BiRefresh className="mr-1" /> Regenerate Image
              </button>
            </div>
          )}
        </div>

        <div className="flex justify-end p-4 border-t border-gray-200 dark:border-gray-700">
          <Button
            onClick={onClose}
            variant="outline"
            className="mr-2"
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSubmitting}
            isLoading={isSubmitting}
          >
            Save Changes
          </Button>
        </div>
      </div>
    </div>
  );
};

export default EditPostModal;
