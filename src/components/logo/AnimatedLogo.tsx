'use client';

import React from 'react';
import { useTheme } from '@/context/ThemeContext';

interface AnimatedLogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const AnimatedLogo: React.FC<AnimatedLogoProps> = ({
  className = '',
  size = 'md'
}) => {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  // Size mapping - increased icon size by another 20%
  const sizeMap = {
    sm: { icon: 28, text: 'text-xl' },
    md: { icon: 36, text: 'text-2xl' },
    lg: { icon: 44, text: 'text-3xl' },
  };

  const iconSize = sizeMap[size].icon;
  const textSize = sizeMap[size].text;

  return (
    <div className={`flex items-center ${className}`} style={{ gap: '0.175rem' }}>
      {/* Modern Animated Rocket Icon */}
      <div className="relative">
        <div className="rocket-container animate-float">
          {/* Rocket Body */}
          <div className="rocket-body">
            {/* Rocket Main Body */}
            <div className="rocket-main" style={{ backgroundColor: isDark ? '#0061ff' : '#0061ff' }}></div>

            {/* Rocket Window */}
            <div className="rocket-window"></div>

            {/* Rocket Fins */}
            <div className="rocket-fin rocket-fin-left" style={{ backgroundColor: isDark ? '#0061ff' : '#0061ff' }}></div>
            <div className="rocket-fin rocket-fin-right" style={{ backgroundColor: isDark ? '#0061ff' : '#0061ff' }}></div>
          </div>

          {/* Rocket exhaust/flames */}
          <div className="rocket-flames">
            <div className="flame flame-main"></div>
            <div className="flame flame-middle"></div>
            <div className="flame flame-left"></div>
            <div className="flame flame-right"></div>
          </div>
        </div>


      </div>

      {/* Text "dolze" in Apercu Pro with fallback - bolder and more prominent */}
      <span
        className={`font-extrabold tracking-tight ${textSize} ${isDark ? 'text-white' : 'text-gray-900'}`}
        style={{
          fontFamily: "'Apercu Pro', sans-serif",
          letterSpacing: '-0.02em',
          marginLeft: '2px'
        }}
      >
        dolze
      </span>

      {/* Add animation keyframes */}
      <style jsx global>{`
        /* Rocket container and float animation */
        .rocket-container {
          position: relative;
          width: ${iconSize}px;
          height: ${iconSize}px;
          transform-origin: center center;
        }

        @keyframes float {
          0% { transform: rotate(45deg) translateY(0); }
          25% { transform: rotate(44deg) translateY(-3px); }
          50% { transform: rotate(45deg) translateY(-5px); }
          75% { transform: rotate(46deg) translateY(-3px); }
          100% { transform: rotate(45deg) translateY(0); }
        }

        .animate-float {
          animation: float 2.5s ease-in-out infinite;
        }

        /* Rocket body styling */
        .rocket-body {
          position: relative;
          width: 100%;
          height: 100%;
        }

        .rocket-main {
          position: absolute;
          top: 15%;
          left: 50%;
          transform: translateX(-50%);
          width: 40%;
          height: 60%;
          background-color: #FE8C00;
          border-radius: 50% 50% 15% 15% / 60% 60% 40% 40%;
          z-index: 2;
        }

        .rocket-window {
          position: absolute;
          top: 30%;
          left: 50%;
          transform: translateX(-50%);
          width: 20%;
          height: 20%;
          background-color: #FFFFFF;
          border-radius: 50%;
          z-index: 3;
          box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.2);
        }

        .rocket-fin {
          position: absolute;
          bottom: 30%;
          width: 15%;
          height: 25%;
          background-color: #FE8C00;
          z-index: 1;
        }

        .rocket-fin-left {
          left: 25%;
          transform: skew(15deg, 15deg);
          border-radius: 50% 10% 10% 50% / 20% 20% 20% 20%;
        }

        .rocket-fin-right {
          right: 25%;
          transform: skew(-15deg, -15deg);
          border-radius: 10% 50% 50% 10% / 20% 20% 20% 20%;
        }

        /* Rocket flames */
        .rocket-flames {
          position: absolute;
          bottom: 10%;
          left: 50%;
          transform: translateX(-50%);
          width: 40%;
          height: 40%;
          z-index: 1;
        }

        .flame {
          position: absolute;
          bottom: -5px;
          border-radius: 50% 50% 20% 20%;
          filter: blur(1px);
        }

        .flame-main {
          width: 70%;
          height: 70%;
          background: linear-gradient(to bottom, #0061ff, #60efff);
          left: 15%;
          animation: flameMain 0.5s ease-in-out infinite alternate;
          z-index: 3;
        }

        .flame-middle {
          width: 60%;
          height: 65%;
          background: linear-gradient(to bottom, #ffffff, #60efff);
          left: 20%;
          animation: flameMiddle 0.4s ease-in-out infinite alternate;
          z-index: 2;
        }

        .flame-left {
          width: 30%;
          height: 60%;
          background: linear-gradient(to bottom, #0061ff, #60efff);
          left: 5%;
          animation: flameSide 0.7s ease-in-out infinite alternate;
          z-index: 1;
        }

        .flame-right {
          width: 30%;
          height: 60%;
          background: linear-gradient(to bottom, #0061ff, #60efff);
          left: 65%;
          animation: flameSide 0.6s ease-in-out infinite alternate;
          z-index: 1;
        }

        @keyframes flameMain {
          0% { height: 70%; opacity: 0.8; }
          100% { height: 80%; opacity: 1; }
        }

        @keyframes flameMiddle {
          0% { height: 65%; opacity: 0.8; }
          100% { height: 75%; opacity: 1; }
        }

        @keyframes flameSide {
          0% { height: 60%; opacity: 0.6; }
          100% { height: 70%; opacity: 0.8; }
        }


      `}</style>
    </div>
  );
};

export default AnimatedLogo;
