'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>Mail, <PERSON><PERSON>witter, FiInstagram, FiLinkedin } from 'react-icons/fi';

interface ComingSoonProps {
  title?: string;
  launchDate?: string;
}

export default function ComingSoon({
  title = 'Coming Soon',
  launchDate = '2025-04-15',
}: ComingSoonProps) {
  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft(launchDate));

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft(launchDate));
    }, 1000);

    return () => clearInterval(timer);
  }, [launchDate]);

  function calculateTimeLeft(endDate: string) {
    const difference = +new Date(endDate) - +new Date();
    return {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
      minutes: Math.floor((difference / 1000 / 60) % 60),
      seconds: Math.floor((difference / 1000) % 60),
      expired: difference < 0,
    };
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-white dark:bg-boxdark p-6 animate-fade-in">
      <div className="text-center max-w-2xl w-full animate-slide-up">
        <h1 className="text-4xl md:text-5xl font-bold text-black dark:text-white pb-6 bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
          {title}
        </h1>
        <p className="text-gray-600 dark:text-gray-300 text-lg mb-8">
          We're working hard to bring you this feature. Stay tuned!
        </p>

        {!timeLeft.expired && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8 animate-scale-in">
            <div className="p-6 bg-gradient-to-br from-brand-50 to-brand-100 dark:from-brand-900/30 dark:to-brand-800/20 rounded-lg shadow-sm">
              <div className="text-3xl font-bold text-brand-500">
                {timeLeft.days}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                Days
              </div>
            </div>
            <div className="p-6 bg-gradient-to-br from-brand-50 to-brand-100 dark:from-brand-900/30 dark:to-brand-800/20 rounded-lg shadow-sm">
              <div className="text-3xl font-bold text-brand-500">
                {timeLeft.hours}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                Hours
              </div>
            </div>
            <div className="p-6 bg-gradient-to-br from-brand-50 to-brand-100 dark:from-brand-900/30 dark:to-brand-800/20 rounded-lg shadow-sm">
              <div className="text-3xl font-bold text-brand-500">
                {timeLeft.minutes}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                Minutes
              </div>
            </div>
            <div className="p-6 bg-gradient-to-br from-brand-50 to-brand-100 dark:from-brand-900/30 dark:to-brand-800/20 rounded-lg shadow-sm">
              <div className="text-3xl font-bold text-brand-500">
                {timeLeft.seconds}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                Seconds
              </div>
            </div>
          </div>
        )}

        <div className="space-y-4">
          <div className="flex justify-center space-x-4 animate-fade-in-delay">
            <a
              href="https://twitter.com/dolzeai"
              target="_blank"
              rel="noopener noreferrer"
              className="p-3 bg-gray-100 dark:bg-gray-800 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            >
              <FiTwitter className="w-6 h-6 text-gray-700 dark:text-gray-300" />
            </a>
            <a
              href="https://instagram.com/dolze.ai"
              target="_blank"
              rel="noopener noreferrer"
              className="p-3 bg-gray-100 dark:bg-gray-800 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            >
              <FiInstagram className="w-6 h-6 text-gray-700 dark:text-gray-300" />
            </a>
            <a
              href="https://linkedin.com/company/dolzeai"
              target="_blank"
              rel="noopener noreferrer"
              className="p-3 bg-gray-100 dark:bg-gray-800 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            >
              <FiLinkedin className="w-6 h-6 text-gray-700 dark:text-gray-300" />
            </a>
          </div>

          <div className="flex items-center justify-center space-x-2 text-gray-600 dark:text-gray-300 animate-fade-in-delay">
            <FiMail className="w-5 h-5" />
            <a href="mailto:<EMAIL>" className="hover:underline">
              <EMAIL>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
