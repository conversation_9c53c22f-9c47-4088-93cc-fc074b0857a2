'use client';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { Dropdown } from '../ui/dropdown/Dropdown';
import { useAuth } from '@/context/AuthContext';
import Link from 'next/link';
import { FiZap, FiLogOut } from 'react-icons/fi';

interface UserDropdownProps {
  isMobile?: boolean;
}

export default function UserDropdown({ isMobile = false }: UserDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { user, logout } = useAuth();
  const router = useRouter();

  function toggleDropdown(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) {
    e.stopPropagation();
    setIsOpen(!isOpen);
  }

  function closeDropdown() {
    setIsOpen(false);
  }

  // Get first letters of first two names for avatar
  const getInitials = (name: string | null | undefined): string => {
    if (!name) return 'U';

    const nameParts = name.split(' ');
    if (nameParts.length === 1) return nameParts[0][0].toUpperCase();

    return (nameParts[0][0] + nameParts[1][0]).toUpperCase();
  };

  const avatarInitials = getInitials(user?.displayName);

  return (
    <div className="relative flex items-center">
      <button
        onClick={toggleDropdown}
        className="flex items-center justify-center dropdown-toggle"
      >
        <div className="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center text-white text-xs font-medium aspect-square">
          {avatarInitials}
        </div>
      </button>

      {isMobile ? (
        // Mobile drawer style dropdown
        <div
          className={`fixed inset-0 bg-gray-900 bg-opacity-50 z-50 transition-opacity ${isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
          onClick={closeDropdown}
        >
          <div
            className={`fixed right-0 top-0 h-full w-[300px] bg-white shadow-xl transform transition-transform ${isOpen ? 'translate-x-0' : 'translate-x-full'}`}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="relative p-4 border-b border-gray-100">
              <button
                onClick={closeDropdown}
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 18L18 6M6 6L18 18"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>
              <div className="flex items-center gap-3 pr-8">
                <div className="w-10 h-10 rounded-full bg-purple-500 flex items-center justify-center text-white aspect-square min-w-[2.5rem]">
                  {avatarInitials}
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-900">
                      {user?.displayName}
                    </span>
                    <span className="rounded bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-700">
                      PRO
                    </span>
                  </div>
                  <span className="text-sm text-gray-500">
                    {user?.email}
                  </span>
                </div>
              </div>
            </div>
            <div className="py-4 px-4">
              <div className="pl-2">
                <Link
                  href="/credits"
                  onClick={closeDropdown}
                  className="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg mb-2"
                >
                  <FiZap className="mr-3 h-5 w-5 text-yellow-500" />
                  Upgrade Plan
                </Link>

                <button
                  onClick={async () => {
                    try {
                      await logout();
                      closeDropdown();
                      router.push('/signin');
                    } catch (error) {
                      console.error('Error signing out:', error);
                    }
                  }}
                  className="flex w-full items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg"
                >
                  <FiLogOut className="mr-3 h-5 w-5" />
                  Sign Out
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        // Desktop dropdown style
        <Dropdown
          isOpen={isOpen}
          onClose={closeDropdown}
          className="absolute right-0 top-full mt-2 w-[300px] rounded-lg border border-gray-200 bg-white py-2 shadow-lg"
        >
          <div className="px-4 py-3 border-b border-gray-100">
            <div>
              <div className="flex items-center gap-2">
                <span className="font-medium text-gray-900">
                  {user?.displayName}
                </span>
                <span className="rounded bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-700">
                  PRO
                </span>
              </div>
              <span className="text-sm text-gray-500">
                {user?.email}
              </span>
            </div>
          </div>
          <div className="py-1">
            <div className="px-1">
              <Link
                href="/credits"
                onClick={closeDropdown}
                className="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg"
              >
                <FiZap className="mr-3 h-5 w-5 text-yellow-500" />
                Upgrade Plan
              </Link>

              <button
                onClick={async () => {
                  try {
                    await logout();
                    closeDropdown();
                    router.push('/signin');
                  } catch (error) {
                    console.error('Error signing out:', error);
                  }
                }}
                className="flex w-full items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg"
              >
                <FiLogOut className="mr-3 h-5 w-5" />
                Sign Out
              </button>
            </div>
          </div>
        </Dropdown>
      )}
    </div>
  );
}
