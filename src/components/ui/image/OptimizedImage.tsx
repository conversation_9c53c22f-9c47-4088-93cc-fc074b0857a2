import React, { useState } from 'react';
import Image, { ImageProps } from 'next/image';
import { BiImage, BiUser } from 'react-icons/bi';

interface OptimizedImageProps extends Omit<ImageProps, 'onLoad' | 'onError'> {
  fallbackIcon?: React.ReactNode;
  showPlaceholder?: boolean;
  isAvatar?: boolean;
  priority?: boolean;
}

/**
 * OptimizedImage component with built-in loading states, error handling, and optimizations
 */
const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className = '',
  fallbackIcon,
  showPlaceholder = true,
  isAvatar = false,
  placeholder = 'blur',
  loading,
  sizes,
  quality = 80,
  priority,
  ...props
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);

  // Skip optimization for data URLs
  const isDataUrl = typeof src === 'string' && src.startsWith('data:');

  // Default blur data URL based on image dimensions
  const defaultBlurDataUrl = isAvatar
    ? 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9IiNFNUU3RUIiLz48L3N2Zz4='
    : 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDQwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjQwMCIgaGVpZ2h0PSI0MDAiIGZpbGw9IiNFNUU3RUIiLz48L3N2Zz4=';

  // Default sizes if not provided
  const defaultSizes = isAvatar
    ? '40px'
    : '(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw';

  // Default fallback icon
  const defaultFallbackIcon = isAvatar ? (
    <BiUser className="text-gray-400" size={20} />
  ) : (
    <BiImage className="text-gray-400 text-6xl" />
  );

  const handleLoad = (event: React.SyntheticEvent<HTMLImageElement>) => {
    setIsLoading(false);
    const target = event.target as HTMLImageElement;
    target.classList.remove('opacity-0');
  };

  const handleError = () => {
    setIsLoading(false);
    setError(true);
  };

  // If there's an error, show fallback
  if (error) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-200 dark:bg-gray-700 ${className}`}
      >
        {fallbackIcon || defaultFallbackIcon}
      </div>
    );
  }

  return (
    <>
      {showPlaceholder && isLoading && (
        <div
          className={`absolute inset-0 flex items-center justify-center bg-gray-200 dark:bg-gray-700 ${isAvatar ? 'rounded-full' : ''}`}
        >
          {fallbackIcon || defaultFallbackIcon}
        </div>
      )}
      <Image
        src={src}
        alt={alt || ''}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        onLoad={handleLoad}
        onError={handleError}
        loading={priority ? undefined : loading || 'lazy'}
        placeholder={placeholder}
        blurDataURL={props.blurDataURL || defaultBlurDataUrl}
        sizes={sizes || defaultSizes}
        quality={quality}
        priority={priority}
        unoptimized={isDataUrl}
        {...props}
      />
    </>
  );
};

export default OptimizedImage;
