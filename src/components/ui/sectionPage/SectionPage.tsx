'use client';

import Link from 'next/link';
import React, { useState } from 'react';
import { FiChevronRight } from 'react-icons/fi';
import { SectionPageCardProps } from '@/types';

const SectionPageCard: React.FC<SectionPageCardProps> = ({
  title,
  description,
  icon,
  label,
  path,
  isActionCard,
  actionCardComponent: CardComponent,
  color = 'bg-brand-50',
}) => {
  if (isActionCard && CardComponent) {
    return <CardComponent />;
  }

  return (
    <Link href={path || '#'}>
      <div
        className={`${color} rounded-xl p-4 sm:p-6 shadow-sm hover:shadow-md transition-all duration-200 h-full flex flex-col`}
      >
        <div className="mb-4">
          {typeof icon === 'function'
            ? React.createElement(
                icon as React.ComponentType<{ className?: string }>,
                {
                  className: 'w-6 h-6 text-brand-500',
                }
              )
            : icon}
        </div>
        {label && (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-brand-100 text-brand-800 mb-2">
            {label}
          </span>
        )}
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {title}
        </h3>
        {description && (
          <p className="text-gray-500 dark:text-gray-400 text-sm mb-4 flex-grow">
            {description}
          </p>
        )}
        <div className="flex items-center text-brand-600 text-sm font-medium">
          Get started
          <FiChevronRight className="ml-1 h-4 w-4" />
        </div>
      </div>
    </Link>
  );
};

interface SectionPageProps {
  title: string;
  subtitle: string;
  description: string;
  cards: SectionPageCardProps[];
  gridColumns?: string;
  actionButton?: {
    label: string;
    onClick: () => void;
  };
}

export default function SectionPage({
  title,
  subtitle,
  description,
  cards,
  gridColumns = 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
  actionButton,
}: SectionPageProps) {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="min-h-screen w-full overflow-y-auto bg-gray-50 dark:bg-boxdark pb-20">
      {/* Project Header */}
      <div className="bg-white dark:bg-gray-900 border-b dark:border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="w-full">
              <div className="inline-block mb-4 px-3 py-1.5 bg-brand-50 dark:bg-brand-900/30 rounded-full">
                <span className="text-brand-600 dark:text-brand-400 font-medium text-sm">
                  {subtitle}
                </span>
              </div>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white break-words">
                {title}
              </h1>
              <p className="text-gray-500 dark:text-gray-400 mt-1 text-sm sm:text-base">
                {description}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8 overflow-visible">
        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-800 mb-4 sm:mb-8">
          <nav
            className="-mb-px flex space-x-4 sm:space-x-8 overflow-x-auto"
            aria-label="Tabs"
          >
            <button
              onClick={() => setActiveTab('overview')}
              className={`${
                activeTab === 'overview'
                  ? 'border-brand-500 text-brand-600 dark:text-brand-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-700'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab('all')}
              className={`${
                activeTab === 'all'
                  ? 'border-brand-500 text-brand-600 dark:text-brand-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-700'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              All Tools
            </button>
          </nav>
        </div>

        {activeTab === 'overview' && (
          <>
            {/* Featured Actions */}
            <div className="mb-6 sm:mb-10">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4 sm:mb-6">
                Featured Tools
              </h2>
              <div className={`grid ${gridColumns} gap-4 sm:gap-6`}>
                {cards.slice(0, 4).map((card) => (
                  <SectionPageCard key={card.id} {...card} />
                ))}
              </div>
            </div>

            {actionButton && (
              <div className="mt-6 text-center">
                <button
                  onClick={actionButton.onClick}
                  className="px-6 py-2.5 bg-brand-500 hover:bg-brand-600 text-white rounded-md transition-colors whitespace-nowrap text-sm font-medium shadow-sm hover:shadow-md"
                >
                  {actionButton.label}
                </button>
              </div>
            )}
          </>
        )}

        {activeTab === 'all' && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            {cards.map((card) => (
              <SectionPageCard key={card.id} {...card} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
