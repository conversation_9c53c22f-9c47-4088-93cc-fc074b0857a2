'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import Button from '../button/Button';
import { useGlobalContext } from '@/context/GlobalContext';
import * as ActionTypes from '@/constants/actions/actions.constants';
import { ChatboxMessage } from '@/types';
import { apiService } from '@/services/api';
import { useSearchParams } from 'next/navigation';
import { Modal } from '../modal';
import ReactMarkdown from 'react-markdown';
import ThreeDotLoader from '@/components/loading/ThreeDotLoader';
import { convertToIST } from '@/utils';
import { FiClock } from 'react-icons/fi';
import { ChatIcon, CloseLineIcon } from '@/icons';

type ChatBoxProps = {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (
    message: string,
    conversationId?: string,
    businessIdeaId?: string
  ) => Promise<{
    success: boolean;
    response?: string;
    error?: string;
    conversation_id?: string;
  }>;
  initialMessage: string;
};

interface Conversation {
  id: string;
  user_id: string;
  business_idea_id: string;
  session_id: string;
  messages: {
    sender: 'user' | 'agent';
    text: string;
    timestamp: string;
  }[];
  created_at: string;
  updated_at: string;
}

// Using the ChatboxMessage type from global state

const ChatBox = ({
  isOpen,
  onClose,
  onSubmit,
  initialMessage,
}: ChatBoxProps) => {
  const { state, dispatch } = useGlobalContext();
  const { messages } = state.chatboxMessages;
  const [inputValue, setInputValue] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [isThinking, setIsThinking] = useState(false);
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [currentConversationId, setCurrentConversationId] = useState<
    string | undefined
  >(undefined);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const searchParams = useSearchParams();
  const businessIdeaId = searchParams?.get('id');

  // Auto-scroll to bottom when new messages are added
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize chat when opened
  useEffect(() => {
    if (isOpen) {
      // Update global state to show chat is open
      dispatch({
        type: ActionTypes.SET_CHATBOX_OPEN,
        payload: true,
      });

      // Set initial message in global state
      const initialBotMessage: ChatboxMessage = {
        id: `bot-initial-${Date.now()}`,
        text: initialMessage,
        isBot: true,
        timestamp: new Date().toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        }),
        business_idea_id: businessIdeaId || undefined,
      };

      dispatch({
        type: ActionTypes.SET_CHATBOX_MESSAGES,
        payload: [initialBotMessage],
      });

      setInputValue('');
    }
  }, [isOpen, initialMessage, dispatch]);

  // Reset all state values to initial state
  const resetChatState = useCallback(() => {
    setInputValue('');
    setIsSending(false);
    setIsThinking(false);
    setIsHistoryOpen(false);
    setConversations([]);
    setIsLoadingHistory(false);
    setCurrentConversationId(undefined);

    // Clear messages in global state
    dispatch({
      type: ActionTypes.CLEAR_CHATBOX_MESSAGES,
    });
  }, [dispatch]);

  // Update global state when chat is closed
  useEffect(() => {
    if (!isOpen) {
      dispatch({
        type: ActionTypes.SET_CHATBOX_OPEN,
        payload: false,
      });

      // Reset all state when chat is closed
      resetChatState();
    }

    // Clean up when component unmounts
    return () => {
      resetChatState();
    };
  }, [isOpen, dispatch, resetChatState]);

  // Fetch conversation history
  const fetchConversations = async () => {
    if (!businessIdeaId) return;

    setIsLoadingHistory(true);
    try {
      const response = await apiService.get<Conversation[]>(
        `/api/conversations?business_idea_id=${businessIdeaId}`
      );

      if (response.success && response.data) {
        setConversations(response.data);
      }
    } catch (error) {
      console.error('Error fetching conversations:', error);
    } finally {
      setIsLoadingHistory(false);
    }
  };

  // Load conversation history when history button is clicked
  const handleHistoryClick = () => {
    setIsHistoryOpen(true);
    fetchConversations();
  };

  // Select a conversation from history
  const selectConversation = (conversation: Conversation) => {
    setCurrentConversationId(conversation.id);

    // Convert conversation messages to ChatboxMessage format
    const formattedMessages: ChatboxMessage[] = [
      // Add initial bot message
      {
        id: `bot-initial`,
        text: initialMessage,
        isBot: true,
        timestamp: new Date().toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        }),
        conversation_id: conversation.id,
        business_idea_id: conversation.business_idea_id,
      },
      // Add conversation messages
      ...conversation.messages.map((msg, index) => ({
        id: `${msg.sender === 'user' ? 'user' : 'bot'}-${index}`,
        text: msg.text,
        isBot: msg.sender === 'agent',
        timestamp: convertToIST(msg.timestamp),
        conversation_id: conversation.id,
        business_idea_id: conversation.business_idea_id,
      })),
    ];

    // Set messages in global state
    dispatch({
      type: ActionTypes.SET_CHATBOX_MESSAGES,
      payload: formattedMessages,
    });

    // Close history modal
    setIsHistoryOpen(false);
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isSending) return;

    // Create user message
    const userMessage: ChatboxMessage = {
      id: `user-${Date.now()}`,
      text: inputValue,
      isBot: false,
      timestamp: new Date().toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      }),
      conversation_id: currentConversationId,
      business_idea_id: businessIdeaId || undefined,
    };

    // Add user message to global state
    dispatch({
      type: ActionTypes.ADD_CHATBOX_MESSAGE,
      payload: userMessage,
    });

    setInputValue('');
    setIsSending(true);
    setIsThinking(true);
    scrollToBottom();

    try {
      // Make API call with the single message
      const result = await onSubmit(
        userMessage.text,
        currentConversationId,
        businessIdeaId || undefined
      );

      // Update conversation ID if received from API
      if (result.conversation_id && !currentConversationId) {
        setCurrentConversationId(result.conversation_id);
      }

      // Create bot response
      const botMessage: ChatboxMessage = {
        id: `bot-${Date.now()}`,
        text: result.success
          ? result.response || 'Message received'
          : result.error || 'Something went wrong',
        isBot: true,
        timestamp: new Date().toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        }),
        conversation_id: result.conversation_id || currentConversationId,
        business_idea_id: businessIdeaId || undefined,
      };

      // Add bot response after a short delay to simulate thinking
      setTimeout(() => {
        setIsThinking(false);
        // Add bot message to global state
        dispatch({
          type: ActionTypes.ADD_CHATBOX_MESSAGE,
          payload: botMessage,
        });
        scrollToBottom();
      }, 500);
    } catch (error) {
      console.error('Chat error:', error);

      // Create error message
      const errorMessage: ChatboxMessage = {
        id: `error-${Date.now()}`,
        text: 'An error occurred. Please try again.',
        isBot: true,
        timestamp: new Date().toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        }),
        conversation_id: currentConversationId,
        business_idea_id: businessIdeaId || undefined,
      };

      // Add error message to global state after a short delay
      setTimeout(() => {
        setIsThinking(false);
        dispatch({
          type: ActionTypes.ADD_CHATBOX_MESSAGE,
          payload: errorMessage,
        });
        scrollToBottom();
      }, 500);
    } finally {
      setIsSending(false);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed h-[85vh] sm:h-3/4 bottom-0 sm:bottom-4 right-0 sm:right-4 w-full sm:w-96 bg-white dark:bg-boxdark rounded-t-xl sm:rounded-lg shadow-xl border border-stroke dark:border-strokedark flex flex-col z-50 transition-all duration-300 ease-in-out">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b border-stroke dark:border-strokedark bg-gray-50 dark:bg-gray-800 rounded-t-xl sm:rounded-t-lg">
          <div className="flex items-center gap-2">
            <h3 className="text-md font-semibold text-black dark:text-white flex items-center gap-2">
              <ChatIcon />
              Chat
            </h3>
            <button
              onClick={handleHistoryClick}
              className="text-gray-500 hover:text-brand-500 dark:hover:text-brand-400 transition-colors text-sm flex items-center gap-1 bg-white dark:bg-gray-700 px-2 py-1 rounded-md shadow-theme-xs"
              title="View chat history"
            >
              <FiClock />
              <span>History</span>
            </button>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-brand-500 dark:hover:text-brand-400 transition-colors bg-white dark:bg-gray-700 p-1.5 rounded-md shadow-theme-xs"
          >
            <CloseLineIcon />
          </button>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-800 custom-scrollbar">
          {messages.map((message, index) => (
            <div
              key={message.id || index}
              className={`flex ${message.isBot ? 'justify-start' : 'justify-end'} animate-fade-in`}
            >
              <div
                className={`max-w-[85%] p-3.5 rounded-2xl shadow-theme-sm ${
                  message.isBot
                    ? 'bg-white dark:bg-boxdark text-gray-800 dark:text-gray-200 rounded-tl-none'
                    : 'bg-brand-600 text-white rounded-tr-none'
                }`}
              >
                <div className="text-sm markdown-content">
                  {message.isBot ? (
                    <div className="prose prose-sm max-w-none dark:prose-invert prose-p:leading-relaxed prose-headings:font-semibold prose-a:text-brand-500 dark:prose-a:text-brand-400 prose-a:no-underline hover:prose-a:underline">
                      <ReactMarkdown>{message.text}</ReactMarkdown>
                    </div>
                  ) : (
                    message.text
                  )}
                </div>
                <span
                  className={`text-xs mt-2 block ${
                    message.isBot ? 'text-gray-500' : 'text-brand-100'
                  } opacity-80`}
                >
                  {message.timestamp}
                </span>
              </div>
            </div>
          ))}

          {/* Thinking indicator */}
          {isThinking && (
            <div className="flex justify-start animate-fade-in">
              <div className="max-w-[85%] p-3.5 rounded-2xl rounded-tl-none shadow-theme-sm bg-white dark:bg-boxdark text-gray-800 dark:text-gray-200">
                <div className="flex items-center space-x-3">
                  <ThreeDotLoader />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    Thinking...
                  </span>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <div className="p-2 border-t border-stroke dark:border-strokedark bg-white dark:bg-boxdark shadow-theme-sm">
          <div className="flex gap-3 items-center">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              className="flex-1 p-3 border border-stroke dark:border-strokedark rounded-full focus:outline-none focus:ring-2 focus:ring-brand-500 dark:bg-boxdark dark:text-white text-sm shadow-theme-xs"
              placeholder="Type a message..."
              onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
              disabled={isSending || isThinking}
            />
            <Button
              onClick={handleSendMessage}
              variant="primary"
              className="px-4 py-2.5 text-sm rounded-full shadow-theme-sm"
              disabled={isSending || isThinking || !inputValue.trim()}
            >
              {isSending || isThinking ? 'Sending...' : 'Send'}
            </Button>
          </div>
        </div>
      </div>

      {/* Chat History Modal */}
      <Modal
        isOpen={isHistoryOpen}
        onClose={() => setIsHistoryOpen(false)}
        className="max-w-2xl p-5 lg:p-6 bg-white dark:bg-gray-900 w-[90%] sm:w-auto mx-auto"
        title="Chat History"
        subtitle="Select a conversation to continue"
        headerClassName="bg-white dark:bg-gray-900 rounded-t-3xl  border-b border-stroke dark:border-strokedark"
      >
        <div className="mt-20 p-4 pt-0 max-h-[70vh] overflow-y-auto custom-scrollbar">
          {isLoadingHistory ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-500"></div>
            </div>
          ) : conversations.length > 0 ? (
            <div className="space-y-4 ">
              {conversations.map((conversation) => (
                <div
                  key={conversation.id || conversation.created_at}
                  className="p-4 border border-stroke dark:border-strokedark rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-all duration-200 hover:shadow-theme-sm transform hover:-translate-y-1"
                  onClick={() => selectConversation(conversation)}
                >
                  <div className="flex justify-between items-start mb-2">
                    <span className="text-sm font-medium">
                      {new Date(conversation.created_at).toLocaleString()}
                    </span>
                    <span className="text-xs text-gray-500">
                      {conversation.messages.length} messages
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                    {conversation.messages[0]?.text || 'No messages'}
                  </p>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>No conversation history found</p>
            </div>
          )}
          <div className="flex items-center justify-center mt-8">
            <Button
              onClick={() => setIsHistoryOpen(false)}
              variant="outline"
              className="px-6"
            >
              Close
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ChatBox;
