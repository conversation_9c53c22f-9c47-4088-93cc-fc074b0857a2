import { ChatMessage } from './types';

/**
 * Creates a new user message
 */
export function createUserMessage(content: string): ChatMessage {
  return {
    id: `user-${Date.now()}`,
    content,
    sender: 'user',
    timestamp: new Date(),
  };
}

/**
 * Creates a new bot message
 */
export function createBotMessage(content: string | React.ReactNode): ChatMessage {
  return {
    id: `bot-${Date.now()}`,
    content,
    sender: 'bot',
    timestamp: new Date(),
  };
}

/**
 * Formats a timestamp to a readable time string
 */
export function formatTimestamp(timestamp: Date): string {
  return timestamp.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
  });
}
