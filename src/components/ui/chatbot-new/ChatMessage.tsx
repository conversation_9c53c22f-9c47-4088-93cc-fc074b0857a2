'use client';

import React, { useState, useEffect } from 'react';
import { ChatMessage as ChatMessageType } from './types';
import OptimizedImage from '../image/OptimizedImage';
// formatTimestamp import removed

interface ChatMessageProps {
  message: ChatMessageType;
  userPhotoURL?: string;
  onEditClick?: (messageId: string) => void;
  isLastBotMessage?: boolean;
}

const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  userPhotoURL,
  onEditClick,
  isLastBotMessage,
}) => {
  const { content, sender, editable } = message; // timestamp removed
  // Timestamp display removed as requested
  const [isVisible, setIsVisible] = useState(false);
  const [isTyping, setIsTyping] = useState(
    sender === 'bot' && isLastBotMessage
  );

  // Default icons
  const userIcon = userPhotoURL || '/images/user-avatar.png';

  // Animation effect when message appears
  useEffect(() => {
    // Small delay to trigger animation
    const timer = setTimeout(() => setIsVisible(true), 50);

    // Simulate typing effect for bot messages
    if (sender === 'bot' && isLastBotMessage) {
      const typingTimer = setTimeout(() => setIsTyping(false), 1000);
      return () => clearTimeout(typingTimer);
    }

    return () => clearTimeout(timer);
  }, [sender, isLastBotMessage]);

  // Typing indicator component
  const TypingIndicator = () => (
    <div className="flex space-x-1.5 px-2 py-1.5">
      <div
        className="w-2 h-2 bg-brand-500 rounded-full animate-bounce"
        style={{ animationDelay: '0ms' }}
      />
      <div
        className="w-2 h-2 bg-brand-500 rounded-full animate-bounce"
        style={{ animationDelay: '150ms' }}
      />
      <div
        className="w-2 h-2 bg-brand-500 rounded-full animate-bounce"
        style={{ animationDelay: '300ms' }}
      />
    </div>
  );

  return (
    <div
      className={`flex items-start ${sender === 'bot' ? 'justify-start' : 'justify-end'}
      mb-4 gap-2 message-container transition-opacity duration-300 ease-in-out ${isVisible ? 'opacity-100' : 'opacity-0'}`}
      // Timestamp hover functionality removed
    >
      {sender === 'bot' && (
        <div className="flex-shrink-0 w-10 h-10 rounded-full overflow-hidden bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-md border-2 border-white">
          <OptimizedImage
            height={40}
            width={40}
            src="/images/icons/dolze-icon-black.svg"
            alt="AI Assistant"
            className="w-7 h-7 object-contain filter brightness-0 invert"
          />
        </div>
      )}

      <div className="relative group">
        {/* Timestamp tooltip removed */}

        <div
          className={`max-w-[80%] p-3.5 rounded-lg relative ${
            sender === 'bot'
              ? 'bg-gradient-to-r from-white to-gray-50 text-gray-800 rounded-tl-none shadow-md border border-gray-100'
              : 'bg-gradient-to-r from-brand-600 to-brand-500 text-white rounded-tr-none shadow-md'
          } transform transition-all duration-200 ease-out ${isVisible ? 'translate-y-0' : sender === 'bot' ? 'translate-y-2' : '-translate-y-2'}`}
        >
          {sender === 'bot' && onEditClick && isLastBotMessage && editable && (
            <button
              onClick={() => onEditClick(message.id)}
              className="edit-trigger-button absolute top-2 right-2 p-1.5 z-10 rounded-full bg-white text-brand-600 hover:bg-brand-50 shadow-sm transition-all duration-200 opacity-0 group-hover:opacity-100 hover:scale-110"
              title="Edit message"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
              </svg>
            </button>
          )}

          {isTyping ? (
            <TypingIndicator />
          ) : typeof content === 'string' ? (
            <p className="text-sm pr-6 leading-relaxed">{content}</p>
          ) : (
            content
          )}

          {/* Message decoration removed as requested */}
        </div>
      </div>

      {sender === 'user' && (
        <div className="flex-shrink-0 w-10 h-10 rounded-full overflow-hidden bg-gradient-to-br from-brand-500 to-brand-600 flex items-center justify-center shadow-md border-2 border-white">
          <OptimizedImage
            src={userIcon}
            alt="User"
            className="w-full h-full object-cover"
            height={40}
            width={40}
          />
        </div>
      )}
    </div>
  );
};

export default ChatMessage;
