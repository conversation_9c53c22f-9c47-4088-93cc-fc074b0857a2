import React from 'react';
import { ChatbotConfig } from './types';
import { BiLink } from 'react-icons/bi';

/**
 * Default chatbot configuration
 * This can be extended or overridden by passing a custom config to the Chatbot component
 */
export const getDefaultConfig = (userName: string): ChatbotConfig => ({
  initialNodeId: 'welcome',
  nodes: {
    welcome: {
      id: 'welcome',
      type: 'message',
      content: (
        <div className="p-2">
          <p className="text-gray-600 mt-2">
            Hi {userName || 'user'}, Welcome to Dolze.
          </p>
          <p className="text-gray-600 mt-2">
            You bring the dream, we bring intelligent agents to support you.
            Let's take your business to new heights together.
          </p>
          <b>Select your business stage</b> and Let's get started now.
        </div>
      ),
      bottomBar: {
        buttons: [
          {
            label: 'I have a business idea 💡',
            action: 'next',
            nextNodeId: 'businessIdea',
            variant: 'primary',
          },
          {
            label: 'I run an existing business 📈',
            action: 'next',
            nextNodeId: 'businessIdea',
            variant: 'outline',
          },
        ],
      },
    },
    businessIdea: {
      id: 'businessIdea',
      type: 'message',
      content: (
        <div className="p-2">
          <p className="text-gray-600 mt-2">
            Got it. Tell us about your business idea
          </p>
          <p className="text-gray-600 mt-2">
            2-3 lines will be enough to begin with. We will gather more details
            as we go.
          </p>
          <p className="text-gray-600 mt-2">
            (Optional) If you have a website, documents, or images handy, feel
            free to add it!
          </p>
        </div>
      ),
    },
    businessIdeaConfirm: {
      id: 'businessIdeaConfirm',
      type: 'message',
      content: (data) => (
        <div className="p-3 bg-gray-50 rounded-lg border border-gray-100 relative">
          <p className="text-gray-500 font-medium mb-2">
            This is the input idea, would you like to edit it? :
          </p>
          <p className="text-gray-800 font-sm bg-white">
            {data?.message || ''}
          </p>
          {data?.website && (
            <div className="mt-2 flex items-center text-sm text-blue-600">
              <BiLink />
              <a
                href={data.website}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:underline truncate"
              >
                {new URL(data.website).hostname}
              </a>
            </div>
          )}
        </div>
      ),
      editable: true, // Only this node is editable
      bottomBar: {
        buttons: [
          {
            label: 'Continue',
            action: 'next',
            nextNodeId: 'analyzeIdea',
            variant: 'primary',
          },
        ],
      },
    },
    analyzeIdea: {
      id: 'analyzeIdea',
      type: 'message',
      content: (
        <div className="p-2">
          <p className="text-gray-600 mt-2">
            Thanks for sharing your business idea! I'm analyzing it now...
          </p>
        </div>
      ),
      apiCall: {
        endpoint: '/api/validate-idea',
        method: 'POST',
        payloadFn: (data: any) => ({
          prompt: data?.message || '',
          website: data?.website || '',
        }),
        onSuccess: (response: any, dispatch: any) => {
          // Handle successful API response
          console.log('API call successful:', response);

          if (response.is_valid_idea) {
            // Show success message with business details
            dispatch({
              type: 'ADD_MESSAGE',
              payload: {
                id: `bot-success-${Date.now()}`,
                editable: true,
                data: {
                  // Store the original message and website
                  message: response.description || '',
                  website: response.website || '',
                  businessType: 'idea',
                  // Store the full API response data for editing
                  ...response,
                },
                content: (
                  <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100 shadow-sm relative">
                    <div className="flex items-center mb-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#10B981"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="mr-2"
                      >
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                      <h3 className="text-lg font-semibold text-green-700">
                        Great news! Your business idea is valid
                      </h3>
                    </div>

                    <div className="mb-3 bg-white p-3 rounded-md border border-green-100">
                      <h4 className="font-medium text-green-800 mb-1">
                        {response.title}
                      </h4>
                      <p className="text-gray-700 text-sm">
                        {response.description}
                      </p>
                    </div>

                    <div className="grid grid-cols-2 gap-2 mb-3">
                      <div className="bg-white p-2 rounded border border-green-100">
                        <p className="text-xs text-gray-500">Business Type</p>
                        <p className="text-sm font-medium text-gray-800 capitalize">
                          {response.business_idea_config?.biz_type || 'N/A'}
                        </p>
                      </div>
                      <div className="bg-white p-2 rounded border border-green-100">
                        <p className="text-xs text-gray-500">Offering</p>
                        <p className="text-sm font-medium text-gray-800 capitalize">
                          {response.business_idea_config?.offering || 'N/A'}
                        </p>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-1 mb-3">
                      {response.tags &&
                        response.tags.map((tag: string, index: number) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                    </div>

                    {response.website && (
                      <div className="flex items-center text-sm text-green-600 mb-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="12"
                          height="12"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-1"
                        >
                          <circle cx="12" cy="12" r="10"></circle>
                          <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                        </svg>
                        <a
                          href={response.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="hover:underline"
                        >
                          {response.website}
                        </a>
                      </div>
                    )}
                  </div>
                ),
                sender: 'bot',
                timestamp: new Date(),
                bottomBar: {
                  buttons: [
                    {
                      label: 'Continue',
                      action: 'next',
                      nextNodeId: 'processBusinessIdea',
                      variant: 'primary',
                    },
                  ],
                },
              },
            });

            // Set the current node to a node ID that will be used to determine the next steps
            dispatch({
              type: 'SET_CURRENT_NODE',
              payload: 'analyzeIdea',
            });
          } else {
            // Show error message with validation error
            dispatch({
              type: 'ADD_MESSAGE',
              payload: {
                id: `bot-error-${Date.now()}`,
                content: (
                  <div className="p-3 bg-red-50 rounded-lg border border-red-100">
                    <div className="flex items-center mb-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#DC2626"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="mr-2"
                      >
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="8" x2="12" y2="12"></line>
                        <line x1="12" y1="16" x2="12.01" y2="16"></line>
                      </svg>
                      <h3 className="font-medium text-red-700">
                        Validation Failed
                      </h3>
                    </div>
                    <p className="text-red-600 text-sm">
                      {response.validation_error_msg ||
                        'Your business idea needs more details to be validated.'}
                    </p>
                  </div>
                ),
                sender: 'bot',
                timestamp: new Date(),
                bottomBar: {
                  buttons: [
                    {
                      label: 'Start Over',
                      action: 'next',
                      nextNodeId: 'welcome',
                      variant: 'primary',
                    },
                  ],
                },
              },
            });
          }
        },
        onError: (error: any, dispatch: any) => {
          // Handle API error
          console.error('API call failed:', error);

          // You can add error handling logic here
          // For example, show an error message to the user
          dispatch({
            type: 'ADD_MESSAGE',
            payload: {
              id: `bot-error-${Date.now()}`,
              content: (
                <div className="p-3 bg-red-50 rounded-lg border border-red-100">
                  <div className="flex items-center mb-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="#DC2626"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-2"
                    >
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="12" y1="8" x2="12" y2="12"></line>
                      <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                    <h3 className="font-medium text-red-700">
                      Validation Failed
                    </h3>
                  </div>
                  <p className="text-red-600 text-sm">
                    {error?.message ||
                      'An error occurred while processing your request.'}
                  </p>
                </div>
              ),
              sender: 'bot',
              timestamp: new Date(),
              bottomBar: {
                buttons: [
                  {
                    label: 'Start Over',
                    action: 'next',
                    nextNodeId: 'welcome',
                    variant: 'primary',
                  },
                ],
              },
            },
          });
        },
      },
      bottomBar: {
        buttons: [
          {
            label: 'Continue',
            action: 'next',
            nextNodeId: 'processBusinessIdea',
            variant: 'primary',
          },
        ],
      },
    },
    processBusinessIdea: {
      id: 'processBusinessIdea',
      type: 'message',
      content: (
        <div className="p-2">
          <p className="text-gray-600 mt-2">Processing your business idea...</p>
        </div>
      ),
      apiCall: {
        endpoint: '/api/business-context/save-business-context',
        method: 'POST',
        payloadFn: (data: any) => {
          // Check if website was added
          if (data?.website) {
            return {
              projectId: data.id,
              content: {
                website: data.website,
                isWebsite: true,
              },
            };
          } else {
            // If no website, make a different API call
            return {
              endpoint: '/api/business-context/get-business-context',
              projectId: data.id,
              idea: data.description || data.message || '',
              isWebsite: false,
            };
          }
        },
        onSuccess: (response?: any, dispatch?: any) => {
          // Handle successful API response
          console.log('Business context API call successful:', response);

          // If website was provided, show success message with redirect button
          if (response.projectId) {
            dispatch({
              type: 'ADD_MESSAGE',
              payload: {
                id: `bot-context-success-${Date.now()}`,
                content: (
                  <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100 shadow-sm relative">
                    <div className="flex items-center mb-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#10B981"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="mr-2"
                      >
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                      <h3 className="text-lg font-semibold text-green-700">
                        Content Generated Successfully
                      </h3>
                    </div>
                    <p className="text-gray-700 mb-4">
                      We have all the details and have generated the required
                      data for you! Click the button below to explore your
                      dashboard.
                    </p>
                  </div>
                ),
                sender: 'bot',
                timestamp: new Date(),
                bottomBar: {
                  buttons: [
                    {
                      label: 'Go to Dashboard',
                      action: 'next',
                      nextNodeId: 'redirectToDashboard',
                      variant: 'primary',
                    },
                  ],
                },
                data: {
                  projectId: response.projectId,
                },
              },
            });
          }
        },
        onError: (error: any, dispatch: any) => {
          // Handle API error
          console.error('Business context API call failed:', error);

          dispatch({
            type: 'ADD_MESSAGE',
            payload: {
              id: `bot-error-${Date.now()}`,
              content: (
                <div className="p-3 bg-red-50 rounded-lg border border-red-100">
                  <div className="flex items-center mb-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="#DC2626"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-2"
                    >
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="12" y1="8" x2="12" y2="12"></line>
                      <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                    <h3 className="font-medium text-red-700">
                      Something went wrong
                    </h3>
                  </div>
                  <p className="text-red-600 text-sm">
                    We couldn't process your business details at the moment.
                    Please try again later.
                  </p>
                </div>
              ),
              sender: 'bot',
              timestamp: new Date(),
              bottomBar: {
                buttons: [
                  {
                    label: 'Start Over',
                    action: 'next',
                    nextNodeId: 'welcome',
                    variant: 'primary',
                  },
                ],
              },
            },
          });
        },
      },
    },
    redirectToDashboard: {
      id: 'redirectToDashboard',
      type: 'message',
      content: (
        <div className="p-2">
          <p className="text-gray-600 mt-2">Redirecting to dashboard...</p>
        </div>
      ),
      onEnter: (data: any) => {
        // Auto redirect to home page after 2 seconds
        setTimeout(() => {
          const projectId = data?.projectId || '';
          window.location.href = `/home?id=${projectId}`;
        }, 2000);
      },
    },
  },
});

// Fallback config for when userName is not provided
export const defaultConfig: ChatbotConfig = getDefaultConfig('user');
