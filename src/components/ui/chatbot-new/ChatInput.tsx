'use client';

import React, { useState, useRef, useEffect } from 'react';
import Button from '../button/Button';
import { BiLink } from 'react-icons/bi';
import { IoAttach } from 'react-icons/io5';

// Animation keyframes for fadeIn
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.innerHTML = `
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    .animate-fadeIn {
      animation: fadeIn 0.3s ease-out forwards;
    }
  `;
  document.head.appendChild(style);
}

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  placeholder?: string;
  disabled?: boolean;
  onWebsiteAdded?: (url: string) => void;
  handleStartOver: () => void;
}
const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  placeholder = 'Type a message...',
  disabled = false,
  onWebsiteAdded,
  handleStartOver,
}) => {
  const [inputValue, setInputValue] = useState('');
  const [showPopover, setShowPopover] = useState(false);
  const [showUrlInput, setShowUrlInput] = useState(false);
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [urlInputValue, setUrlInputValue] = useState('');
  const [urlError, setUrlError] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const popoverRef = useRef<HTMLDivElement>(null);
  const urlInputRef = useRef<HTMLInputElement>(null);
  const urlPopoverRef = useRef<HTMLDivElement>(null);

  // Close popovers when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Close options popover when clicking outside
      if (
        popoverRef.current &&
        !popoverRef.current.contains(event.target as Node)
      ) {
        setShowPopover(false);
      }

      // Close URL input popover when clicking outside
      if (
        urlPopoverRef.current &&
        !urlPopoverRef.current.contains(event.target as Node)
      ) {
        setShowUrlInput(false);
        setUrlError('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Focus URL input when it appears
  useEffect(() => {
    if (showUrlInput && urlInputRef.current) {
      urlInputRef.current.focus();
    }
  }, [showUrlInput]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const message = inputValue.trim();
    if (!message) return;

    onSendMessage(message);
    setInputValue('');

    // Focus the input after sending
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      e.preventDefault();
      onSendMessage(inputValue.trim());
      setInputValue('');
    }
  };

  const validateUrl = (url: string): boolean => {
    // If URL doesn't start with http:// or https://, prepend https://
    let processedUrl = url.trim();
    if (processedUrl && !processedUrl.match(/^https?:\/\//)) {
      processedUrl = `https://${processedUrl}`;
    }

    // Basic URL validation
    try {
      const urlObj = new URL(processedUrl);

      // Additional validation: ensure there's a valid domain with at least one dot
      if (!urlObj.hostname || !urlObj.hostname.includes('.')) {
        setUrlError('Please enter a valid domain name');
        return false;
      }

      // Check for a valid domain extension (at least two characters after the last dot)
      const parts = urlObj.hostname.split('.');
      const tld = parts[parts.length - 1];

      // Ensure TLD is at least 2 characters and contains only letters
      // This prevents domains like 'example.' or 'example.1' from being accepted
      if (!tld || tld.length < 2 || !/^[a-zA-Z]+$/.test(tld)) {
        setUrlError('Please enter a valid domain extension (e.g., .com, .org)');
        return false;
      }

      setUrlError('');
      setWebsiteUrl(processedUrl);
      if (onWebsiteAdded) {
        onWebsiteAdded(processedUrl);
      }
      return true;
    } catch (err) {
      console.error('Invalid URL:', err);
      setUrlError('Please enter a valid URL');
      return false;
    }
  };

  const handleUrlSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Don't proceed if URL is empty
    if (!urlInputValue.trim()) {
      setUrlError('Please enter a URL');
      return;
    }

    if (validateUrl(urlInputValue)) {
      setShowUrlInput(false);
      setShowPopover(false);
      setUrlInputValue('');
    }
  };

  const handlePlusClick = () => {
    setShowPopover(!showPopover);
  };

  const handleOptionClick = (option: string) => {
    if (option === 'website') {
      setShowUrlInput(true);
      setShowPopover(false); // Close the original popover
    } else {
      // For now, just close the popover for other options
      setShowPopover(false);
    }
  };

  return (
    <div className="relative">
      <form
        onSubmit={handleSubmit}
        className="flex items-center gap-2 p-3 bg-gradient-to-r from-white to-gray-50 rounded-lg shadow-md border border-gray-100"
      >
        {/* Start Over button - more compact */}
        <button
          type="button"
          onClick={handleStartOver}
          className="h-10 p-1.5 rounded-md text-xs font-medium transition-all border border-gray-200 text-gray-600 hover:bg-gray-50 hover:text-gray-800 flex items-center shadow-sm hover:scale-105 active:scale-95"
          title="Start Over"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M3 2v6h6"></path>
            <path d="M3 13a9 9 0 1 0 3-7.7L3 8"></path>
          </svg>
        </button>

        {/* Input field with enhanced styling */}
        <div className="relative flex-1 flex items-center">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => {
              setInputValue(e.target.value);
            }}
            onKeyDown={handleKeyDown}
            onFocus={() => inputValue.length > 0}
            placeholder={websiteUrl ? '' : placeholder}
            disabled={disabled}
            className={`w-full h-10 px-3 border ${'border-gray-200'} rounded-md focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all text-gray-700 shadow-sm text-sm bg-white`}
          />
        </div>

        {/* Plus button with enhanced styling */}
        <button
          type="button"
          onClick={handlePlusClick}
          className="h-10 p-1.5 text-brand-600 hover:text-brand-700 focus:outline-none bg-brand-50 rounded-md transition-all duration-200 border border-brand-100 hover:bg-brand-100 hover:scale-105 active:scale-95 shadow-sm"
          title="Add content"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
        </button>

        {/* Website URL display with enhanced styling */}
        {websiteUrl && (
          <div className="flex items-center h-10 pointer-events-auto">
            <div
              className="bg-gradient-to-r from-brand-50 to-brand-100 border border-brand-200 text-brand-700 text-xs px-2 rounded-md flex items-center max-w-[180px] shadow-sm h-full cursor-pointer hover:bg-brand-100 transition-all duration-200 hover:scale-105 active:scale-95"
              onClick={() => {
                // Pre-fill the URL input when editing
                setUrlInputValue(websiteUrl);
                setShowUrlInput(true);
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="10"
                height="10"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-1 flex-shrink-0 text-brand-600"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
              </svg>
              <span className="truncate font-medium">
                {new URL(websiteUrl).hostname}
              </span>
              <button
                type="button"
                className="ml-1 text-brand-600 hover:text-brand-800 flex-shrink-0 p-0.5 hover:bg-brand-200 rounded-full transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  setWebsiteUrl('');
                  if (onWebsiteAdded) onWebsiteAdded('');
                }}
              >
                ×
              </button>
            </div>
          </div>
        )}

        {/* Send button with enhanced styling */}
        <button
          type="submit"
          disabled={!inputValue.trim() || disabled}
          className="h-10 bg-gradient-to-r from-brand-600 to-brand-500 text-white px-3 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:from-brand-700 hover:to-brand-600 transition-all duration-200 shadow-md font-medium text-sm flex items-center gap-1 hover:scale-105 active:scale-95"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="22" y1="2" x2="11" y2="13"></line>
            <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
          </svg>
        </button>
      </form>

      {/* Popover for options */}
      {showPopover && (
        <div
          ref={popoverRef}
          className="absolute bottom-16 right-0 bg-white shadow-xl rounded-lg p-4 w-72 z-10 border border-gray-200 animate-fadeIn"
          style={{ boxShadow: '0 8px 30px rgba(0,0,0,0.12)' }}
        >
          <div className="text-sm font-semibold text-gray-800 mb-3 flex items-center">
            <div className="w-8 h-8 rounded-full flex items-center justify-center mr-2 shadow-sm">
              <IoAttach className="w-6 h-6" />
            </div>
            Add to your message
          </div>

          <div className="space-y-1.5">
            <button
              type="button"
              className="w-full text-left px-4 py-3 rounded-md hover:bg-brand-50 flex items-center text-sm text-gray-700 hover:text-brand-700 transition-all duration-200 group"
              onClick={() => handleOptionClick('website')}
            >
              <div className="w-8 h-8 rounded-full bg-brand-100 flex items-center justify-center mr-3 group-hover:bg-brand-200 transition-colors duration-200 shadow-sm">
                <BiLink />
              </div>
              <div>
                <div className="font-medium">Upload website link</div>
                <div className="text-xs text-gray-500 mt-0.5">
                  Add your business website URL
                </div>
              </div>
            </button>

            <button
              type="button"
              className="w-full text-left px-4 py-3 rounded-md hover:bg-gray-50 flex items-center text-sm text-gray-400 cursor-not-allowed"
              disabled
            >
              <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-3 shadow-sm">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-gray-400"
                >
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                  <circle cx="8.5" cy="8.5" r="1.5"></circle>
                  <polyline points="21 15 16 10 5 21"></polyline>
                </svg>
              </div>
              <div>
                <div className="font-medium">Upload screenshot</div>
                <div className="text-xs text-gray-400 mt-0.5">Coming soon</div>
              </div>
            </button>

            <button
              type="button"
              className="w-full text-left px-4 py-3 rounded-md hover:bg-gray-50 flex items-center text-sm text-gray-400 cursor-not-allowed"
              disabled
            >
              <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-3 shadow-sm">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-gray-400"
                >
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
              </div>
              <div>
                <div className="font-medium">Upload documents</div>
                <div className="text-xs text-gray-400 mt-0.5">Coming soon</div>
              </div>
            </button>
          </div>
        </div>
      )}

      {/* URL input popover */}
      {showUrlInput && (
        <div
          ref={urlPopoverRef}
          className="absolute bottom-16 right-0 bg-white shadow-xl rounded-lg p-4 w-72 z-10 border border-gray-200 animate-fadeIn"
          style={{ boxShadow: '0 8px 30px rgba(0,0,0,0.12)' }}
        >
          <div className="text-sm font-semibold text-gray-800 mb-3 flex items-center">
            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-brand-500 to-brand-600 flex items-center justify-center mr-2 shadow-sm">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
              </svg>
            </div>
            Add website URL
          </div>

          <form onSubmit={handleUrlSubmit} className="space-y-3">
            <div className="relative">
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-brand-500">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                </svg>
              </div>
              <input
                ref={urlInputRef}
                type="text"
                value={urlInputValue}
                onChange={(e) => setUrlInputValue(e.target.value)}
                placeholder="Enter website URL"
                className="w-full p-2.5 pl-9 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent text-sm shadow-sm bg-white"
              />
              {urlError && (
                <p className="text-red-500 text-xs mt-1.5 flex items-center bg-red-50 p-1.5 rounded-md">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="12"
                    height="12"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-1.5 flex-shrink-0"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                  </svg>
                  {urlError}
                </p>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <Button
                type="button"
                onClick={() => {
                  setShowUrlInput(false);
                  setUrlInputValue('');
                  setUrlError('');
                }}
                variant="outline"
                className="shadow-sm hover:bg-gray-50 transition-all duration-200"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={!urlInputValue.trim()}
                className="bg-gradient-to-r from-brand-600 to-brand-500 hover:from-brand-700 hover:to-brand-600 transition-all duration-200 shadow-md"
              >
                Done
              </Button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default ChatInput;
