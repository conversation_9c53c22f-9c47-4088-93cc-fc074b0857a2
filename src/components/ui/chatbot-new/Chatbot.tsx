'use client';

import React, { useEffect, useRef, useState } from 'react';
import {
  ChatbotProps,
  ChatMessage as ChatMessageType,
  ChatbotButton,
} from './types';
import { useChatbot } from './ChatbotContext';
import ChatMessage from './ChatMessage';
import ChatInput from './ChatInput';
import ValidationResponseEditor from './ValidationResponseEditor';
import BusinessContextForm from './BusinessContextForm';
import { getDefaultConfig } from './config';
import { apiService } from '@/services/api';
// ThreeDotLoader is now implemented directly in the component
import { FiRefreshCcw } from 'react-icons/fi';
import OptimizedImage from '../image/OptimizedImage';

const Chatbot: React.FC<ChatbotProps> = ({
  config,
  userName,
  userPhotoURL,
  initialMessage,
}) => {
  const { state, dispatch } = useChatbot();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [websiteUrl, setWebsiteUrl] = useState<string>('');
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editingText, setEditingText] = useState<string>('');
  const [editingValidationResponse, setEditingValidationResponse] =
    useState<boolean>(false);
  const [showEditUrlInput, setShowEditUrlInput] = useState<boolean>(false);
  const [urlError, setUrlError] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const urlInputRef = useRef<HTMLInputElement>(null);

  // Use the provided config or generate one based on userName
  const chatbotConfig = config || getDefaultConfig(userName);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [state.messages]);

  // Initialize with welcome message
  useEffect(() => {
    const welcomeNode = chatbotConfig.nodes[chatbotConfig.initialNodeId];

    if (welcomeNode && state.messages.length === 0) {
      const welcomeMessage: ChatMessageType = {
        id: `bot-welcome-${Date.now()}`,
        content:
          initialMessage ||
          (typeof welcomeNode.content === 'function'
            ? welcomeNode.content()
            : welcomeNode.content),
        sender: 'bot',
        timestamp: new Date(),
      };

      dispatch({ type: 'ADD_MESSAGE', payload: welcomeMessage });
      dispatch({
        type: 'SET_CURRENT_NODE',
        payload: chatbotConfig.initialNodeId,
      });
    }
  }, [chatbotConfig, dispatch, state.messages.length, initialMessage]);

  // Focus URL input when modal opens
  useEffect(() => {
    if (showEditUrlInput && urlInputRef.current) {
      urlInputRef.current.focus();
    }
  }, [showEditUrlInput]);

  // Handle escape key to close URL modal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && showEditUrlInput) {
        setShowEditUrlInput(false);
        setUrlError('');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [showEditUrlInput]);

  const scrollToBottom = () => {
    // Use a small timeout to ensure DOM has updated before scrolling
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 1400);
  };

  const handleSendMessage = (message: string) => {
    // Create and add user message
    const userMessage: ChatMessageType = {
      id: `user-${Date.now()}`,
      content: message,
      sender: 'user',
      timestamp: new Date(),
    };

    dispatch({ type: 'ADD_MESSAGE', payload: userMessage });

    // If there's a website URL, we can use it for API calls or other operations
    if (websiteUrl) {
      // If the user is responding to a business prompt, show the appropriate confirmation node
      if (state.currentNodeId === 'businessIdea') {
        // Determine which confirmation node to use
        const confirmNodeId = 'businessIdeaConfirm';
        // Get the next node (confirmation node)
        const nextNode = chatbotConfig.nodes[confirmNodeId];
        if (nextNode) {
          // Create data object with message and website
          const data = {
            message: message,
            website: websiteUrl,
            businessType:
              state.currentNodeId === 'businessIdea' ? 'idea' : 'existing',
          };

          // Create bot message with the confirmation content
          const botMessage: ChatMessageType = {
            id: `bot-${Date.now()}`,
            content:
              typeof nextNode.content === 'function'
                ? nextNode.content(data)
                : nextNode.content,
            sender: 'bot',
            timestamp: new Date(),
            data: data, // Store the data for editing later
            editable: nextNode.editable,
          };

          // Add a small delay to simulate bot thinking
          setTimeout(() => {
            dispatch({ type: 'ADD_MESSAGE', payload: botMessage });
            dispatch({
              type: 'SET_CURRENT_NODE',
              payload: confirmNodeId,
            });
            scrollToBottom();
          }, 500);
        }
      }
    } else {
      // Handle normal message flow without website URL
      if (state.currentNodeId === 'businessIdea') {
        // Determine which confirmation node to use
        const confirmNodeId = 'businessIdeaConfirm';
        // Get the next node (confirmation node)
        const nextNode = chatbotConfig.nodes[confirmNodeId];
        if (nextNode) {
          // Create data object with just the message
          const data = {
            message: message,
            businessType:
              state.currentNodeId === 'businessIdea' ? 'idea' : 'existing',
          };

          // Create bot message with the confirmation content
          const botMessage: ChatMessageType = {
            id: `bot-${Date.now()}`,
            content:
              typeof nextNode.content === 'function'
                ? nextNode.content(data)
                : nextNode.content,
            sender: 'bot',
            timestamp: new Date(),
            data: data, // Store the data for editing later
            editable: nextNode.editable,
          };

          // Add a small delay to simulate bot thinking
          setTimeout(() => {
            dispatch({ type: 'ADD_MESSAGE', payload: botMessage });
            dispatch({
              type: 'SET_CURRENT_NODE',
              payload: confirmNodeId,
            });
            scrollToBottom();

            // Check if the next node has an API call configuration
            const nextNode = chatbotConfig.nodes[confirmNodeId];
            if (nextNode && nextNode.apiCall) {
              handleApiCall(nextNode, [...state.messages, botMessage]);
            }
          }, 500);
        }
      }
    }
  };

  const handleButtonClick = (button: ChatbotButton) => {
    // Handle Start Over button
    if (button.label === 'Start Over' && button.nextNodeId === 'welcome') {
      handleStartOver();
      return;
    }

    if (button.action === 'next' && button.nextNodeId) {
      // Add user message to acknowledge the button click
      const userMessage: ChatMessageType = {
        id: `user-${Date.now()}`,
        content: button.label,
        sender: 'user',
        timestamp: new Date(),
      };

      dispatch({ type: 'ADD_MESSAGE', payload: userMessage });

      // Get the next node
      const nextNode = chatbotConfig.nodes[button.nextNodeId];
      if (nextNode) {
        // Add bot message for the next node
        const botMessage: ChatMessageType = {
          id: `bot-${Date.now()}`,
          content:
            typeof nextNode.content === 'function'
              ? nextNode.content()
              : nextNode.content,
          sender: 'bot',
          timestamp: new Date(),
        };

        // Add a small delay to simulate bot thinking
        setTimeout(() => {
          dispatch({ type: 'ADD_MESSAGE', payload: botMessage });
          dispatch({ type: 'SET_CURRENT_NODE', payload: button.nextNodeId });
          scrollToBottom();

          // Check if the node has an onEnter function
          if (nextNode.onEnter) {
            // Find the most recent editable message (which contains our business idea data)
            const editableMessages = state.messages.filter((msg) => msg.data);
            const lastEditableMessage =
              editableMessages[editableMessages.length - 1];

            nextNode.onEnter(lastEditableMessage?.data || {});
          }

          // Check if the node has an API call configuration
          if (nextNode.apiCall) {
            handleApiCall(nextNode, state.messages);
          }
        }, 500);
      }
    }
  };

  // Function to handle API calls
  const handleApiCall = async (node: any, messages: ChatMessageType[]) => {
    if (!node.apiCall) return;

    // Set loading state to true when API call starts
    setIsLoading(true);
    // Ensure we scroll to show the loading indicator
    scrollToBottom();

    try {
      // Find the most recent user message with data
      const userMessages = messages.filter(
        (msg) => msg.sender === 'user' && msg.data
      );
      const lastUserMessage = userMessages[userMessages.length - 1];

      // Find the most recent editable message (which contains our business idea data)
      const editableMessages = messages.filter(
        (msg) => msg.editable && msg.data
      );
      const lastEditableMessage = editableMessages[editableMessages.length - 1];

      // Use the data from the editable message or the last user message
      const data = lastEditableMessage?.data || lastUserMessage?.data || {};

      // Check if we're in the processBusinessIdea node and need to determine which API to call
      if (node.id === 'processBusinessIdea') {
        try {
          // Check if website was added
          if (data.website) {
            // If website was added, make the save-business-context API call
            const savePayload = {
              projectId: data.id,
              content: {
                website: data.website,
                isWebsite: true,
              },
            };

            apiService.post(
              '/api/business-context/save-business-context',
              savePayload
            );

            if (node.apiCall.onSuccess) {
              node.apiCall.onSuccess({ projectId: data.id }, dispatch);
            }
          } else {
            // If no website, make the get-business-context API call
            const getPayload = {
              projectId: data.id,
              idea: data.description || data.message || '',
              isWebsite: false,
            };

            const getResponse = await apiService.post(
              '/api/business-context/get-business-context',
              getPayload
            );

            if (getResponse.success && node.apiCall.onSuccess) {
              // Create a bot message with the business context form
              const businessContextMessage: ChatMessageType = {
                id: `bot-business-context-${Date.now()}`,
                content: (
                  <div className="p-4">
                    <BusinessContextForm
                      data={{
                        ...(getResponse.data as any),
                        projectId: data.id,
                      }}
                      onSave={(formData) => {
                        // Create a new message with the updated data
                        const updatedMessage: ChatMessageType = {
                          id: `bot-business-context-updated-${Date.now()}`,
                          content: (
                            <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100 shadow-sm">
                              <div className="flex items-center mb-3">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-5 w-5 text-green-500 mr-2"
                                  viewBox="0 0 20 20"
                                  fill="currentColor"
                                >
                                  <path
                                    fillRule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                    clipRule="evenodd"
                                  />
                                </svg>
                                <h3 className="text-lg font-semibold text-green-800">
                                  Business Context Saved
                                </h3>
                              </div>
                              <p className="text-green-700 mb-4">
                                Your business context has been saved
                                successfully. You can now proceed to the
                                dashboard.
                              </p>
                              <div className="flex justify-end">
                                <button
                                  onClick={() =>
                                    (window.location.href = `/home?id=${data.id}`)
                                  }
                                  className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors shadow-sm flex items-center gap-1"
                                >
                                  Go to Dashboard
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                  >
                                    <path
                                      fillRule="evenodd"
                                      d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                                      clipRule="evenodd"
                                    />
                                  </svg>
                                </button>
                              </div>
                            </div>
                          ),
                          sender: 'bot',
                          timestamp: new Date(),
                          data: {
                            ...data,
                            businessContext: formData,
                          },
                        };

                        // Add the updated message
                        dispatch({
                          type: 'ADD_MESSAGE',
                          payload: updatedMessage,
                        });

                        // Call the original onSuccess handler
                        node.apiCall.onSuccess(
                          {
                            ...(getResponse.data as any),
                            businessContext: formData,
                          },
                          dispatch
                        );
                      }}
                    />
                  </div>
                ),
                sender: 'bot',
                timestamp: new Date(),
                data: {
                  ...data,
                  apiResponse: getResponse.data,
                },
              };

              // Add the business context message
              dispatch({
                type: 'ADD_MESSAGE',
                payload: businessContextMessage,
              });
            } else if (!getResponse.success && node.apiCall.onError) {
              node.apiCall.onError(getResponse, dispatch);
            }
          }
        } catch (error) {
          console.error('API call error:', error);
          if (node.apiCall.onError) {
            node.apiCall.onError(error, dispatch);
          }
        } finally {
          // Set loading state to false when API call completes
          setIsLoading(false);
        }
        return; // Exit early since we've handled the API call
      }

      // For other nodes, proceed with the normal API call flow
      // Prepare the payload using the payloadFn if provided
      const payload = node.apiCall.payloadFn
        ? node.apiCall.payloadFn(data)
        : data;

      // Make the API call
      let response;
      switch (node.apiCall.method) {
        case 'GET':
          response = await apiService.get(node.apiCall.endpoint, {
            params: payload,
          });
          break;
        case 'POST':
          response = await apiService.post(node.apiCall.endpoint, payload);
          break;
        case 'PUT':
          response = await apiService.put(node.apiCall.endpoint, payload);
          break;
        case 'DELETE':
          response = await apiService.delete(node.apiCall.endpoint, {
            data: payload,
          });
          break;
        default:
          response = await apiService.post(node.apiCall.endpoint, payload);
      }

      // Handle success
      if (response.success && node.apiCall.onSuccess) {
        node.apiCall.onSuccess(response.data, dispatch);
      } else if (!response.success && node.apiCall.onError) {
        node.apiCall.onError(response, dispatch);
      }
    } catch (error) {
      console.error('API call error:', error);
      if (node.apiCall.onError) {
        node.apiCall.onError(error, dispatch);
      }
    } finally {
      // Set loading state to false when API call completes (success or error)
      setIsLoading(false);
      // Ensure we scroll to the latest message after loading is complete
      setTimeout(() => scrollToBottom(), 100);
    }
  };

  // Function to handle starting over
  const handleStartOver = () => {
    // Clear all messages
    dispatch({ type: 'SET_MESSAGES', payload: [] });

    // Reset website URL
    setWebsiteUrl('');

    // Reset to initial node
    dispatch({
      type: 'SET_CURRENT_NODE',
      payload: chatbotConfig.initialNodeId,
    });

    // Add welcome message again
    const welcomeNode = chatbotConfig.nodes[chatbotConfig.initialNodeId];
    if (welcomeNode) {
      const welcomeMessage: ChatMessageType = {
        id: `bot-welcome-${Date.now()}`,
        content:
          initialMessage ||
          (typeof welcomeNode.content === 'function'
            ? welcomeNode.content()
            : welcomeNode.content),
        sender: 'bot',
        timestamp: new Date(),
      };

      dispatch({ type: 'ADD_MESSAGE', payload: welcomeMessage });
    }
  };

  // Get current node to determine bottom bar content
  const currentNode = chatbotConfig.nodes[state.currentNodeId];

  // Check if the last message has a bottom bar
  const lastMessage = state.messages[state.messages.length - 1];
  const hasMessageBottomBar =
    lastMessage?.bottomBar?.buttons && lastMessage.bottomBar.buttons.length > 0;

  // Use message bottom bar if available, otherwise use the current node's bottom bar
  const hasBottomBarButtons =
    hasMessageBottomBar ||
    (currentNode?.bottomBar?.buttons &&
      currentNode.bottomBar.buttons.length > 0);

  return (
    <div className="flex flex-col h-full bg-gradient-to-b from-gray-50 to-white rounded-lg shadow-xl overflow-hidden border border-gray-200">
      {/* Header with AI Assistant branding */}

      {/* Messages with enhanced styling */}
      <div
        className="flex-1 p-6 mt-20 overflow-y-auto relative"
        style={{
          backgroundImage:
            'radial-gradient(circle at 50% 50%, rgba(249, 250, 251, 0.8) 0%, rgba(255, 255, 255, 0.8) 100%)',
        }}
      >
        {/* Messages are rendered here */}
        {state.messages.map((message, _, messages) => {
          // Calculate if this is the last bot message
          const botMessages = messages.filter((msg) => msg.sender === 'bot');
          const isLastBotMessage =
            message.sender === 'bot' &&
            botMessages.indexOf(message) === botMessages.length - 1;

          return (
            <div key={message.id} className="relative group">
              {editingMessageId === message.id && !editingValidationResponse ? (
                <div className="flex items-start justify-start mb-4 gap-2">
                  {message.sender === 'bot' && (
                    <div className="flex-shrink-0 w-8 h-8 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                      <OptimizedImage
                        height={40}
                        width={40}
                        src="/images/icons/dolze-icon-black.svg"
                        alt="Bot"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}

                  <div className="max-w-[80%] p-3 rounded-lg bg-white text-gray-800 rounded-tl-none shadow-sm border border-gray-100 relative">
                    <div className="mb-3">
                      <p className="text-gray-700 font-medium mb-2">
                        Your updated input data
                      </p>
                      <textarea
                        value={editingText}
                        onChange={(e) => setEditingText(e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm min-h-[80px]"
                        placeholder="Describe your business idea..."
                      />
                    </div>

                    <div className="mb-3">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-sm text-gray-700">
                          Website URL:
                        </span>
                        <button
                          onClick={() => setShowEditUrlInput(true)}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          {websiteUrl ? 'Edit' : 'Add'} URL
                        </button>
                      </div>

                      {websiteUrl && (
                        <div className="flex items-center text-sm text-blue-600 bg-blue-50 p-2 rounded-md">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="12"
                            height="12"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="mr-1"
                          >
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                          </svg>
                          <span className="truncate">{websiteUrl}</span>
                        </div>
                      )}
                    </div>

                    <div className="flex justify-end gap-2">
                      <button
                        onClick={() => {
                          setEditingMessageId(null);
                          setEditingText('');
                          setWebsiteUrl('');
                          // Don't reset website URL here as it might be needed for other operations
                        }}
                        className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={() => {
                          // Update the message with edited content
                          const updatedData = {
                            message: editingText,
                            website: websiteUrl,
                            businessType: message.data?.businessType || 'idea',
                          };

                          // Find the node to get the content function
                          const nodeId = 'businessIdeaConfirm'; // We only want to edit the business idea confirm node
                          const node = chatbotConfig.nodes[nodeId];

                          // Create updated message
                          const updatedMessage: ChatMessageType = {
                            ...message,
                            content:
                              typeof node.content === 'function'
                                ? node.content(updatedData)
                                : node.content,
                            data: updatedData,
                          };

                          // Update the message in the state
                          dispatch({
                            type: 'UPDATE_MESSAGE',
                            payload: {
                              id: message.id,
                              message: updatedMessage,
                            },
                          });

                          // Reset editing state
                          setEditingMessageId(null);
                          setEditingText('');
                        }}
                        className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
                        disabled={!editingText.trim()}
                      >
                        Save Changes
                      </button>
                    </div>
                  </div>
                </div>
              ) : editingMessageId === message.id &&
                editingValidationResponse ? (
                <div className="flex items-start justify-start mb-4 gap-2">
                  {message.sender === 'bot' && (
                    <div className="flex-shrink-0 w-8 h-8 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                      <OptimizedImage
                        height={40}
                        width={40}
                        src="/images/icons/dolze-icon-black.svg"
                        alt="Bot"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <div className="max-w-[80%]">
                    <ValidationResponseEditor
                      initialData={{
                        ...message.data,
                        // If the API response data is stored directly in the message data
                        ...(message.data?.is_valid_idea !== undefined
                          ? message.data
                          : {}),
                        // If the API response data is stored in a nested property
                        ...(message.data?.apiResponse || {}),
                      }}
                      messageId={message.id}
                      onSave={async (messageId, editedData) => {
                        try {
                          // Show loading state
                          const loadingMessage: ChatMessageType = {
                            ...message,
                            content: (
                              <div className="p-4 bg-white rounded-lg border border-gray-200 shadow-sm relative">
                                <div className="flex items-center justify-center space-x-2">
                                  <svg
                                    className="animate-spin h-5 w-5 text-blue-600"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                  >
                                    <circle
                                      className="opacity-25"
                                      cx="12"
                                      cy="12"
                                      r="10"
                                      stroke="currentColor"
                                      strokeWidth="4"
                                    ></circle>
                                    <path
                                      className="opacity-75"
                                      fill="currentColor"
                                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                    ></path>
                                  </svg>
                                  <span className="text-gray-700">
                                    Updating your business information...
                                  </span>
                                </div>
                              </div>
                            ),
                          };

                          // Update the message to show loading state
                          dispatch({
                            type: 'UPDATE_MESSAGE',
                            payload: {
                              id: messageId,
                              message: loadingMessage,
                            },
                          });

                          // Prepare API payload
                          const apiPayload = {
                            prompt: editedData.description || '',
                            website: editedData.website || '',
                            id: editedData.id, // Include the original ID
                            name: editedData.name,
                            title: editedData.title,
                            description: editedData.description,
                            tags: editedData.tags,
                            business_idea_config:
                              editedData.business_idea_config,
                          };

                          // Make API call to validate idea with the edited data
                          const response = await apiService.post<any>(
                            '/api/validate-idea',
                            apiPayload
                          );

                          if (
                            !response.success ||
                            (response.data &&
                              typeof response.data === 'object' &&
                              'detail' in response.data)
                          ) {
                            throw new Error(
                              response.message ||
                                (response.data &&
                                typeof response.data === 'object'
                                  ? response.data.detail
                                  : undefined) ||
                                'Failed to validate idea'
                            );
                          }

                          // Create updated message with the new validation result
                          const updatedMessage: ChatMessageType = {
                            ...message,
                            content: (
                              <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100 shadow-sm relative">
                                <div className="flex items-center mb-3">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="20"
                                    height="20"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="#10B981"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    className="mr-2"
                                  >
                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                  </svg>
                                  <h3 className="text-lg font-semibold text-green-700">
                                    Great news! Your business idea is valid
                                  </h3>
                                </div>

                                <div className="mb-3 bg-white p-3 rounded-md border border-green-100">
                                  <h4 className="font-medium text-green-800 mb-1">
                                    {response.data.title}
                                  </h4>
                                  <p className="text-gray-700 text-sm">
                                    {response.data.description}
                                  </p>
                                </div>

                                <div className="grid grid-cols-2 gap-2 mb-3">
                                  <div className="bg-white p-2 rounded border border-green-100">
                                    <p className="text-xs text-gray-500">
                                      Business Type
                                    </p>
                                    <p className="text-sm font-medium text-gray-800 capitalize">
                                      {response.data.business_idea_config
                                        ?.biz_type || 'N/A'}
                                    </p>
                                  </div>
                                  <div className="bg-white p-2 rounded border border-green-100">
                                    <p className="text-xs text-gray-500">
                                      Offering
                                    </p>
                                    <p className="text-sm font-medium text-gray-800 capitalize">
                                      {response.data.business_idea_config
                                        ?.offering || 'N/A'}
                                    </p>
                                  </div>
                                </div>

                                <div className="flex flex-wrap gap-1 mb-3">
                                  {response.data.tags &&
                                    response.data.tags.map(
                                      (tag: string, index: number) => (
                                        <span
                                          key={index}
                                          className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full"
                                        >
                                          {tag}
                                        </span>
                                      )
                                    )}
                                </div>

                                {response.data.website && (
                                  <div className="flex items-center text-sm text-green-600 mb-2">
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      width="12"
                                      height="12"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      className="mr-1"
                                    >
                                      <circle cx="12" cy="12" r="10"></circle>
                                      <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                                    </svg>
                                    <a
                                      href={response.data.website}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="hover:underline"
                                    >
                                      {response.data.website}
                                    </a>
                                  </div>
                                )}
                              </div>
                            ),
                            data: response.data as any,
                            editable: true,
                            bottomBar: {
                              buttons: [
                                {
                                  label: 'Continue',
                                  action: 'next',
                                  nextNodeId: 'processBusinessIdea',
                                  variant: 'primary',
                                },
                              ],
                            },
                          };

                          // Update the message in the state
                          dispatch({
                            type: 'UPDATE_MESSAGE',
                            payload: {
                              id: messageId,
                              message: updatedMessage,
                            },
                          });
                        } catch (error: any) {
                          console.error('Error updating validation:', error);

                          // Show error message
                          const errorMessage: ChatMessageType = {
                            id: `bot-error-${Date.now()}`,
                            content: (
                              <div className="p-3 bg-red-50 rounded-lg border border-red-100">
                                <div className="flex items-center mb-2">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="#DC2626"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    className="mr-2"
                                  >
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" y1="8" x2="12" y2="12"></line>
                                    <line
                                      x1="12"
                                      y1="16"
                                      x2="12.01"
                                      y2="16"
                                    ></line>
                                  </svg>
                                  <h3 className="font-medium text-red-700">
                                    Update Failed
                                  </h3>
                                </div>
                                <p className="text-red-600 text-sm">
                                  {error?.message ||
                                    error?.error ||
                                    'Failed to update your business information. Please try again.'}
                                </p>
                              </div>
                            ),
                            sender: 'bot',
                            timestamp: new Date(),
                          };

                          dispatch({
                            type: 'ADD_MESSAGE',
                            payload: errorMessage,
                          });
                        } finally {
                          // Reset editing state
                          setEditingMessageId(null);
                          setEditingValidationResponse(false);
                        }
                      }}
                      onCancel={() => {
                        setEditingMessageId(null);
                        setEditingValidationResponse(false);
                      }}
                    />
                  </div>
                </div>
              ) : (
                <ChatMessage
                  message={message}
                  userPhotoURL={userPhotoURL}
                  isLastBotMessage={isLastBotMessage}
                  onEditClick={(messageId) => {
                    let isValidationResponse = false;

                    // Method 1: Check if the message ID contains 'success'
                    if (message.id && message.id.includes('bot-success')) {
                      isValidationResponse = true;
                    }

                    // Additional check: Look for specific properties in the data
                    if (!isValidationResponse && message.data) {
                      if (
                        message.data.is_valid_idea !== undefined ||
                        message.data.title ||
                        message.data.tags ||
                        message.data.business_idea_config
                      ) {
                        isValidationResponse = true;
                      }
                    }

                    // Method 2: Check if the message content has specific className
                    if (
                      !isValidationResponse &&
                      typeof message.content !== 'string' &&
                      message.editable === true &&
                      message.content &&
                      React.isValidElement(message.content)
                    ) {
                      // Try to access the className property safely
                      const props = message.content.props as any;

                      // Check if this is a validation response by looking for specific elements in the content
                      if (
                        props &&
                        props.className &&
                        typeof props.className === 'string' &&
                        (props.className.includes(
                          'from-green-50 to-emerald-50'
                        ) ||
                          props.className.includes('bg-gradient-to-r'))
                      ) {
                        isValidationResponse = true;
                      }
                    }

                    // Method 3: Check for specific children in the content
                    if (
                      !isValidationResponse &&
                      typeof message.content !== 'string' &&
                      React.isValidElement(message.content)
                    ) {
                      const props = message.content.props as any;
                      if (props && props.children) {
                        // Look for text that indicates this is a validation response
                        const contentStr = JSON.stringify(props.children);
                        if (
                          contentStr.includes('Great news') ||
                          contentStr.includes('business idea is valid')
                        ) {
                          isValidationResponse = true;
                        }
                      }
                    }

                    if (isValidationResponse) {
                      setEditingMessageId(messageId);
                      setEditingValidationResponse(true);
                    } else {
                      setEditingMessageId(messageId);
                      setEditingText(message.data?.message || '');
                      setWebsiteUrl(message.data?.website || '');
                    }
                  }}
                />
              )}
            </div>
          );
        })}
        {/* Enhanced loading indicator - positioned as a message for better scrolling */}
        {isLoading && (
          <div className="my-4 flex items-start justify-start">
            <div className="flex-shrink-0 w-10 h-10 rounded-full overflow-hidden bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-md border-2 border-white mr-2">
              <OptimizedImage
                src="/images/icons/dolze-icon-black.svg"
                alt="AI Assistant"
                className="w-7 h-7 object-contain filter brightness-0 invert"
                height={40}
                width={40}
              />
            </div>
            <div className="max-w-[80%] p-3.5 rounded-lg bg-gradient-to-r from-white to-gray-50 text-gray-800 rounded-tl-none shadow-md border border-gray-100">
              <div className="flex space-x-1.5 px-2 py-1.5">
                <div
                  className="w-2.5 h-2.5 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full animate-bounce"
                  style={{ animationDelay: '0ms' }}
                />
                <div
                  className="w-2.5 h-2.5 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full animate-bounce"
                  style={{ animationDelay: '150ms' }}
                />
                <div
                  className="w-2.5 h-2.5 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full animate-bounce"
                  style={{ animationDelay: '300ms' }}
                />
              </div>
            </div>
          </div>
        )}
        {/* Always keep the messagesEndRef at the very end with height to ensure it's visible */}
        <div ref={messagesEndRef} className="h-4" />
      </div>

      {/* URL Edit Popover */}
      {showEditUrlInput && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div
            className="bg-white rounded-lg p-4 w-80 shadow-lg animate-fadeIn"
            onClick={(e) => e.stopPropagation()}
          >
            <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2 text-blue-500"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
              </svg>
              {websiteUrl ? 'Edit' : 'Add'} Website URL
            </h3>

            <div className="mb-4">
              <input
                ref={urlInputRef}
                type="text"
                value={websiteUrl}
                onChange={(e) => setWebsiteUrl(e.target.value)}
                placeholder="Enter website URL"
                className={`w-full p-2 border ${urlError ? 'border-red-300 bg-red-50' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm`}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    document.getElementById('save-url-button')?.click();
                  }
                }}
              />
              {urlError ? (
                <p className="text-xs text-red-500 mt-1 flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="12"
                    height="12"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-1"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                  </svg>
                  {urlError}
                </p>
              ) : (
                <p className="text-xs text-gray-500 mt-1">
                  Example: https://example.com
                </p>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => {
                  setShowEditUrlInput(false);
                  setWebsiteUrl('');
                }}
                className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                id="save-url-button"
                onClick={() => {
                  // Validate URL using the same validation as in ChatInput
                  if (websiteUrl.trim()) {
                    // If URL doesn't start with http:// or https://, prepend https://
                    let processedUrl = websiteUrl.trim();
                    if (!processedUrl.match(/^https?:\/\//)) {
                      processedUrl = `https://${processedUrl}`;
                    }

                    // Basic URL validation
                    try {
                      const urlObj = new URL(processedUrl);

                      // Additional validation: ensure there's a valid domain with at least one dot
                      if (!urlObj.hostname || !urlObj.hostname.includes('.')) {
                        setUrlError('Please enter a valid domain name');
                        return;
                      }

                      // URL is valid
                      setUrlError('');
                      setWebsiteUrl(processedUrl);
                      setShowEditUrlInput(false);
                    } catch (err) {
                      console.error(err);
                      setUrlError('Please enter a valid URL');
                    }
                  } else {
                    // Empty URL is allowed
                    setWebsiteUrl('');
                    setUrlError('');
                    setShowEditUrlInput(false);
                  }
                }}
                className="px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Bottom Bar with enhanced styling */}
      <div className="bg-white border-t border-gray-200 shadow-inner">
        {hasBottomBarButtons ? (
          <div className="p-4 flex flex-wrap gap-3 justify-center">
            {/* If last message has a bottom bar, use that */}
            {hasMessageBottomBar
              ? // Render buttons from the last message with enhanced styling
                lastMessage.bottomBar?.buttons?.map((button, index) => (
                  <button
                    key={`${button.label}-${index}`}
                    onClick={() => handleButtonClick(button)}
                    className={`px-5 py-2.5 rounded-md text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg active:scale-95 ${
                      button.variant === 'primary'
                        ? 'bg-gradient-to-r from-brand-600 to-brand-500 text-white hover:from-brand-700 hover:to-brand-600'
                        : button.variant === 'secondary'
                          ? 'bg-gradient-to-r from-indigo-500 to-indigo-600 text-white hover:from-indigo-600 hover:to-indigo-700'
                          : 'bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    {button.icon && <span className="mr-2">{button.icon}</span>}
                    {button.label}
                  </button>
                ))
              : // Otherwise use the current node's bottom bar with enhanced styling
                currentNode.bottomBar?.buttons?.map((button, index) => (
                  <button
                    key={`${button.label}-${index}`}
                    onClick={() => handleButtonClick(button)}
                    className={`px-5 py-2.5 rounded-md text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg active:scale-95 ${
                      button.variant === 'primary'
                        ? 'bg-gradient-to-r from-brand-600 to-brand-500 text-white hover:from-brand-700 hover:to-brand-600'
                        : button.variant === 'secondary'
                          ? 'bg-gradient-to-r from-indigo-500 to-indigo-600 text-white hover:from-indigo-600 hover:to-indigo-700'
                          : 'bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    {button.icon && <span className="mr-2">{button.icon}</span>}
                    {button.label}
                  </button>
                ))}

            {/* Add Start Over button to all nodes except welcome - with enhanced styling */}
            {state.currentNodeId !== chatbotConfig.initialNodeId && (
              <button
                onClick={handleStartOver}
                className="px-5 py-2.5 rounded-md text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg active:scale-95 bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-gray-900 flex items-center gap-2"
              >
                <FiRefreshCcw className="text-brand-500" />
                Start Over
              </button>
            )}
          </div>
        ) : (
          <ChatInput
            onSendMessage={handleSendMessage}
            onWebsiteAdded={(url) => setWebsiteUrl(url)}
            handleStartOver={handleStartOver}
          />
        )}
      </div>
    </div>
  );
};

export default Chatbot;
