'use client';

import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { ChatMessage } from './types';

// Define the state shape
interface ChatbotState {
  messages: ChatMessage[];
  currentNodeId: string;
}

// Define action types
type ChatbotAction =
  | { type: 'ADD_MESSAGE'; payload: ChatMessage }
  | { type: 'SET_MESSAGES'; payload: ChatMessage[] }
  | { type: 'SET_CURRENT_NODE'; payload: string }
  | { type: 'UPDATE_MESSAGE'; payload: { id: string; message: ChatMessage } }
  | { type: 'RESET' };

// Define the context shape
interface ChatbotContextType {
  state: ChatbotState;
  dispatch: React.Dispatch<ChatbotAction>;
}

// Create the context
const ChatbotContext = createContext<ChatbotContextType | undefined>(undefined);

// Initial state
const initialState: ChatbotState = {
  messages: [],
  currentNodeId: 'welcome',
};

// Reducer function
function chatbotReducer(
  state: ChatbotState,
  action: ChatbotAction
): ChatbotState {
  switch (action.type) {
    case 'ADD_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload],
      };
    case 'SET_MESSAGES':
      return {
        ...state,
        messages: action.payload,
      };
    case 'SET_CURRENT_NODE':
      return {
        ...state,
        currentNodeId: action.payload,
      };
    case 'UPDATE_MESSAGE':
      return {
        ...state,
        messages: state.messages.map((msg) =>
          msg.id === action.payload.id ? action.payload.message : msg
        ),
      };
    case 'RESET':
      return initialState;
    default:
      return state;
  }
}

// Provider component
export function ChatbotProvider({
  children,
  initialNodeId,
}: {
  children: ReactNode;
  initialNodeId?: string;
}) {
  const [state, dispatch] = useReducer(chatbotReducer, {
    ...initialState,
    currentNodeId: initialNodeId || initialState.currentNodeId,
  });

  return (
    <ChatbotContext.Provider value={{ state, dispatch }}>
      {children}
    </ChatbotContext.Provider>
  );
}

// Custom hook to use the chatbot context
export function useChatbot() {
  const context = useContext(ChatbotContext);
  if (context === undefined) {
    throw new Error('useChatbot must be used within a ChatbotProvider');
  }
  return context;
}
