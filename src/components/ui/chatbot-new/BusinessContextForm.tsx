'use client';

import React, { useState } from 'react';
import { apiService } from '@/services/api';

interface Option {
  option: string;
  selected: boolean;
}

interface Question {
  question: string;
  options: Option[];
}

interface BusinessContextResponse {
  idea_analysis: string;
  questions: Question[];
  projectId?: string;
}

interface BusinessContextFormProps {
  data: BusinessContextResponse;
  onSave: (updatedData: any) => void;
}

const BusinessContextForm: React.FC<BusinessContextFormProps> = ({
  data,
  onSave,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<Question[]>(data.questions);
  const [comments, setComments] = useState<string[]>(
    Array(data?.questions?.length).fill('')
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleOptionChange = (questionIndex: number, optionIndex: number) => {
    const updatedFormData = [...formData];

    // Allow multiple selections for all questions
    updatedFormData[questionIndex].options[optionIndex].selected =
      !updatedFormData[questionIndex].options[optionIndex].selected;

    setFormData(updatedFormData);
  };

  const handleCommentChange = (questionIndex: number, value: string) => {
    const updatedComments = [...comments];
    updatedComments[questionIndex] = value;
    setComments(updatedComments);
  };

  const handleNext = () => {
    if (currentStep < formData?.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    // Format the data for submission
    const formattedData = {
      questions: formData.map((question, index) => ({
        question: question.question,
        options: question.options
          .filter((opt) => opt.selected)
          .map((opt) => opt.option),
        comment: comments[index],
      })),
    };

    try {
      // Get the projectId from the data
      const projectId = data.projectId || '';

      // Make API call to save business context
      apiService.post('/api/business-context/save-business-context', {
        projectId: projectId,
        content: formattedData,
      });

      // Call the onSave callback with the formatted data
      onSave(formattedData);
    } catch (error) {
      console.error('Error saving business context:', error);
      // Still call onSave to allow the UI to proceed
      onSave(formattedData);
    } finally {
      setIsSubmitting(false);
    }
  };

  const progressPercentage = ((currentStep + 1) / formData?.length) * 100;
  if (!formData) {
    return <div> Oops some error occured</div>;
  }
  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden w-[600px] mx-auto">
      {/* Header with progress bar */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-semibold text-gray-800">
            Business Context
          </h3>
          <span className="text-sm text-gray-500">
            Step {currentStep + 1} of {formData?.length}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5 overflow-hidden">
          <div
            className="bg-gradient-to-r from-brand-500 to-brand-600 h-2.5 rounded-full transition-all duration-300 ease-in-out shadow-inner"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Form content */}
      <div className="p-5 min-h-[400px]">
        <div className="mb-4">
          <h4 className="text-md font-medium text-gray-800 mb-2">
            {formData[currentStep]?.question}
          </h4>
          <p className="text-sm text-gray-500 mb-3 flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1 text-brand-500"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
            You can select multiple options
          </p>

          <div className="space-y-2 mb-4">
            {formData[currentStep].options.map((option, optionIndex) => (
              <div
                key={optionIndex}
                className={`p-3 border rounded-md cursor-pointer transition-all ${
                  option.selected
                    ? 'border-brand-500 bg-brand-50 text-brand-700'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
                onClick={() => handleOptionChange(currentStep, optionIndex)}
              >
                <div className="flex items-center">
                  <div
                    className={`w-5 h-5 rounded border flex items-center justify-center mr-3 ${
                      option.selected
                        ? 'border-brand-500 bg-brand-500'
                        : 'border-gray-300'
                    }`}
                  >
                    {option.selected && (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3 w-3 text-white"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                  </div>
                  <span className="text-sm">{option.option}</span>
                </div>
              </div>
            ))}
          </div>

          {/* Additional comments */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Additional comments (optional)
            </label>
            <textarea
              value={comments[currentStep]}
              onChange={(e) => handleCommentChange(currentStep, e.target.value)}
              placeholder="Add any additional thoughts or context..."
              className="w-full p-3 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-brand-500 min-h-[80px]"
            />
          </div>
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="p-4 border-t border-gray-200 flex justify-between">
        <button
          type="button"
          onClick={handlePrevious}
          disabled={currentStep === 0}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
        >
          Previous
        </button>

        {currentStep < formData.length - 1 ? (
          <button
            type="button"
            onClick={handleNext}
            className="px-4 py-2 bg-brand-600 text-white rounded-md text-sm font-medium hover:bg-brand-700"
          >
            Next
          </button>
        ) : (
          <button
            type="button"
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="px-4 py-2 bg-brand-600 text-white rounded-md text-sm font-medium hover:bg-brand-700 disabled:opacity-70 disabled:cursor-not-allowed flex items-center gap-2"
          >
            {isSubmitting ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Saving...
              </>
            ) : (
              'Save'
            )}
          </button>
        )}
      </div>
    </div>
  );
};

export default BusinessContextForm;
