export interface ChatMessage {
  id: string;
  content: string | React.ReactNode;
  sender: 'user' | 'bot' | 'system';
  timestamp: Date;
  data?: {
    message?: string;
    website?: string;
    [key: string]: any;
  };
  editable?: boolean;
  bottomBar?: BottomBarConfig;
}

export interface ChatbotConfig {
  initialNodeId: string;
  nodes: {
    [key: string]: ChatbotNode;
  };
}

export interface ChatbotButton {
  label: string;
  action: 'next' | 'api';
  nextNodeId: string;
  variant?: 'primary' | 'outline' | 'secondary';
  icon?: React.ReactNode;
  apiEndpoint?: string;
  apiPayloadFn?: (data: any) => any;
}

export interface BottomBarConfig {
  buttons?: ChatbotButton[];
}

export interface ChatbotNode {
  id: string;
  type: 'message';
  content: string | React.ReactNode | ((data?: any) => React.ReactNode);
  bottomBar?: BottomBarConfig;
  editable?: boolean;
  onEnter?: (data: any) => void;
  apiCall?: {
    endpoint: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    payloadFn?: (data: any) => any;
    onSuccess?: (response: any, dispatch: any) => void;
    onError?: (error: any, dispatch: any) => void;
  };
}

export interface ChatbotProps {
  config?: ChatbotConfig;
  userPhotoURL?: string;
  userName: string;
  initialMessage?: string | React.ReactNode;
}
