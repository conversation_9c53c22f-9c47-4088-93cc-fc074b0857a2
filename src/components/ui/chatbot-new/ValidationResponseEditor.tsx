'use client';

import React, { useState, useEffect } from 'react';
import Button from '../button/Button';

interface ValidationData {
  id?: string;
  title?: string;
  description?: string;
  name?: string;
  tags?: string[];
  website?: string;
  business_idea_config?: {
    biz_type?: string;
    offering?: string;
  };
  [key: string]: any;
}

interface ValidationResponseEditorProps {
  initialData: ValidationData;
  messageId: string;
  onSave: (messageId: string, data: ValidationData) => void;
  onCancel: () => void;
}

const ValidationResponseEditor: React.FC<ValidationResponseEditorProps> = ({
  initialData,
  messageId,
  onSave,
  onCancel,
}) => {
  // Local state to manage form values
  const [formData, setFormData] = useState<ValidationData>({
    title: '',
    description: '',
    name: '',
    tags: [],
    website: '',
    business_idea_config: {
      biz_type: '',
      offering: '',
    },
  });

  // Initialize form data from initialData
  useEffect(() => {
    console.log('Initial data in editor:', initialData);

    // Extract data from the initialData, which might have different structures
    // depending on how it was created
    const extractedData = {
      id: initialData.id || '',
      title: initialData.title || '',
      description: initialData.description || initialData.message || '',
      name: initialData.name || '',
      tags: initialData.tags || [],
      website: initialData.website || '',
      business_idea_config: {
        biz_type: initialData.business_idea_config?.biz_type || '',
        offering: initialData.business_idea_config?.offering || '',
      },
    };

    console.log('Extracted data for form:', extractedData);
    setFormData(extractedData);
  }, [initialData]);

  // Handle input changes
  const handleInputChange = (field: keyof ValidationData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle nested input changes (for business_idea_config)
  const handleNestedInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      business_idea_config: {
        ...prev.business_idea_config,
        [field]: value,
      },
    }));
  };

  // Tags are handled directly in the input onChange

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Preserve all original data fields and only update the edited ones
    onSave(messageId, formData);
  };

  return (
    <div className="p-5 bg-gradient-to-r from-white to-gray-50 rounded-lg border border-gray-200 shadow-lg relative">
      <div className="flex items-center justify-between mb-5">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center mr-3 shadow-md">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </div>
          <div>
            <h3 className="text-base font-bold text-gray-800">
              Edit your business information
            </h3>
            <p className="text-xs text-gray-500 mt-0.5">
              Update your details to get better recommendations
            </p>
          </div>
        </div>
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 px-3 py-1.5 rounded-full text-xs font-medium text-green-700 border border-green-100 shadow-sm">
          Editing
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-5">
        {/* Title and description section */}
        <div className="mb-4 p-4 rounded-lg border border-gray-200 bg-white shadow-sm">
          <div className="flex items-center mb-3">
            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-brand-500 to-brand-600 flex items-center justify-center mr-2 shadow-sm">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
              </svg>
            </div>
            <h4 className="text-sm font-semibold text-gray-800">
              Basic Information
            </h4>
          </div>

          <div className="mb-4">
            <label className="font-medium text-gray-700 text-xs mb-1.5 block">
              Title
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              className="w-full rounded-md border border-gray-300 p-2.5 text-sm focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent font-medium text-gray-800 bg-white shadow-sm transition-all duration-200"
            />
          </div>
          <div>
            <label className="font-medium text-gray-700 text-xs mb-1.5 block">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className="w-full rounded-md border border-gray-300 p-2.5 text-sm focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent text-gray-800 bg-white min-h-[100px] shadow-sm transition-all duration-200"
              rows={4}
            />
          </div>
        </div>

        {/* Business details section */}
        <div className="mb-4 p-4 rounded-lg border border-gray-200 bg-white shadow-sm">
          <div className="flex items-center mb-3">
            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-indigo-500 to-indigo-600 flex items-center justify-center mr-2 shadow-sm">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M20 7h-9"></path>
                <path d="M14 17H5"></path>
                <circle cx="17" cy="17" r="3"></circle>
                <circle cx="7" cy="7" r="3"></circle>
              </svg>
            </div>
            <h4 className="text-sm font-semibold text-gray-800">
              Business Details
            </h4>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="font-medium text-gray-700 text-xs mb-1.5 block">
                Business Type
              </label>
              <input
                type="text"
                value={formData.business_idea_config?.biz_type || ''}
                onChange={(e) =>
                  handleNestedInputChange('biz_type', e.target.value)
                }
                className="w-full rounded-md border border-gray-300 p-2.5 text-sm focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent font-medium text-gray-800 bg-white shadow-sm transition-all duration-200"
              />
            </div>
            <div>
              <label className="font-medium text-gray-700 text-xs mb-1.5 block">
                Offering
              </label>
              <input
                type="text"
                value={formData.business_idea_config?.offering || ''}
                onChange={(e) =>
                  handleNestedInputChange('offering', e.target.value)
                }
                className="w-full rounded-md border border-gray-300 p-2.5 text-sm focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent font-medium text-gray-800 bg-white shadow-sm transition-all duration-200"
              />
            </div>
          </div>
        </div>

        {/* Website section */}
        <div className="mb-4 p-4 rounded-lg border border-gray-200 bg-white shadow-sm">
          <div className="flex items-center mb-3">
            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center mr-2 shadow-sm">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
              </svg>
            </div>
            <h4 className="text-sm font-semibold text-gray-800">Website</h4>
          </div>

          <div>
            <label className="font-medium text-gray-700 text-xs mb-1.5 block">
              Website URL
            </label>
            <div className="relative">
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10a15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                </svg>
              </div>
              <input
                type="text"
                value={formData.website || ''}
                onChange={(e) => handleInputChange('website', e.target.value)}
                className="w-full rounded-md border border-gray-300 p-2.5 pl-9 text-sm focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent font-medium text-gray-800 bg-white shadow-sm transition-all duration-200"
                placeholder="https://example.com"
              />
            </div>
          </div>
        </div>

        {/* Name field - hidden at bottom since it's less commonly edited */}
        <div className="hidden">
          <label className="font-medium text-gray-700 text-xs">Name</label>
          <input
            type="text"
            value={formData.name || ''}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className="w-full rounded-md border border-gray-300 p-2.5 text-sm focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent font-medium text-gray-800 bg-white shadow-sm transition-all duration-200"
          />
        </div>

        <div className="flex justify-end space-x-3 pt-4 mt-2">
          <Button
            type="button"
            onClick={onCancel}
            variant="outline"
            className="px-5 py-2.5 shadow-sm hover:bg-gray-50 transition-all duration-200"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            className="px-5 py-2.5 bg-gradient-to-r from-brand-600 to-brand-500 hover:from-brand-700 hover:to-brand-600 transition-all duration-200 shadow-md"
          >
            Save Changes
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ValidationResponseEditor;
