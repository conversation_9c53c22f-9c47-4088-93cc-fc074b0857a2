import React, { ReactNode } from 'react';
import { useSampleProject } from '@/hooks/useSampleProject';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode; // Button text or content
  size?: 'sm' | 'md' | 'lg'; // Button size
  variant?: 'primary' | 'outline' | 'danger' | 'success' | 'warning'; // Button variant
  startIcon?: ReactNode; // Icon before the text
  endIcon?: ReactNode; // Icon after the text
  fullWidth?: boolean; // Full width button
  isLoading?: boolean; // Loading state
  className?: string; // Additional classes
  ignoreSampleProject?: boolean; // Whether to ignore the sample project check
}

const Button: React.FC<ButtonProps> = ({
  children,
  size = 'md',
  variant = 'primary',
  startIcon,
  endIcon,
  fullWidth = false,
  isLoading = false,
  className = '',
  disabled = false,
  ignoreSampleProject = false,
  ...props
}) => {
  // Check if the current project is a sample project
  const isSampleProject = useSampleProject();

  // Disable the button if this is a sample project and we're not ignoring the check
  const isDisabled =
    disabled || isLoading || (isSampleProject && !ignoreSampleProject);
  // Size Classes
  const sizeClasses = {
    sm: 'px-3 py-2 text-heading-3 font-medium',
    md: 'px-4 py-2.5 text-heading-3 font-medium',
    lg: 'px-5 py-3 text-heading-3 font-medium',
  };

  // Variant Classes
  const variantClasses = {
    primary:
      'bg-gradient-to-r from-[#0061ff] to-[#60efff] text-white shadow-sm hover:from-[#0061ff] hover:to-[#60efff] hover:opacity-90 active:opacity-80 disabled:opacity-50 transition-all duration-200 font-medium',
    outline:
      'bg-white border border-primary-300 text-primary-600 hover:border-primary-500 hover:text-primary-700 active:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:text-primary-400 dark:hover:bg-gray-700 dark:hover:border-primary-500 dark:hover:text-primary-300 dark:active:bg-gray-600 transition-all duration-200 font-medium relative after:absolute after:inset-0 after:rounded-lg after:opacity-0 hover:after:opacity-10 after:bg-gradient-to-r after:from-primary-300 after:to-primary-500 after:transition-opacity after:duration-200',
    danger:
      'bg-gradient-to-r from-error-500 to-error-600 text-white hover:from-error-600 hover:to-error-700 active:from-error-700 active:to-error-800 focus:ring-error-500/20 transition-all duration-200 font-medium',
    success:
      'bg-gradient-to-r from-success-500 to-success-600 text-white hover:from-success-600 hover:to-success-700 active:from-success-700 active:to-success-800 focus:ring-success-500/20 transition-all duration-200 font-medium',
    warning:
      'bg-gradient-to-r from-warning-500 to-warning-600 text-white hover:from-warning-600 hover:to-warning-700 active:from-warning-700 active:to-warning-800 focus:ring-warning-500/20 transition-all duration-200 font-medium',
  };

  // Simply return the button with disabled state if it's a sample project
  return (
    <button
      className={`inline-flex items-center justify-center font-apercu gap-2 rounded-lg transition-all duration-200 overflow-hidden ${className} ${sizeClasses[size]} ${variantClasses[variant]} ${fullWidth ? 'w-full' : ''} ${isDisabled ? 'cursor-not-allowed opacity-50' : ''}`}
      disabled={isDisabled}
      {...props}
    >
      {isLoading ? (
        <>
          <svg className={`animate-spin -ml-1 mr-2 h-4 w-4 ${variant === 'outline' ? 'text-primary-600 dark:text-primary-400' : 'text-white'}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span className={`${variant === 'outline' ? 'text-primary-600 dark:text-primary-400' : 'text-white'}`}>Loading...</span>
        </>
      ) : (
        <>
          {startIcon && <span className="flex items-center">{startIcon}</span>}
          {children}
          {endIcon && <span className="flex items-center">{endIcon}</span>}
        </>
      )}
    </button>
  );
};

export default Button;
