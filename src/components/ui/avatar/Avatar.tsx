import Image from 'next/image';
import React, { useState } from 'react';

interface AvatarProps {
  src: string; // URL of the avatar image
  alt?: string; // Alt text for the avatar
  size?: 'xsmall' | 'small' | 'medium' | 'large' | 'xlarge' | 'xxlarge'; // Avatar size
  status?: 'online' | 'offline' | 'busy' | 'none'; // Status indicator
  priority?: boolean; // Whether to prioritize loading this image
}

const sizeClasses = {
  xsmall: 'h-6 w-6 max-w-6',
  small: 'h-8 w-8 max-w-8',
  medium: 'h-10 w-10 max-w-10',
  large: 'h-12 w-12 max-w-12',
  xlarge: 'h-14 w-14 max-w-14',
  xxlarge: 'h-16 w-16 max-w-16',
};

const statusSizeClasses = {
  xsmall: 'h-1.5 w-1.5 max-w-1.5',
  small: 'h-2 w-2 max-w-2',
  medium: 'h-2.5 w-2.5 max-w-2.5',
  large: 'h-3 w-3 max-w-3',
  xlarge: 'h-3.5 w-3.5 max-w-3.5',
  xxlarge: 'h-4 w-4 max-w-4',
};

const statusColorClasses = {
  online: 'bg-success-500',
  offline: 'bg-error-400',
  busy: 'bg-warning-500',
};

const Avatar: React.FC<AvatarProps> = ({
  src,
  alt = 'User Avatar',
  size = 'medium',
  status = 'none',
  priority = false,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);

  // Get actual pixel dimensions based on size
  const getDimensions = () => {
    const sizeMap = {
      xsmall: 24, // 6 * 4px
      small: 32, // 8 * 4px
      medium: 40, // 10 * 4px
      large: 48, // 12 * 4px
      xlarge: 56, // 14 * 4px
      xxlarge: 64, // 16 * 4px
    };
    return sizeMap[size];
  };

  const dimension = getDimensions();

  return (
    <div
      className={`relative rounded-full ${sizeClasses[size]} overflow-hidden bg-gray-200`}
    >
      {/* Avatar Image */}
      {!error ? (
        <Image
          width={dimension}
          height={dimension}
          sizes={`${dimension}px`}
          src={src}
          alt={alt}
          className={`object-cover w-full h-full rounded-full transition-opacity duration-300 ${isLoading ? 'opacity-0' : 'opacity-100'}`}
          onLoad={() => setIsLoading(false)}
          onError={() => setError(true)}
          loading={priority ? 'eager' : 'lazy'}
          priority={priority}
        />
      ) : (
        <div className="w-full h-full flex items-center justify-center bg-gray-300 text-gray-600 font-medium">
          {alt?.charAt(0) || '?'}
        </div>
      )}

      {/* Status Indicator */}
      {status !== 'none' && (
        <span
          className={`absolute bottom-0 right-0 rounded-full border-[1.5px] border-white dark:border-gray-900 ${
            statusSizeClasses[size]
          } ${statusColorClasses[status] || ''}`}
        ></span>
      )}
    </div>
  );
};

export default Avatar;
