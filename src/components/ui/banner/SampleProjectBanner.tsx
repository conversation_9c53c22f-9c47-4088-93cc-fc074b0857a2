'use client';

import React from 'react';
import { FiInfo } from 'react-icons/fi';
import { useGlobalContext } from '@/context/GlobalContext';

/**
 * A banner component that displays a message when viewing a sample project
 */
const SampleProjectBanner: React.FC = () => {
  const { state } = useGlobalContext();
  const isSampleProject = !!state.projectDetails?.data?.isSampleProject;

  if (!isSampleProject) {
    return null;
  }

  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-blue-500 p-3">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <FiInfo className="h-5 w-5 text-blue-500" aria-hidden="true" />
        </div>
        <div className="ml-3 flex-1">
          <div className="flex items-center">
            <h3 className="text-sm font-medium text-blue-800">
              Sample Project
            </h3>
            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Demo
            </span>
          </div>
          <div className="mt-1 text-sm text-blue-700">
            This is a sample project for demonstration purposes. Some
            functionality has been disabled.
          </div>
        </div>
      </div>
    </div>
  );
};

export default SampleProjectBanner;
