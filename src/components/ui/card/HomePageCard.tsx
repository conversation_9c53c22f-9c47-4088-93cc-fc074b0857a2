'use client';
import OptimizedImage from '../image/OptimizedImage';

interface CardProps {
  id: string;
  img_url: string;
  title: string;
  status: 'active' | 'inactive';
  description: string;
  tags: string[];
  category: string;
  updated_at: string;
}

export default function Card({
  img_url,
  title,
  status,
  description,
  tags,
  updated_at,
}: CardProps) {
  // Handle image loading errors

  return (
    <div className="block overflow-hidden bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-100 dark:border-gray-700 hover:border-gray-200 dark:hover:border-gray-600">
      <div className="relative h-48 overflow-hidden">
        <OptimizedImage
          src={img_url}
          alt={title}
          fill
          priority={false} // Set to true for above-the-fold images
          loading="lazy"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          quality={75} // Optimize quality for performance
          className={`object-cover transition-transform duration-300 hover:scale-105 'opacity-0'`}
          placeholder="blur"
          blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEDQIHXG8H1QAAAABJRU5ErkJggg=="
        />
      </div>
      <div className="p-5 space-y-3">
        <div className="flex items-center justify-between">
          <h3
            className="font-semibold text-lg text-gray-900 dark:text-gray-100 line-clamp-1"
            title={title}
          >
            {title}
          </h3>
          <span
            className={`text-xs px-3 py-1 rounded-full ${status === 'active'
              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'}`}
          >
            {status}
          </span>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3" title={description}>
          {description}
        </p>

        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 pt-2 border-t border-gray-100 dark:border-gray-700">
          <div className="flex items-center gap-2 flex-wrap">
            {tags.map((tag, index) => (
              <span
                key={index}
                className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                {tag}
              </span>
            ))}
          </div>{' '}
          <span>{new Date(updated_at).toLocaleDateString()}</span>
        </div>
      </div>
    </div>
  );
}
