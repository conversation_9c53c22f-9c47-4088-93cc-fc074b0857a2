'use client';

export default function CardSkeleton() {
  return (
    <div className="block overflow-hidden bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700">
      {/* Image skeleton */}
      <div className="relative h-48 overflow-hidden bg-gray-200 dark:bg-gray-700 animate-pulse"></div>

      <div className="p-5 space-y-3">
        {/* Title and status skeleton */}
        <div className="flex items-center justify-between">
          <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse"></div>
          <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded-full w-16 animate-pulse"></div>
        </div>

        {/* Description skeleton */}
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full animate-pulse"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6 animate-pulse"></div>

        {/* Tags skeleton */}
        <div className="flex items-center gap-2 flex-wrap">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded-full w-16 animate-pulse"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded-full w-12 animate-pulse"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded-full w-14 animate-pulse"></div>
        </div>

        {/* Footer skeleton */}
        <div className="flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-700">
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-24 animate-pulse"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-20 animate-pulse"></div>
        </div>
      </div>
    </div>
  );
}
