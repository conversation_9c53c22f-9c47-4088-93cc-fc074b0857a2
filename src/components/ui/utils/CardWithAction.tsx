import React from 'react';
import Button from '@/components/ui/button/Button';
import ThreeDotLoader from '@/components/loading/ThreeDotLoader';
import { ANIMATION_DURATION_MEDIUM } from '@/constants';

interface CardWithActionProps {
  title: string;
  description: string;
  buttonText: string;
  onButtonClick: () => void;
  icon: React.ReactNode;
  className?: string;
  buttonIcon: React.ComponentType;
  loading: boolean;
}

const CardWithAction: React.FC<CardWithActionProps> = ({
  title,
  description,
  buttonText,
  onButtonClick,
  icon,
  className = '',
  buttonIcon,
  loading,
}) => {
  return (
    <div
      data-testid="card-with-action"
      className={`min-h-[300px] bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 flex items-center justify-center transition-all duration-${ANIMATION_DURATION_MEDIUM} rounded-xl ${className}`}
    >
      <div className="text-center p-8 bg-white dark:bg-gray-800/90 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 max-w-md w-full">
        <span className="text-5xl mb-4 block">{icon}</span>
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-3">
          {title}
        </h2>
        <p className="text-gray-600 dark:text-gray-300 mb-6">{description}</p>
        <Button
          variant="primary"
          onClick={onButtonClick}
          className="inline-flex items-center justify-center gap-2 bg-brand-600 hover:bg-brand-700 text-white px-6 py-2.5 rounded-md transition-colors shadow-md dark:bg-brand-500 dark:hover:bg-brand-600"
          disabled={loading}
        >
          <div className="inline-flex items-center justify-center gap-2">
            {loading ? <ThreeDotLoader /> : React.createElement(buttonIcon)}
            {buttonText}
          </div>
        </Button>
      </div>
    </div>
  );
};

export default CardWithAction;
