import { useState, useRef, useEffect } from 'react';
import { BiDotsVerticalRounded } from 'react-icons/bi';

interface ActionItem {
  label: string;
  onClick: () => void;
  icon?: React.ReactNode;
}

interface ActionDropdownProps {
  items: ActionItem[];
  position?: 'left' | 'right';
  iconColor?: string;
}

const ActionDropdown: React.FC<ActionDropdownProps> = ({
  items,
  position = 'right',
  iconColor,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        className={`p-1 ${iconColor ? '' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-full`}
        onClick={toggleDropdown}
        aria-label="More options"
      >
        <BiDotsVerticalRounded size={20} className={iconColor || ''} />
      </button>

      {isOpen && (
        <div
          className={`absolute z-50 mt-1 ${position === 'right' ? 'right-0' : 'left-0'} bg-white dark:bg-gray-800 shadow-lg rounded-md py-1 min-w-40 border border-gray-200 dark:border-gray-700`}
        >
          {items.map((item, index) => (
            <button
              key={index}
              className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
              onClick={() => {
                item.onClick();
                setIsOpen(false);
              }}
            >
              {item.icon && <span className="mr-2">{item.icon}</span>}
              {item.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default ActionDropdown;
