'use client';

import React from 'react';
import { useToast } from '../../../context/ToastContext';
import { IoClose } from 'react-icons/io5';
import {
  IoCheckmarkCircle,
  IoInformationCircle,
  IoWarning,
  IoCloseCircle,
} from 'react-icons/io5';
import { ToastType } from './types';

const toastTypeStyles: Record<ToastType, string> = {
  success:
    'bg-green-50 text-green-800 border-green-200 dark:bg-green-900/50 dark:text-green-300 dark:border-green-800',
  error:
    'bg-red-50 text-red-800 border-red-200 dark:bg-red-900/50 dark:text-red-300 dark:border-red-800',
  warning:
    'bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/50 dark:text-yellow-300 dark:border-yellow-800',
  info: 'bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/50 dark:text-blue-300 dark:border-blue-800',
};

const toastIcons: Record<ToastType, React.ReactElement> = {
  success: <IoCheckmarkCircle className="w-5 h-5" />,
  error: <IoCloseCircle className="w-5 h-5" />,
  warning: <IoWarning className="w-5 h-5" />,
  info: <IoInformationCircle className="w-5 h-5" />,
};

export const ToastContainer = () => {
  const { toasts, removeToast } = useToast();

  return (
    <div
      data-testid="toast-container"
      className="fixed top-4 right-4 z-[999] w-full max-w-sm space-y-4 pointer-events-none"
    >
      {toasts.map((toast) => (
        <div
          key={toast.id}
          className={`transform transition-all duration-300 ease-in-out pointer-events-auto flex items-start w-full max-w-sm overflow-hidden rounded-lg border shadow-lg ${toastTypeStyles[toast.type]} ${toasts.length > 0 ? 'translate-y-0 opacity-100' : '-translate-y-4 opacity-0'}`}
        >
          <div className="flex-1 p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">{toastIcons[toast.type]}</div>
              <div className="ml-3 flex-1">
                <p className="text-sm font-medium">{toast.message}</p>
              </div>
              <div className="ml-4 flex flex-shrink-0">
                <button
                  onClick={() => removeToast(toast.id)}
                  className="inline-flex rounded-md text-current hover:opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2"
                >
                  <IoClose className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
