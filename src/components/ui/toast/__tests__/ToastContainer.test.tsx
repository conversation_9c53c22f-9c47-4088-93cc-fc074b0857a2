import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ToastContainer } from '../ToastContainer';
import { useToast } from '../../../../context/ToastContext';

// Mock the useToast hook
jest.mock('../../../../context/ToastContext', () => ({
  ...jest.requireActual('../../../../context/ToastContext'),
  useToast: jest.fn(),
}));

describe('ToastContainer', () => {
  const mockRemoveToast = jest.fn();

  beforeEach(() => {
    // Reset mock function calls
    mockRemoveToast.mockClear();
    // Mock the useToast implementation for each test
    (useToast as jest.Mock).mockReturnValue({
      toasts: [
        { id: '1', message: 'Success message', type: 'success' },
        { id: '2', message: 'Error message', type: 'error' },
      ],
      removeToast: mockRemoveToast,
    });
  });

  it('renders all toasts with correct messages', () => {
    render(<ToastContainer />);

    expect(screen.getByText('Success message')).toBeInTheDocument();
    expect(screen.getByText('Error message')).toBeInTheDocument();
  });

  it('applies correct styles based on toast type', () => {
    render(<ToastContainer />);

    const successToast = screen
      .getByText('Success message')
      .closest('div[class*="bg-green"]');
    const errorToast = screen
      .getByText('Error message')
      .closest('div[class*="bg-red"]');

    expect(successToast).toBeInTheDocument();
    expect(errorToast).toBeInTheDocument();
  });

  it('calls removeToast when close button is clicked', () => {
    render(<ToastContainer />);

    const closeButtons = screen.getAllByRole('button');
    fireEvent.click(closeButtons[0]);

    expect(mockRemoveToast).toHaveBeenCalledWith('1');
  });

  it('renders no toasts when toasts array is empty', () => {
    (useToast as jest.Mock).mockReturnValue({
      toasts: [],
      removeToast: mockRemoveToast,
    });

    render(<ToastContainer />);

    const toastContainer = screen.getByTestId('toast-container');
    expect(toastContainer.children).toHaveLength(0);
  });
});
