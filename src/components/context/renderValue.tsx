export default function RenderValue({ value }: { value: unknown }) {
  if (Array.isArray(value)) {
    return (
      <div className="space-y-2 pb-4">
        <ul className="list-disc pl-5">
          <li>{value[0]}</li>
          {value.slice(1).map((item, index) => (
            <li key={index}>{item}</li>
          ))}
        </ul>
      </div>
    );
  }

  if (typeof value === 'object' && value !== null) {
    return (
      <div className="overflow-y-auto h-fit">
        <div className="space-y-1 pl-5 mt-1">
          {Object.entries(value).map(([key, val]) => (
            <p key={key} className="break-words">
              <span className="font-semibold text-gray-800 dark:text-gray-100">
                {key.charAt(0).toUpperCase() + key.slice(1)}:
              </span>{' '}
              <span className="text-gray-600 dark:text-gray-300">
                {String(val)}
              </span>
            </p>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="break-words">
      <p className="text-gray-600 dark:text-gray-300">{String(value)}</p>
    </div>
  );
}
