import { SocialPlatform } from '@/types/api';
import { FiInstagram, FiTwitter, FiLinkedin, FiFacebook } from 'react-icons/fi';
import { FaWhatsapp } from 'react-icons/fa';
import Button from '@/components/ui/button/Button';

export default function PlatformCard({
  platform,
  onConnect,
}: {
  platform: SocialPlatform;
  onConnect: (platformId: string) => void;
}) {
  return (
    <div className="rounded-2xl border border-stroke/40 bg-gradient-to-br from-white to-gray-100 p-6 shadow-md transition-all duration-300 hover:shadow-lg hover:scale-[1.02] dark:border-strokedark dark:bg-gray-900">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-full ">
            {platform.id === 'instagram' ? (
              <FiInstagram className="w-10 h-10 text-pink-500" />
            ) : platform.id === 'twitter' ? (
              <FiTwitter className="w-10 h-10 text-blue-500" />
            ) : platform.id === 'linkedin' ? (
              <FiLinkedin className="w-10 h-10 text-blue-700" />
            ) : platform.id === 'facebook' ? (
              <FiFacebook className="w-10 h-10 text-blue-600" />
            ) : (
              <FaWhatsapp className="w-10 h-10 text-green-500" />
            )}
          </div>
          <div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
              {platform.name}
            </h4>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {platform.description}
            </p>
          </div>
        </div>
        <Button
          onClick={() => onConnect(platform.id)}
          disabled={platform.isLoading}
          className={`inline-flex items-center ml-2 justify-center gap-2 rounded-lg px-5 py-2 text-sm font-medium transition-all duration-200 ${
            platform.isLoading
              ? 'cursor-not-allowed opacity-50 bg-gray-300 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
              : platform.isConnected
                ? 'border border-red-500 text-red-500 hover:bg-red-500 hover:text-white'
                : 'border border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white'
          }`}
        >
          {platform.isLoading
            ? 'Connecting...'
            : platform.isConnected
              ? 'Disconnect'
              : 'Connect'}
        </Button>
      </div>
    </div>
  );
}
