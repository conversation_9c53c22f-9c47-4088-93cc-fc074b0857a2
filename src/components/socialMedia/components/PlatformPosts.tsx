'use client';

import React, { Suspense } from 'react';
import dynamic from 'next/dynamic';
import PostLoadingSkeleton from './PostLoadingSkeleton';
import { MockPost, SocialPlatform } from '@/types/api';

// Dynamically import post components only when needed
const PostComponents = {
  twitter: dynamic(() => import('@/components/socialMedia/posts/twitterPosts')),
  instagram: dynamic(
    () => import('@/components/socialMedia/posts/instagramPosts')
  ),
  linkedin: dynamic(
    () => import('@/components/socialMedia/posts/linkedinPosts')
  ),
  facebook: dynamic(
    () => import('@/components/socialMedia/posts/facebookPosts')
  ),
  whatsapp: dynamic(
    () => import('@/components/socialMedia/posts/whatsappPosts')
  ),
};

const SocialMediaPostsLoader = dynamic(
  () => import('@/components/loading/SocialMediaPostsLoader')
);

interface PlatformPostsProps {
  platform: SocialPlatform;
  posts: MockPost[];
  connectedAccount: any;
}

export default function PlatformPosts({
  platform,
  posts,
  connectedAccount,
}: PlatformPostsProps) {
  // Determine the grid layout based on platform
  const gridLayout =
    platform.id === 'instagram'
      ? 'grid-cols-1 gap-4 md:grid-cols-3'
      : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 auto-rows-[minmax(200px, auto)]';

  // Dynamically select the correct post component
  const PostComponent =
    PostComponents[platform.id as keyof typeof PostComponents];

  return (
    <div className={`grid ${gridLayout}`}>
      {posts.length > 0 ? (
        posts.map((post: MockPost) => (
          <Suspense
            key={post.id || post._id}
            fallback={<PostLoadingSkeleton />}
          >
            <PostComponent
              post={post}
              platform={platform.id}
              isConnected={!!connectedAccount?.user_id}
              username={connectedAccount?.username || ''}
              displayPicture={connectedAccount?.profile_picture_url || ''}
              name={connectedAccount?.name || ''}
            />
          </Suspense>
        ))
      ) : (
        <Suspense fallback={<PostLoadingSkeleton />}>
          <SocialMediaPostsLoader platform={platform.id} />
        </Suspense>
      )}
    </div>
  );
}
