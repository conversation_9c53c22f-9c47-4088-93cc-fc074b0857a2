import React from 'react';

interface PostLoadingSkeletonProps {
  platform?: string;
  count?: number;
}

/**
 * Optimized loading skeleton for social media posts
 * Uses CSS for animation instead of Tailwind's animate-pulse for better performance
 */
export default function PostLoadingSkeleton({
  platform = 'default',
  count = 1,
}: PostLoadingSkeletonProps) {
  // Determine aspect ratio based on platform
  const isInstagram = platform === 'instagram';
  const imageClass = isInstagram ? 'aspect-square' : 'h-32';

  const skeletons = [];

  for (let i = 0; i < count; i++) {
    skeletons.push(
      <div
        key={`skeleton-${i}`}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 h-full min-h-[200px] skeleton-container"
      >
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full mr-3 skeleton-pulse"></div>
          <div>
            <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-24 mb-2 skeleton-pulse"></div>
            <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-16 skeleton-pulse"></div>
          </div>
        </div>
        <div className="space-y-2 mb-4">
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full skeleton-pulse"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full skeleton-pulse"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4 skeleton-pulse"></div>
        </div>
        <div
          className={`${imageClass} bg-gray-200 dark:bg-gray-700 rounded w-full mb-4 skeleton-pulse`}
        ></div>
        <div className="flex justify-between">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16 skeleton-pulse"></div>
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16 skeleton-pulse"></div>
        </div>
      </div>
    );
  }

  return <>{skeletons}</>;
}
