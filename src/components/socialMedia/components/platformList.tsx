import { SocialPlatform } from '@/types';

export const initialPlatforms: SocialPlatform[] = [
  {
    id: 'instagram',
    name: 'Instagram',
    icon: '/images/social/instagram.svg',
    description:
      'Share your photos and stories with your followers. Connect your instagram account to start posting',
    isConnected: false,
    isLoading: false,
  },
  {
    id: 'twitter',
    name: 'Twitter',
    icon: '/images/social/twitter.svg',
    description:
      'Engage with your audience in real-time, Connect your twitter account to start posting',
    isConnected: false,
    isLoading: false,
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    icon: '/images/social/linkedin.svg',
    description:
      'Share professional updates and connect with your network. Connect your LinkedIn account to start posting',
    isConnected: false,
    isLoading: false,
  },
  {
    id: 'facebook',
    name: 'Facebook',
    icon: '/images/social/facebook.svg',
    description:
      'Connect with friends and share updates. Connect your Facebook account to start posting',
    isConnected: false,
    isLoading: false,
  },
  {
    id: 'whatsapp',
    name: 'WhatsApp',
    icon: '/images/social/whatsapp.svg',
    description:
      'Send messages to your contacts and groups. Connect your WhatsApp account to start messaging',
    isConnected: false,
    isLoading: false,
  },
];
