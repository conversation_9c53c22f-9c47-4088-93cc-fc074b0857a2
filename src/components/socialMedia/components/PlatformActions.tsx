'use client';

import React from 'react';
import { FiPlus, FiRefreshCw } from 'react-icons/fi';
import Button from '@/components/ui/button/Button';
import ThreeDotLoader from '@/components/loading/ThreeDotLoader';
import { SocialPlatform } from '@/types/api';

interface PlatformActionsProps {
  platform: SocialPlatform;
  isRefreshing: boolean;
  isGenerating: boolean;
  onRefresh: () => void;
  onGenerate: () => void;
}

export default function PlatformActions({
  platform,
  isRefreshing,
  isGenerating,
  onRefresh,
  onGenerate,
}: PlatformActionsProps) {
  return (
    <div className="flex items-center justify-between w-full mb-4">
      <h3 className="text-xl font-semibold text-black dark:text-white">
        {platform.name} Posts
      </h3>

      {/* Action buttons */}
      <div className="flex gap-3">
        {/* Refresh button */}
        <button
          onClick={onRefresh}
          disabled={isRefreshing}
          className="flex items-center gap-2 px-4 py-2 rounded-md bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <FiRefreshCw className={`${isRefreshing ? 'animate-spin' : ''}`} />
          {isRefreshing ? <ThreeDotLoader /> : ''}
        </button>

        {/* Generate posts button */}
        <Button
          onClick={onGenerate}
          variant="outline"
          disabled={isGenerating}
          ignoreSampleProject={true}
        >
          {isGenerating ? (
            <ThreeDotLoader />
          ) : (
            <>
              <FiPlus /> Create
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
