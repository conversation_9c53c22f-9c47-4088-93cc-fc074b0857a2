import { ConnectedAccount, SocialPlatform } from '@/types';
import { getPlatformProfileUrl } from '@/utils';
import OptimizedImage from '@/components/ui/image/OptimizedImage';
export default function ConnectedCard({
  platform,
  getConnectedAccount,
}: {
  platform: SocialPlatform;
  getConnectedAccount: ConnectedAccount;
}) {
  return (
    <div
      key={platform.id}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-all duration-300"
    >
      <div className="p-5">
        <div className="flex items-center">
          <div className="relative w-16 h-16 rounded-full overflow-hidden border-2 border-primary mr-4">
            {getConnectedAccount?.profile_picture_url ? (
              <>
                {/* Placeholder that shows while image is loading */}
                <div className="absolute inset-0 flex items-center justify-center bg-gray-200 rounded-full">
                  <div className="w-8 h-8 rounded-full bg-gray-300 animate-pulse"></div>
                </div>
                <OptimizedImage
                  src={getConnectedAccount.profile_picture_url}
                  alt={getConnectedAccount.name || 'User'}
                  className="w-full h-full object-cover z-10 relative opacity-0 transition-opacity duration-300"
                  width={64}
                  height={64}
                  isAvatar={true}
                  priority={true}
                  showPlaceholder={false}
                />
              </>
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-primary/10 text-primary text-xl font-bold">
                {(getConnectedAccount?.name?.[0] || 'U').toUpperCase()}
              </div>
            )}
          </div>

          <div className="flex-1">
            <div className="flex items-center mb-1">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mr-2">
                {getConnectedAccount?.name || 'User'}
              </h3>
              <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100 flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                Connected
              </span>
            </div>

            <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
              {getConnectedAccount?.username && platform.id !== 'whatsapp' ? (
                <a
                  href={getPlatformProfileUrl(
                    platform.id,
                    getConnectedAccount?.username
                  )}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mr-2 hover:text-blue-500 hover:underline transition-colors flex items-center"
                >
                  <span>@{getConnectedAccount?.username}</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3 w-3 ml-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                    />
                  </svg>
                </a>
              ) : (
                <span className="mr-2">
                  @{getConnectedAccount?.username || 'username'}
                </span>
              )}
              <span className="inline-block w-1 h-1 rounded-full bg-gray-400"></span>
              <span className="ml-2">{platform.name}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
