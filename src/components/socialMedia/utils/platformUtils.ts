import {
  LOADING_STATE_TWITTER_POST_GENERATION,
  LOADING_STATE_INSTAGRAM_POST_GENERATION,
  LOADING_STATE_LINKEDIN_POST_GENERATION,
  LOADING_STATE_FACEBOOK_POST_GENERATION,
  LOADING_STATE_WHATSAPP_POST_GENERATION,
} from '@/constants';

/**
 * Get the loading state key for a specific platform
 */
export const getPlatformLoadingStateKey = (tenant: string): string => {
  switch (tenant) {
    case 'twitter':
      return LOADING_STATE_TWITTER_POST_GENERATION;
    case 'instagram':
      return LOADING_STATE_INSTAGRAM_POST_GENERATION;
    case 'linkedin':
      return LOADING_STATE_LINKEDIN_POST_GENERATION;
    case 'facebook':
      return LOADING_STATE_FACEBOOK_POST_GENERATION;
    case 'whatsapp':
      return LOADING_STATE_WHATSAPP_POST_GENERATION;
    default:
      return LOADING_STATE_TWITTER_POST_GENERATION;
  }
};

/**
 * Get posts for a specific platform from the project details
 */
export const getPlatformPosts = (projectDetails: any, tenant: string) => {
  if (!projectDetails?.data?.socialMediaPosts) {
    return [];
  }

  switch (tenant) {
    case 'twitter':
      return projectDetails.data.socialMediaPosts.twitterPost || [];
    case 'instagram':
      return projectDetails.data.socialMediaPosts.instagramPost || [];
    case 'linkedin':
      return projectDetails.data.socialMediaPosts.linkedinPost || [];
    case 'facebook':
      return projectDetails.data.socialMediaPosts.facebookPost || [];
    case 'whatsapp':
      return projectDetails.data.socialMediaPosts.whatsappPost || [];
    default:
      return [];
  }
};

/**
 * Get connected account for a specific platform
 */
export const getConnectedAccount = (projectDetails: any, tenant: string) => {
  if (!projectDetails?.data?.connectedAccounts) {
    return null;
  }

  return projectDetails.data.connectedAccounts[tenant] || null;
};
