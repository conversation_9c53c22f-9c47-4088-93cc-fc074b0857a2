import { BiCopy, BiDownload, BiShare, BiEdit } from 'react-icons/bi';
import { MockPost } from '@/types';

/**
 * Creates a set of common action items for social media post dropdowns
 * @param post The post data
 * @param showToast Toast notification function
 * @param onEditClick Function to handle edit click
 * @returns Array of action items for the dropdown
 */
export const createPostActionItems = (
  post: MockPost,
  showToast: (
    message: string,
    type: 'success' | 'error' | 'info' | 'warning'
  ) => void,
  onEditClick?: () => void
) => {
  // Create base action items array
  const actionItems = [
    {
      label: 'Copy text',
      onClick: () => {
        if (post?.content_text) {
          navigator.clipboard.writeText(post.content_text);
          showToast('Text copied to clipboard', 'success');
        }
      },
      icon: <BiCopy />,
    },
    // Download media option will be added conditionally
    {
      label: 'Share post',
      onClick: () => {
        // Share functionality can be implemented here
        showToast('Sharing feature coming soon', 'info');
      },
      icon: <BiShare />,
    },
    {
      label: 'Edit post',
      onClick: () => {
        if (onEditClick) {
          onEditClick();
        } else {
          showToast('Edit feature not available', 'info');
        }
      },
      icon: <BiEdit />,
    },
  ];

  // Add download media option only if the post has media_url
  if (post?.media_url) {
    // Insert the download media option after the copy text option (at index 1)
    actionItems.splice(1, 0, {
      label: 'Download media',
      onClick: () => {
        const proxyUrl = `/api/download?url=${encodeURIComponent(post.media_url!)}`; // Non-null assertion is safe here
        const link = document.createElement('a');
        link.href = proxyUrl;
        link.download = post.media_url!.split('/').pop() || 'image-download';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      },
      icon: <BiDownload />,
    });
  }

  return actionItems;
};
