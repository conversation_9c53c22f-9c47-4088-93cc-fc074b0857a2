import Button from '@/components/ui/button/Button';
import { useState } from 'react';
import { MockPost } from '@/types/api';
import { BiImage, BiUser, BiLike, BiComment, BiShare } from 'react-icons/bi';
import { apiService } from '@/services/api';
import ThreeDotLoader from '@/components/loading/ThreeDotLoader';
import { useToast } from '@/context/ToastContext';
import * as Constants from '@/constants';
import DownloadWrapper from '@/components/downloadWrapper';
import ActionDropdown from '@/components/ui/dropdown/ActionDropdown';
import { createPostActionItems } from '@/components/socialMedia/postActionItems';
import EditPostModal from '@/components/modals/EditPostModal';
import { updatePost } from '@/services/postService';
import { convertToIST } from '@/utils';
import OptimizedImage from '@/components/ui/image/OptimizedImage';

const FacebookPost: React.FC<{
  post: MockPost;
  platform: string;
  isConnected: boolean;
  username: string;
  displayPicture: string;
  name: string;
}> = ({ post, platform, username, isConnected, displayPicture, name }) => {
  const [isPosting, setIsPosting] = useState(false);
  const [posted, setPosted] = useState(post.post_status || false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingPost, setEditingPost] = useState<MockPost | null>(null);
  const { showToast } = useToast();

  const handlePost = async () => {
    setIsPosting(true);
    const { success } = await apiService.post(Constants.API_ENDPOINT_POST, {
      postId: post.id,
      platform: platform,
    });
    if (success) {
      showToast(Constants.POSTED_SUCCESS_MESSAGE, 'success');
      setPosted(true);
    } else {
      showToast(Constants.POSTED_ERROR_MESSAGE, 'error');
      setPosted(false);
    }
    setPosted(true);
    setIsPosting(false);
  };

  const handleEditClick = () => {
    setEditingPost(post);
    setIsEditModalOpen(true);
  };

  const handleSaveEdit = async (updatedPost: MockPost) => {
    try {
      await updatePost(updatedPost);
      // Update the local post state with the updated content
      Object.assign(post, updatedPost);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  };

  return (
    <div
      className="rounded-lg h-fit border border-stroke/50 bg-white p-4 shadow-sm hover:shadow-md transition-all duration-300 dark:border-strokedark dark:bg-gray-800"
      style={{ gridRowEnd: `span ${post.media_url ? 3 : 2}` }}
    >
      <div className="flex items-start space-x-3 mb-2">
        <div className="h-10 w-10 rounded-full bg-gray-200 overflow-hidden flex-shrink-0">
          {displayPicture ? (
            <>
              {/* Placeholder that shows while image is loading */}
              <div className="absolute w-10 h-10 flex items-center justify-center bg-gray-200 rounded-full">
                <BiUser className="text-gray-400 animate-pulse" size={24} />
              </div>
              <OptimizedImage
                src={displayPicture}
                alt={username || 'User'}
                width={40}
                height={40}
                className="object-cover w-full h-full relative z-10 opacity-0 transition-opacity duration-300"
                loading="lazy"
                placeholder="blur"
                blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIGZpbGw9IiNFNUU3RUIiLz48L3N2Zz4="
                isAvatar={true}
                showPlaceholder={false}
              />
            </>
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-blue-100 text-blue-500">
              {(username || 'U').charAt(0).toUpperCase()}
            </div>
          )}
        </div>
        <div className="flex flex-col flex-1">
          <div className="flex items-center justify-between w-full">
            <span className="text-sm font-semibold text-black dark:text-white mr-1 truncate">
              {name || 'User '}
            </span>
            <ActionDropdown
              items={createPostActionItems(post, showToast, handleEditClick)}
            />
          </div>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {convertToIST(post.created_at || post.timestamp)}
          </span>
        </div>
      </div>

      <div className="mt-2 text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap break-words">
        {post.content_text}
      </div>

      {post.media_url && (
        <div className="mt-3 rounded-lg overflow-hidden border border-gray-100 dark:border-gray-700">
          <div className="relative aspect-video bg-gray-100 dark:bg-gray-700 group">
            {/* Placeholder that shows while image is loading */}
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700 z-0">
              <BiImage className="text-4xl text-gray-400 animate-pulse" />
            </div>
            <OptimizedImage
              src={post.media_url}
              alt="Post attachment"
              fill
              className="object-cover opacity-0 transition-opacity duration-500 z-10"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              loading="lazy"
              placeholder="blur"
              blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDYwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjYwMCIgaGVpZ2h0PSI0MDAiIGZpbGw9IiNFNUU3RUIiLz48L3N2Zz4="
              quality={80}
              showPlaceholder={true}
            />
            {post.media_url && <DownloadWrapper media_url={post.media_url} />}
          </div>
        </div>
      )}

      <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-3">
          <div className="flex items-center">
            <BiLike className="mr-1" />
            <span>{post.likes || 0}</span>
          </div>
          <div className="flex space-x-2">
            <span>{post.comments || 0} comments</span>
            <span>{post.shares || 0} shares</span>
          </div>
        </div>

        <div className="flex items-center justify-between border-t border-gray-100 dark:border-gray-700 pt-2">
          <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400 text-sm">
            <button className="flex items-center px-2 py-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
              <BiLike className="mr-1" />
              <span>Like</span>
            </button>
            <button className="flex items-center px-2 py-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
              <BiComment className="mr-1" />
              <span>Comment</span>
            </button>
            <button className="flex items-center px-2 py-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
              <BiShare className="mr-1" />
              <span>Share</span>
            </button>
          </div>
          {/* Copy text functionality moved to dropdown */}
        </div>
      </div>

      {isConnected && (
        <div className="mt-4 flex justify-end">
          <Button
            onClick={handlePost}
            disabled={isPosting || posted}
            className={`inline-flex items-center justify-center gap-2 rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200 ${
              posted
                ? 'bg-green-500 text-white cursor-not-allowed'
                : isPosting
                  ? 'bg-blue-300 text-white cursor-not-allowed'
                  : 'bg-blue-500 text-white hover:bg-blue-600'
            }`}
          >
            {isPosting ? (
              <>
                <ThreeDotLoader /> Posting...
              </>
            ) : posted ? (
              'Posted'
            ) : (
              'Post Now'
            )}
          </Button>
        </div>
      )}

      {/* Edit Post Modal */}
      {isEditModalOpen && editingPost && (
        <EditPostModal
          post={editingPost}
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSave={handleSaveEdit}
        />
      )}
    </div>
  );
};

export default FacebookPost;
