import OptimizedImage from '@/components/ui/image/OptimizedImage';
import Button from '@/components/ui/button/Button';
import { useState } from 'react';
import { MockPost } from '@/types/api';
import { BiImage, BiCheck } from 'react-icons/bi';
import { apiService } from '@/services/api';
import ThreeDotLoader from '@/components/loading/ThreeDotLoader';
import { useToast } from '@/context/ToastContext';
import * as Constants from '@/constants';
import DownloadWrapper from '@/components/downloadWrapper';
import ActionDropdown from '@/components/ui/dropdown/ActionDropdown';
import { createPostActionItems } from '@/components/socialMedia/postActionItems';
import EditPostModal from '@/components/modals/EditPostModal';
import { updatePost } from '@/services/postService';
import { convertToIST } from '@/utils';

// Function to format date in WhatsApp style with IST timezone
const getWhatsAppTimeFormat = (timestamp: string): string => {
  // Convert timestamp to IST for consistent display
  const istTimeString = convertToIST(timestamp);

  const postDate = new Date(timestamp);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  // Check if the date is today
  if (postDate >= today) {
    // Extract just the time portion from the IST string
    const timeMatch = istTimeString.match(/, (\d+:\d+ [ap]m)/);
    return timeMatch ? timeMatch[1] : istTimeString;
  }
  // Check if the date is yesterday
  else if (postDate >= yesterday) {
    return 'Yesterday';
  }
  // Otherwise show the full IST date
  else {
    return istTimeString;
  }
};

const WhatsAppPost: React.FC<{
  post: MockPost;
  platform: string;
  isConnected: boolean;
  username: string;
  displayPicture: string;
  name: string;
}> = ({ post, platform, username, isConnected, displayPicture, name }) => {
  const [isPosting, setIsPosting] = useState(false);
  const [posted, setPosted] = useState(post.post_status || false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingPost, setEditingPost] = useState<MockPost | null>(null);
  const { showToast } = useToast();

  const handlePost = async () => {
    setIsPosting(true);
    const { success } = await apiService.post(Constants.API_ENDPOINT_POST, {
      postId: post.id,
      platform: platform,
    });
    if (success) {
      showToast(Constants.POSTED_SUCCESS_MESSAGE, 'success');
      setPosted(true);
    } else {
      showToast(Constants.POSTED_ERROR_MESSAGE, 'error');
      setPosted(false);
    }
    setPosted(true);
    setIsPosting(false);
  };

  const handleEditClick = () => {
    setEditingPost(post);
    setIsEditModalOpen(true);
  };

  const handleSaveEdit = async (updatedPost: MockPost) => {
    try {
      await updatePost(updatedPost);
      // Update the local post state with the updated content
      Object.assign(post, updatedPost);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  };

  return (
    <div
      className="rounded-lg h-fit border border-stroke/50 bg-[#F5F5F5] p-4 shadow-sm hover:shadow-md transition-all duration-300 dark:border-strokedark dark:bg-gray-800"
      style={{ gridRowEnd: `span ${post.media_url ? 3 : 2}` }}
    >
      {/* WhatsApp chat header */}
      <div className="flex items-center justify-between mb-4 bg-[#075E54] text-white p-2 rounded-t-lg -mt-4 -mx-4">
        <div className="flex items-center">
          <div className="h-8 w-8 rounded-full bg-gray-200 overflow-hidden flex-shrink-0 mr-2">
            {displayPicture ? (
              <OptimizedImage
                showPlaceholder={false}
                src={displayPicture}
                alt={username || 'User'}
                width={32}
                height={32}
                className="object-cover w-full h-full"
                loading="lazy"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-300 text-gray-600">
                {(name || 'U').charAt(0).toUpperCase()}
              </div>
            )}
          </div>
          <div className="text-sm font-medium">{name || 'User'}</div>
        </div>
        <ActionDropdown
          items={createPostActionItems(post, showToast, handleEditClick)}
          position="right"
          iconColor="text-white"
        />
      </div>

      {/* WhatsApp message bubbles */}
      <div className="flex flex-col space-y-2">
        <div className="flex justify-end">
          <div className="bg-[#DCF8C6] rounded-lg p-2 max-w-[80%] shadow-sm">
            {post.media_url && (
              <div className="mt-2 rounded-lg overflow-hidden">
                <div className="relative aspect-square w-full max-h-48 bg-gray-100 group">
                  {/* Placeholder that shows while image is loading */}
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-0">
                    <BiImage className="text-4xl text-gray-400 animate-pulse" />
                  </div>
                  <OptimizedImage
                    src={post.media_url}
                    alt="Post attachment"
                    fill
                    className="object-cover opacity-0 transition-opacity duration-500 z-10"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    loading="lazy"
                    placeholder="blur"
                    blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDYwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjYwMCIgaGVpZ2h0PSI0MDAiIGZpbGw9IiNFNUU3RUIiLz48L3N2Zz4="
                  />
                  {post.media_url && (
                    <DownloadWrapper media_url={post.media_url} />
                  )}
                </div>
              </div>
            )}
            <div className="text-sm text-gray-800 whitespace-pre-wrap break-words">
              {post.content_text}
            </div>
            <div className="flex justify-end items-center mt-1">
              <span className="text-xs text-gray-500 mr-1">
                {getWhatsAppTimeFormat(post.created_at || post.timestamp)}
              </span>
              <BiCheck className="text-[#34B7F1] w-4 h-4" />
              <BiCheck className="text-[#34B7F1] w-4 h-4 -ml-3" />
            </div>
          </div>
        </div>
      </div>

      {isConnected && (
        <div className="mt-4 flex justify-end">
          <Button
            onClick={handlePost}
            disabled={isPosting || posted}
            className={`inline-flex items-center justify-center gap-2 rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200 ${
              posted
                ? 'bg-green-500 text-white cursor-not-allowed'
                : isPosting
                  ? 'bg-[#128C7E]/70 text-white cursor-not-allowed'
                  : 'bg-[#128C7E] text-white hover:bg-[#075E54]'
            }`}
          >
            {isPosting ? (
              <>
                <ThreeDotLoader /> Sending...
              </>
            ) : posted ? (
              'Sent'
            ) : (
              'Send Now'
            )}
          </Button>
        </div>
      )}

      {/* Edit Post Modal */}
      {isEditModalOpen && editingPost && (
        <EditPostModal
          post={editingPost}
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSave={handleSaveEdit}
        />
      )}
    </div>
  );
};

export default WhatsAppPost;
