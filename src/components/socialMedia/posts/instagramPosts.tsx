import OptimizedImage from '@/components/ui/image/OptimizedImage';
import Button from '@/components/ui/button/Button';
import { useState } from 'react';
import { MockPost } from '@/types';
import { BiImage, BiUser } from 'react-icons/bi';
import { apiService } from '@/services/api';
import ThreeDotLoader from '@/components/loading/ThreeDotLoader';
import { useToast } from '@/context/ToastContext';
import * as Constants from '@/constants';
import DownloadWrapper from '@/components/downloadWrapper';
import ActionDropdown from '@/components/ui/dropdown/ActionDropdown';
import { createPostActionItems } from '@/components/socialMedia/postActionItems';
import EditPostModal from '@/components/modals/EditPostModal';
import { updatePost } from '@/services/postService';
import { convertToIST } from '@/utils';

const InstagramPost: React.FC<{
  post: MockPost;
  platform: string;
  isConnected: boolean;
  username: string;
  displayPicture: string;
  name: string;
}> = ({ post, platform, isConnected, username, displayPicture }) => {
  const [isPosting, setIsPosting] = useState(false);
  const [posted, setPosted] = useState(post.post_status || false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingPost, setEditingPost] = useState<MockPost | null>(null);
  const { showToast } = useToast();
  const handlePost = async () => {
    setIsPosting(true);
    const { success } = await apiService.post(Constants.API_ENDPOINT_POST, {
      postId: post.id,
      platform: platform,
    });
    if (success) {
      showToast(Constants.POSTED_SUCCESS_MESSAGE, 'success');
      setPosted(true);
    } else {
      showToast(Constants.POSTED_ERROR_MESSAGE, 'error');
      setPosted(false);
    }
    setIsPosting(false);
  };

  const handleEditClick = () => {
    setEditingPost(post);
    setIsEditModalOpen(true);
  };

  const handleSaveEdit = async (updatedPost: MockPost) => {
    try {
      await updatePost(updatedPost);
      // Update the local post state with the updated content
      Object.assign(post, updatedPost);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  };

  return (
    <div className="rounded-lg border border-stroke/50 bg-white p-0 shadow-md overflow-hidden dark:bg-gray-800 dark:border-strokedark flex flex-col h-full">
      <div className="flex items-center px-3 py-2">
        <div className="w-8 h-8 rounded-full bg-gray-200 overflow-hidden mr-2">
          {displayPicture ? (
            <>
              {/* Placeholder that shows while image is loading */}
              <div className="absolute w-8 h-8 flex items-center justify-center bg-gray-200 rounded-full">
                <BiUser className="text-gray-400 animate-pulse" size={20} />
              </div>
              <OptimizedImage
                src={displayPicture}
                alt={username || 'User'}
                width={32}
                height={32}
                className="object-cover w-full h-full relative z-10 opacity-0 transition-opacity duration-300 rounded-full"
                loading="lazy"
                placeholder="blur"
                blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9IiNFNUU3RUIiLz48L3N2Zz4="
                unoptimized={displayPicture.startsWith('data:')} // Skip optimization for data URLs
                isAvatar={true}
                showPlaceholder={false}
              />
            </>
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-blue-100 text-blue-500">
              {username?.charAt(0).toUpperCase()}
            </div>
          )}
        </div>
        <div className="flex flex-col flex-1">
          <span className="font-medium text-sm">{username || 'username'}</span>
          <div className="flex items-center text-xs text-gray-500">
            {post.location && <span className="mr-1">{post.location}</span>}
            <span>{convertToIST(post.created_at || post.timestamp)}</span>
          </div>
        </div>
        <div className="ml-auto">
          <ActionDropdown
            items={createPostActionItems(post, showToast, handleEditClick)}
          />
        </div>
      </div>

      <div className="aspect-square relative overflow-hidden bg-gray-100 dark:bg-gray-700 flex items-center justify-center group">
        {post.media_url ? (
          <>
            {/* Placeholder that shows while image is loading */}
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700 z-0">
              <BiImage className="text-6xl text-gray-400 animate-pulse" />
            </div>
            <OptimizedImage
              src={post.media_url}
              alt={post.content_text || 'Instagram post'}
              fill
              className="object-cover opacity-0 transition-opacity duration-500 z-10"
              sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
              loading="lazy"
              placeholder="blur"
              blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDQwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjQwMCIgaGVpZ2h0PSI0MDAiIGZpbGw9IiNFNUU3RUIiLz48L3N2Zz4="
              quality={80} // Slightly reduce quality for faster loading
              showPlaceholder={true}
              unoptimized={post.media_url?.startsWith('data:')}
            />
            {post?.media_url && <DownloadWrapper media_url={post.media_url} />}
          </>
        ) : (
          <BiImage className="text-6xl text-gray-400" />
        )}
      </div>

      <div className="px-3 pt-2 flex-grow">
        <div className="flex items-center pb-2">
          <button className="mr-4">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
            </svg>
          </button>
          <button className="mr-4">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z" />
            </svg>
          </button>
          <button>
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="22" y1="2" x2="11" y2="13" />
              <polygon points="22 2 15 22 11 13 2 9 22 2" />
            </svg>
          </button>
          <button className="ml-auto">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z" />
            </svg>
          </button>
        </div>
        {post?.likes && (
          <div className="font-medium text-sm pb-1">
            {post?.likes?.toLocaleString()} likes
          </div>
        )}
        <div className="text-sm pb-2">
          <span className="font-medium mr-1">{username || 'username'}</span>
          <span>{post?.content_text}</span>
        </div>
      </div>
      {isConnected && (
        <div className="px-3 py-2 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center mt-auto">
          {/* {post?.content_text && (
            <div className="pb-2 pl-1">
              <CopyText text={post?.content_text} />
            </div>
          )} */}
          {posted ? (
            <div className="flex items-center text-green-500 dark:text-green-400">
              <svg
                className="w-4 h-4 mr-1"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <span className="text-sm font-medium">Posted</span>
            </div>
          ) : (
            <Button
              onClick={handlePost}
              disabled={isPosting}
              variant="outline"
              className={`relative inline-flex items-center ${
                isPosting
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:text-gray-500'
                  : 'hover:shadow-md active:shadow-sm dark:from-purple-600 dark:to-pink-600'
              }`}
            >
              {isPosting ? (
                <>
                  <ThreeDotLoader />
                  Posting...
                </>
              ) : (
                <>Share on Instagram</>
              )}
            </Button>
          )}
        </div>
      )}
      {isEditModalOpen && editingPost && (
        <EditPostModal
          post={editingPost}
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSave={handleSaveEdit}
        />
      )}
    </div>
  );
};
export default InstagramPost;
