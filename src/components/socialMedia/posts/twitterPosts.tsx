import OptimizedImage from '@/components/ui/image/OptimizedImage';
import Button from '@/components/ui/button/Button';
import { useState } from 'react';
import { MockPost } from '@/types/api';
import { BiImage, BiUser } from 'react-icons/bi';
import { apiService } from '@/services/api';
import ThreeDotLoader from '@/components/loading/ThreeDotLoader';
import { useToast } from '@/context/ToastContext';
import * as Constants from '@/constants';
import DownloadWrapper from '@/components/downloadWrapper';
import ActionDropdown from '@/components/ui/dropdown/ActionDropdown';
import { createPostActionItems } from '@/components/socialMedia/postActionItems';
import EditPostModal from '../../modals/EditPostModal';
import { updatePost } from '@/services/postService';
import { getRelativeTimeIST } from '@/utils';

// Function to format date in Twitter style based on IST
const getTwitterTimeFormat = (timestamp: string): string => {
  // Use the utility function to get relative time based on IST
  const relativeTime = getRelativeTimeIST(timestamp);
  // Customize the format to match Twitter style
  if (relativeTime === 'Just now') {
    return 'now';
  } else if (relativeTime.endsWith('ago')) {
    // Remove 'ago' suffix for Twitter style
    return relativeTime.replace(' ago', '');
  } else {
    return relativeTime;
  }
};

const TwitterPost: React.FC<{
  post: MockPost;
  platform: string;
  isConnected: boolean;
  username: string;
  displayPicture: string;
  name: string;
}> = ({ post, platform, username, isConnected, displayPicture, name }) => {
  const [isPosting, setIsPosting] = useState(false);
  const [posted, setPosted] = useState(post.post_status || false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingPost, setEditingPost] = useState<MockPost | null>(null);
  const { showToast } = useToast();

  const handlePost = async () => {
    setIsPosting(true);
    const { success } = await apiService.post(Constants.API_ENDPOINT_POST, {
      postId: post.id,
      platform: platform,
    });
    if (success) {
      showToast(Constants.POSTED_SUCCESS_MESSAGE, 'success');
      setPosted(true);
    } else {
      showToast(Constants.POSTED_ERROR_MESSAGE, 'error');
      setPosted(false);
    }
    setPosted(true);
    setIsPosting(false);
  };

  const handleEditClick = () => {
    setEditingPost(post);
    setIsEditModalOpen(true);
  };

  const handleSaveEdit = async (updatedPost: MockPost) => {
    try {
      await updatePost(updatedPost);
      // Update the local post state with the updated content
      Object.assign(post, updatedPost);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  };

  return (
    <div
      className="rounded-lg h-fit border border-stroke/50 bg-white p-4 shadow-sm hover:shadow-md transition-all duration-300 dark:border-strokedark dark:bg-gray-800"
      style={{ gridRowEnd: `span ${post.media_url ? 3 : 2}` }}
    >
      <div className="flex items-start space-x-3 mb-2">
        <div className="h-10 w-10 rounded-full bg-gray-200 overflow-hidden flex-shrink-0">
          {displayPicture ? (
            <>
              {/* Placeholder that shows while image is loading */}
              <div className="absolute w-10 h-10 flex items-center justify-center bg-gray-200 rounded-full">
                <BiUser className="text-gray-400 animate-pulse" size={24} />
              </div>
              <OptimizedImage
                showPlaceholder={false}
                src={displayPicture}
                alt={username || 'User'}
                width={40}
                height={40}
                className="object-cover w-full h-full relative z-10 opacity-0 transition-opacity duration-300"
                loading="lazy"
                placeholder="blur"
                blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIGZpbGw9IiNFNUU3RUIiLz48L3N2Zz4="
                isAvatar={true}
              />
            </>
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-blue-100 text-blue-500">
              {(username || 'U').charAt(0).toUpperCase()}
            </div>
          )}
        </div>
        <div className="flex flex-col flex-1">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center">
              <span className="text-sm font-semibold text-black dark:text-white mr-1 truncate">
                {name || 'User '}
              </span>
              <span className="text-gray-500 dark:text-gray-400 text-sm truncate">
                @{username || 'username'}
              </span>
              <span className="text-gray-500 dark:text-gray-400 text-sm ml-1">
                · {getTwitterTimeFormat(post.created_at || post.timestamp)}
              </span>
            </div>
            <ActionDropdown
              items={createPostActionItems(post, showToast, handleEditClick)}
            />
          </div>
          {post.media_url && (
            <div className="mt-3 rounded-xl overflow-hidden border border-gray-100 dark:border-gray-700">
              <div className="relative aspect-video bg-gray-100 dark:bg-gray-700 group">
                {/* Placeholder that shows while image is loading */}
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700 z-0">
                  <BiImage className="text-4xl text-gray-400 animate-pulse" />
                </div>
                <OptimizedImage
                  src={post.media_url}
                  alt="Post attachment"
                  fill
                  className="object-cover opacity-0 transition-opacity duration-500 z-10"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  loading="lazy"
                  placeholder="blur"
                  blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAwIiBoZWlnaHQ9IjI4MCIgdmlld0JveD0iMCAwIDUwMCAyODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjUwMCIgaGVpZ2h0PSIyODAiIGZpbGw9IiNFNUU3RUIiLz48L3N2Zz4="
                  quality={80}
                  showPlaceholder={true}
                />
                {post.media_url && (
                  <DownloadWrapper media_url={post.media_url} />
                )}
              </div>
            </div>
          )}
          <p className="mt-1 text-gray-800 dark:text-gray-200">
            {post.content_text}
          </p>
          {/* Copy text functionality moved to dropdown */}

          <div className="flex items-center justify-between mt-3 text-gray-500 dark:text-gray-400">
            <div className="flex items-center group">
              <button className="p-2 rounded-full group-hover:bg-blue-50 group-hover:text-blue-500 dark:group-hover:bg-blue-900/20">
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z" />
                </svg>
              </button>
            </div>
            <div className="flex items-center group">
              <button className="p-2 rounded-full group-hover:bg-brand-50 group-hover:text-brand-500 dark:group-hover:bg-brand-900/20">
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <polyline points="17 1 21 5 17 9" />
                  <path d="M3 11V9a4 4 0 0 1 4-4h14" />
                  <polyline points="7 23 3 19 7 15" />
                  <path d="M21 13v2a4 4 0 0 1-4 4H3" />
                </svg>
              </button>
              <span className="text-sm ml-1">{post.retweets}</span>
            </div>
            <div className="flex items-center group">
              <button className="p-2 rounded-full group-hover:bg-red-50 group-hover:text-red-500 dark:group-hover:bg-red-900/20">
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
                </svg>
              </button>
              <span className="text-sm ml-1">{post.likes}</span>
            </div>
            <div className="flex items-center">
              <button className="p-2 rounded-full hover:bg-blue-50 hover:text-blue-500 dark:hover:bg-blue-900/20">
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8" />
                  <polyline points="16 6 12 2 8 6" />
                  <line x1="12" y1="2" x2="12" y2="15" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      {isConnected && (
        <div className="mt-2 border-t border-gray-200 dark:border-gray-700 pt-2 flex justify-between items-center">
          <div className="flex items-center">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {posted ? 'Posted to Twitter' : 'Ready to tweet'}
            </span>
          </div>
          {posted ? (
            <div className="flex items-center text-[#1DA1F2] dark:text-[#1DA1F2]">
              <svg
                className="w-4 h-4 mr-1"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" />
              </svg>
              <span className="text-sm font-medium">Tweeted</span>
            </div>
          ) : (
            <Button
              onClick={handlePost}
              disabled={isPosting}
              variant="outline"
              className={`relative inline-flex items-center px-4 py-1.5 rounded-full text-sm font-medium transition-all duration-200 ${
                isPosting
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:text-gray-500'
                  : 'active:shadow-sm dark:hover:bg-[#1a8cd8]'
              }`}
            >
              {isPosting ? (
                <>
                  <ThreeDotLoader />
                  Posting...
                </>
              ) : (
                <>Tweet</>
              )}
            </Button>
          )}
        </div>
      )}

      {/* Edit Post Modal */}
      {isEditModalOpen && editingPost && (
        <EditPostModal
          post={editingPost}
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSave={handleSaveEdit}
        />
      )}
    </div>
  );
};

export default TwitterPost;
