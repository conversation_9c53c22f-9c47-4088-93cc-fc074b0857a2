import OptimizedImage from '@/components/ui/image/OptimizedImage';
import Button from '@/components/ui/button/Button';
import { useState } from 'react';
import { MockPost } from '@/types/api';
import { BiImage, BiUser } from 'react-icons/bi';
import { FiThumbsUp, FiMessageSquare, FiRepeat, FiSend } from 'react-icons/fi';
import { apiService } from '@/services/api';
import ThreeDotLoader from '@/components/loading/ThreeDotLoader';
import { useToast } from '@/context/ToastContext';
import * as Constants from '@/constants';
import DownloadWrapper from '@/components/downloadWrapper';
import ActionDropdown from '@/components/ui/dropdown/ActionDropdown';
import { createPostActionItems } from '@/components/socialMedia/postActionItems';
import EditPostModal from '../../modals/EditPostModal';
import { updatePost } from '@/services/postService';
import { getRelativeTimeIST } from '@/utils';

// Function to format date in LinkedIn style with IST timezone
const getLinkedInTimeFormat = (timestamp: string): string => {
  // Use the utility function to get relative time based on IST
  return getRelativeTimeIST(timestamp);
};

const LinkedInPost: React.FC<{
  post: MockPost;
  platform: string;
  isConnected: boolean;
  username: string;
  displayPicture: string;
  name: string;
}> = ({ post, platform, username, isConnected, displayPicture, name }) => {
  const [isPosting, setIsPosting] = useState(false);
  const [posted, setPosted] = useState(post.post_status || false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingPost, setEditingPost] = useState<MockPost | null>(null);
  const { showToast } = useToast();

  const handlePost = async () => {
    setIsPosting(true);
    const { success } = await apiService.post(Constants.API_ENDPOINT_POST, {
      postId: post.id,
      platform: platform,
    });
    if (success) {
      showToast(Constants.POSTED_SUCCESS_MESSAGE, 'success');
      setPosted(true);
    } else {
      showToast(Constants.POSTED_ERROR_MESSAGE, 'error');
      setPosted(false);
    }
    setPosted(true);
    setIsPosting(false);
  };

  const handleEditClick = () => {
    setEditingPost(post);
    setIsEditModalOpen(true);
  };

  const handleSaveEdit = async (updatedPost: MockPost) => {
    try {
      await updatePost(updatedPost);
      // Update the local post state with the updated content
      Object.assign(post, updatedPost);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  };

  return (
    <div
      className="rounded-lg h-fit border border-gray-200 bg-white p-0 shadow-sm hover:shadow-md transition-all duration-300 dark:border-gray-700 dark:bg-gray-800 overflow-hidden"
      style={{ gridRowEnd: `span ${post.media_url ? 3 : 2}` }}
    >
      {/* Header with profile info */}
      <div className="p-4 pb-2">
        <div className="flex items-start space-x-3">
          <div className="h-12 w-12 rounded-full bg-gray-200 overflow-hidden flex-shrink-0 border border-gray-300 dark:border-gray-600">
            {displayPicture ? (
              <>
                {/* Placeholder that shows while image is loading */}
                <div className="absolute w-12 h-12 flex items-center justify-center bg-gray-200 rounded-full">
                  <BiUser className="text-gray-400 animate-pulse" size={24} />
                </div>
                <OptimizedImage
                  src={displayPicture}
                  alt={username || 'User'}
                  width={48}
                  height={48}
                  className="object-cover w-full h-full relative z-10 opacity-0 transition-opacity duration-300"
                  loading="lazy"
                  placeholder="blur"
                  blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIGZpbGw9IiNFNUU3RUIiLz48L3N2Zz4="
                  showPlaceholder={false}
                />
              </>
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-blue-100 text-blue-600">
                {(username || 'U').charAt(0).toUpperCase()}
              </div>
            )}
          </div>
          <div className="flex flex-col flex-1">
            <div className="flex flex-col">
              <div className="flex items-center justify-between w-full">
                <span className="font-semibold text-black dark:text-white truncate">
                  {name || 'User'}
                </span>
                <ActionDropdown
                  items={createPostActionItems(
                    post,
                    showToast,
                    handleEditClick
                  )}
                />
              </div>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {getLinkedInTimeFormat(post.created_at || post.timestamp)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Post content */}
      <div className="px-4 pt-1 pb-3">
        <p className="text-gray-800 dark:text-gray-200 text-sm leading-relaxed">
          {post.content_text}
        </p>
        {/* Copy text functionality moved to dropdown */}
      </div>

      {/* Media content */}
      {post.media_url && (
        <div className="w-full border-t border-b border-gray-200 dark:border-gray-700">
          <div className="relative aspect-video bg-gray-100 dark:bg-gray-700 group">
            {/* Placeholder that shows while image is loading */}
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-700 z-0">
              <BiImage className="text-4xl text-gray-400 animate-pulse" />
            </div>
            <OptimizedImage
              src={post.media_url}
              alt="Post attachment"
              fill
              className="object-cover opacity-0 transition-opacity duration-500 z-10"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              loading="lazy"
              placeholder="blur"
              blurDataURL="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAwIiBoZWlnaHQ9IjI4MCIgdmlld0JveD0iMCAwIDUwMCAyODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjUwMCIgaGVpZ2h0PSIyODAiIGZpbGw9IiNFNUU3RUIiLz48L3N2Zz4="
            />
            {post.media_url && <DownloadWrapper media_url={post.media_url} />}
          </div>
        </div>
      )}

      {/* Engagement stats */}
      {/* <div className="px-4 py-2 flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <div className="flex -space-x-1 mr-1">
            <div className="w-4 h-4 rounded-full bg-blue-500 flex items-center justify-center text-white text-[8px] border border-white dark:border-gray-800">
              👍
            </div>
          </div>
          <span>{post.likes || 0}</span>
        </div>
        <div className="flex space-x-3">
          <span>{post.comments || 0} comments</span>
          <span>{post.shares || 0} reposts</span>
        </div>
      </div> */}

      {/* Action buttons */}
      <div className="px-2 py-1 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
        <button className="flex items-center justify-center p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400 flex-1">
          <FiThumbsUp className="mr-1" size={16} />
          <span className="text-xs">Like</span>
        </button>
        <button className="flex items-center justify-center p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400 flex-1">
          <FiMessageSquare className="mr-1" size={16} />
          <span className="text-xs">Comment</span>
        </button>
        <button className="flex items-center justify-center p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400 flex-1">
          <FiRepeat className="mr-1" size={16} />
          <span className="text-xs">Repost</span>
        </button>
        <button className="flex items-center justify-center p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400 flex-1">
          <FiSend className="mr-1" size={16} />
          <span className="text-xs">Send</span>
        </button>
      </div>

      {/* Post management controls */}
      {isConnected && (
        <div className="px-4 py-3 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <div className="flex items-center">
            <span className="text-xs text-gray-600 dark:text-gray-300 font-medium">
              {posted ? 'Status: Posted to LinkedIn' : 'Ready to post'}
            </span>
            {posted && (
              <span className="ml-2 px-1.5 py-0.5 text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full">
                ✓
              </span>
            )}
          </div>
          <Button
            onClick={handlePost}
            disabled={isPosting || posted}
            variant="outline"
            className="text-xs px-4 py-1.5 rounded-full"
          >
            {isPosting ? (
              <>
                <ThreeDotLoader /> Posting...
              </>
            ) : posted ? (
              'Posted to LinkedIn'
            ) : (
              'Post to LinkedIn'
            )}
          </Button>
        </div>
      )}

      {/* Edit Post Modal */}
      {isEditModalOpen && editingPost && (
        <EditPostModal
          post={editingPost}
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSave={handleSaveEdit}
        />
      )}
    </div>
  );
};

export default LinkedInPost;
