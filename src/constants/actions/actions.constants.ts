/**
 * Global action type constants
 * These constants are used in the global reducer and action creators
 */

// User related actions
export const SET_DARK_MODE = 'SET_DARK_MODE';
export const SET_USER = 'SET_USER';
export const UPDATE_USER_PREFERENCES = 'UPDATE_USER_PREFERENCES';

// Social media account actions
export const UPDATE_TWITTER_ACCOUNT = 'UPDATE_TWITTER_ACCOUNT';
export const UPDATE_INSTAGRAM_ACCOUNT = 'UPDATE_INSTAGRAM_ACCOUNT';
export const UPDATE_LINKEDIN_ACCOUNT = 'UPDATE_LINKEDIN_ACCOUNT';

// Message related actions
export const SET_MESSAGES = 'SET_MESSAGES';
export const ADD_MESSAGES = 'ADD_MESSAGES';
export const UPDATE_MESSAGE = 'UPDATE_MESSAGE';
export const SET_IS_TYPING = 'SET_IS_TYPING';
export const FETCH_CAMPAIGN_DATA_SUCCESS = 'FETCH_CAMPAIGN_DATA_SUCCESS';

// ChatBox related actions
export const SET_CHATBOX_MESSAGES = 'SET_CHATBOX_MESSAGES';
export const ADD_CHATBOX_MESSAGE = 'ADD_CHATBOX_MESSAGE';
export const CLEAR_CHATBOX_MESSAGES = 'CLEAR_CHATBOX_MESSAGES';
export const SET_CHATBOX_OPEN = 'SET_CHATBOX_OPEN';

// UI state actions
export const SET_SHOW_BUTTONS = 'SET_SHOW_BUTTONS';
export const SET_PROCEED_COUNT = 'SET_PROCEED_COUNT';

// Project related actions
export const SET_INIT_PROJECTS = 'SET_INIT_PROJECTS';
export const UPDATE_PROJECT_USER_INPUT = 'UPDATE_PROJECT_USER_INPUT';
export const UPDATE_PROJECT_CHAPTERS = 'UPDATE_PROJECT_CHAPTERS';
export const UPDATE_SOCIAL_MEDIA_POSTS = 'UPDATE_SOCIAL_MEDIA_POSTS';
export const UPDATE_LANDING_PAGE_HTML = 'UPDATE_LANDING_PAGE_HTML';
export const UPDATE_FACEBOOK_ACCOUNT = 'UPDATE_FACEBOOK_ACCOUNT';
export const SET_IS_SAMPLE_PROJECT = 'SET_IS_SAMPLE_PROJECT';
export const RESET_PROJECT_DETAILS = 'RESET_PROJECT_DETAILS';

// API request status actions
export const FETCH_IDEAS_REQUEST = 'FETCH_IDEAS_REQUEST';
export const FETCH_IDEAS_SUCCESS = 'FETCH_IDEAS_SUCCESS';
export const FETCH_IDEAS_FAILURE = 'FETCH_IDEAS_FAILURE';
export const FETCH_PROJECT_DETAILS_REQUEST = 'FETCH_PROJECT_DETAILS_REQUEST';
export const FETCH_PROJECT_DETAILS_SUCCESS = 'FETCH_PROJECT_DETAILS_SUCCESS';
export const FETCH_PROJECT_DETAILS_FAILURE = 'FETCH_PROJECT_DETAILS_FAILURE';
export const FETCH_BUSINESS_META_SUCCESS = 'FETCH_BUSINESS_META_SUCCESS';
export const FETCH_BUSINESS_META_FAILURE = 'FETCH_BUSINESS_META_FAILURE';

// Loading state actions
export const SET_LOADING_STATE = 'SET_LOADING_STATE';
export const LOADING_STATE_CONTEXT_GENERATION = 'contextGeneration';
export const LOADING_STATE_TWITTER_POST_GENERATION = 'twitterPostGeneration';
export const LOADING_STATE_INSTAGRAM_POST_GENERATION = 'instaPostGeneration';
export const LOADING_STATE_LINKEDIN_POST_GENERATION = 'linkedinPostGeneration';
export const LOADING_STATE_FACEBOOK_POST_GENERATION = 'facebookPostGeneration';
export const LOADING_STATE_WHATSAPP_POST_GENERATION = 'whatsappPostGeneration';
export const LOADING_STATE_LANDING_PAGE_GENERATION = 'landingPageGeneration';
export const LOADING_STATE_SOCIAL_MEDIA_GENERATION = 'socialMediaGeneration';
export const LOADING_STATE_CONTENT_CALENDAR_GENERATION =
  'contentCalendarGeneration';
