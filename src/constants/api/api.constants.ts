/**
 * API-related constants
 * These constants are used for API endpoints, request configurations, and error handling
 */

// API endpoints
export const API_ENDPOINT_GENERATE_CONTEXT = '/api/generate-business-context';
export const API_ENDPOINT_GENERATE_SOCIAL_POSTS = '/api/generate-posts';
export const API_ENDPOINT_PROJECT_DETAILS = '/api/projectDetails';
export const API_ENDPOINT_PROJECT_DETAILS_SECOND_FOLD =
  '/api/projectDetails/secondfold';
export const API_ENDPOINT_GET_REDIRECT_URL = '/api/social/getRedirectUrl';
export const API_ENDPOINT_SOCIAL_AUTH = '/api/social/auth';
export const API_ENDPOINT_HOME = '/api/home';
export const API_ENDPOINT_CREATE_CAMPAIGN = '/api/campaigns/create-campaign';
export const API_ENDPOINT_UPDATE_CAMPAIGN = '/api/campaigns/update-campaign';
export const API_ENDPOINT_GET_CAMPAIGNS = '/api/campaigns/get-campaign-data';
export const API_ENDPOINT_GET_BUSINESS_META = '/api/business-ideas/get-by-id';
export const API_ENDPOINT_UPDATE_POST = '/api/social/posts/edit';
export const API_ENDPOINT_POST = '/api/social/post';
export const API_VALIDATE_IDEA = '/api/validate-idea';
export const API_ENDPOINT_GET_BUSINESS_CONTEXT =
  'api/business-context/get-business-context';
export const API_ENDPOINT_SAVE_BUSINESS_CONTEXT =
  'api/business-context/save-business-context';
// Payment API endpoints
export const API_ENDPOINT_CREATE_PAYMENT_ORDER = '/api/payments/create-order';
export const API_ENDPOINT_VERIFY_PAYMENT = '/api/payments/verify-payment';
export const API_ENDPOINT_PAYMENT_WEBHOOK = '/api/payments/webhook';
export const API_ENDPOINT_PAYMENT_STATUS = '/api/payments/status/check';

// Credit system endpoints
export const API_ENDPOINT_CREDIT_BALANCE = '/api/credits/balance';
export const API_ENDPOINT_CREDIT_TRANSACTIONS = '/api/credits/transactions';
export const API_ENDPOINT_CREDIT_FEATURES = '/api/credits/features';
export const API_ENDPOINT_CREDIT_PLANS = '/api/credits/plans';

// API request methods
export const HTTP_METHOD_GET = 'GET';
export const HTTP_METHOD_POST = 'POST';
export const HTTP_METHOD_PUT = 'PUT';
export const HTTP_METHOD_DELETE = 'DELETE';

// API response status codes
export const STATUS_CODE_OK = 200;
export const STATUS_CODE_CREATED = 201;
export const STATUS_CODE_BAD_REQUEST = 400;
export const STATUS_CODE_UNAUTHORIZED = 401;
export const STATUS_CODE_NOT_FOUND = 404;
export const STATUS_CODE_SERVER_ERROR = 500;

// Error messages
export const ERROR_NETWORK =
  'Network error occurred. Please check your connection.';
export const ERROR_SERVER = 'Server error occurred. Please try again later.';
export const ERROR_UNAUTHORIZED =
  'You are not authorized to perform this action.';
export const ERROR_CONTEXT_GENERATION =
  'Failed to generate context. Please try again.';
export const ERROR_SOCIAL_POST_GENERATION =
  'Failed to generate social media posts. Please try again.';

export const IN_PROGRESS_STATUS = 'in_progress';
export const DONE_STATUS = 'done';
