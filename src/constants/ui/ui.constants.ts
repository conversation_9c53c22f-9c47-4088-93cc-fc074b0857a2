/**
 * UI-related constants
 * These constants are used for UI elements, styling, and configuration
 */

// Loading state keys

// Button states
export const BUTTON_STATE_IDLE = 'idle';
export const BUTTON_STATE_LOADING = 'loading';
export const BUTTON_STATE_SUCCESS = 'success';
export const BUTTON_STATE_ERROR = 'error';

// UI element sizes
export const ICON_SIZE_SMALL = 16;
export const ICON_SIZE_MEDIUM = 24;
export const ICON_SIZE_LARGE = 32;

// Animation durations (as Tailwind class values)
export const ANIMATION_DURATION_SHORT = '200';
export const ANIMATION_DURATION_MEDIUM = '300';
export const ANIMATION_DURATION_LONG = '700';

// export const CARD_GRADIENTS = [
//   'linear-gradient(135deg, #ffe6e6 30%, #fff0e6 100%)', // Very light coral to pale peach
//   'linear-gradient(135deg, #cce6ff 30%, #e6faff 100%)', // Very light blue to faint cyan
//   'linear-gradient(135deg, #f0e6ff 30%, #e6f0ff 100%)', // Very light purple to pale blue
//   'linear-gradient(135deg, #e6ffe6 30%, #f0fff0 100%)', // Very light green to mint
//   'linear-gradient(135deg, #ffe6eb 30%, #fff5f5 100%)', // Very light pink to blush
//   'linear-gradient(135deg, #fff0cc 30%, #fff8e6 100%)', // Very light yellow to cream
//   'linear-gradient(135deg, #e6f0fa 30%, #f0f5ff 100%)', // Very light slate to pale blue
//   'linear-gradient(135deg, #f5e6ff 30%, #faf0ff 100%)', // Very light lavender to faint purple
//   'linear-gradient(135deg, #e6fff0 30%, #f0fffa 100%)', // Very light teal to pale aqua
//   'linear-gradient(135deg, #fff5e6 30%, #fffaf0 100%)', // Very light apricot to off-white
// ];
