'use client';
import React, {
  useEffect,
  useRef,
  useState,
  useCallback,
  Suspense,
  useMemo,
} from 'react';
import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';
import { useSidebar } from '../context/SidebarContext';
import { useGlobalContext } from '../context/GlobalContext';
import {
  BoxCubeIcon,
  ChevronDownIcon,
  DocsIcon,
  GridIcon,
  HorizontaLDots,
  ListIcon,
  Logo,
  GroupIcon,
  UserCircleIcon,
} from '../icons/index';
import { formatChapterText } from '@/utils';

interface NavItem {
  name: string;
  icon: React.ReactNode;
  path?: string;
  subItems?: { name: string; path: string; new?: boolean; chapter?: string }[];
}

interface SubmenuState {
  type: 'main' | 'others';
  index: number;
}

// Sidebar menu data
const othersItems: NavItem[] = [
  {
    icon: <BoxCubeIcon />,
    name: 'Social Media',
    path: '/socials',
    subItems: [
      { name: 'Campaign', path: '/socials/campaigns' },
      { name: 'Instagram', path: '/socials/instagram' },
      { name: 'Twitter', path: '/socials/twitter' },
      { name: 'LinkedIn', path: '/socials/linkedin' },
      { name: 'Facebook', path: '/socials/facebook' },
      { name: 'WhatsApp', path: '/socials/whatsapp' },
    ],
  },
  {
    icon: <ListIcon />,
    name: 'Landing page',
    path: '/landing-page',
    subItems: [
      { name: 'Page view', path: '/landing-page/view' },
      { name: 'Deploy', path: '/landing-page/deploy' },
    ],
  },
  {
    icon: <UserCircleIcon />,
    name: 'Customer Support',
    path: '/customer-support',
  },
  {
    icon: <DocsIcon />,
    name: 'Business Documents',
    path: '/business-documents',
  },
  {
    icon: <BoxCubeIcon />,
    name: 'Analytics',
    path: '/analytics',
    subItems: [
      { name: 'Instagram Analytics', path: '/analytics/instagram' },
      { name: 'Twitter Analytics', path: '/analytics/twitter' },
      { name: 'Landing Page Analytics', path: '/analytics/landing-page' },
    ],
  },
  {
    icon: <DocsIcon />,
    name: 'Help Center',
    path: '/help-center',
  },
];

// Menu Item Component
const MenuItem: React.FC<{
  item: NavItem;
  isActive: (path: string, chapter?: string) => boolean;
  getPath: (path: string, chapter?: string) => string;
  isExpanded: boolean;
  isHovered: boolean;
  isMobileOpen: boolean;
  isOpen: boolean;
  onToggle: () => void;
  subMenuRef: (el: HTMLDivElement | null) => void;
  subMenuHeight: number;
  toggleMobileSidebar: () => void;
}> = ({
  item,
  isActive,
  getPath,
  isExpanded,
  isHovered,
  isMobileOpen,
  isOpen,
  onToggle,
  subMenuRef,
  subMenuHeight,
  toggleMobileSidebar,
}) => {
  const showText = isExpanded || isHovered || isMobileOpen;
  const defaultPath = item.path || '/context'; // Default to first subItem's path or item's path

  return (
    <li>
      {item.subItems ? (
        <Link
          href={getPath(defaultPath)} // Redirect to default path when clicked
          onClick={onToggle} // Toggle submenu on click
          className={`menu-item group ${isOpen ? 'menu-item-active' : 'menu-item-inactive'} cursor-pointer 
            ${!isExpanded && !isHovered ? 'lg:justify-center' : 'lg:justify-start'}`}
        >
          <span
            className={
              isOpen ? 'menu-item-icon-active' : 'menu-item-icon-inactive'
            }
          >
            {item.icon}
          </span>
          {showText && <span className="menu-item-text">{item.name}</span>}
          {showText && (
            <ChevronDownIcon
              className={`ml-auto w-5 h-5 transition-transform duration-200 ${isOpen ? 'rotate-180 text-brand-500' : ''}`}
            />
          )}
        </Link>
      ) : (
        item.path && (
          <Link
            href={getPath(item.path)}
            className={`menu-item group ${isActive(item.path) ? 'menu-item-active' : 'menu-item-inactive'}`}
          >
            <span
              className={
                isActive(item.path)
                  ? 'menu-item-icon-active'
                  : 'menu-item-icon-inactive'
              }
            >
              {item.icon}
            </span>
            {showText && <span className="menu-item-text">{item.name}</span>}
          </Link>
        )
      )}

      {item.subItems && showText && (
        <div
          ref={subMenuRef}
          className="overflow-hidden transition-all duration-300"
          style={{ height: isOpen ? `${subMenuHeight}px` : '0px' }}
        >
          <ul className="mt-2 space-y-1 ml-9">
            {item.subItems.map((subItem) => (
              <li key={subItem.name}>
                <Link
                  href={getPath(subItem.path, subItem.chapter)}
                  className={`menu-dropdown-item ${
                    isActive(subItem.path, subItem.chapter)
                      ? 'menu-dropdown-item-active'
                      : 'menu-dropdown-item-inactive'
                  }`}
                  onClick={(e) => {
                    // Prevent default only if needed
                    if (isActive(subItem.path, subItem.chapter)) {
                      e.preventDefault();
                    }

                    if (isMobileOpen) {
                      toggleMobileSidebar();
                    }
                  }}
                >
                  {subItem.name}
                  {subItem.new && (
                    <span
                      className={`ml-auto ${
                        isActive(subItem.path)
                          ? 'menu-dropdown-badge-active'
                          : 'menu-dropdown-badge-inactive'
                      } menu-dropdown-badge`}
                    >
                      new
                    </span>
                  )}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      )}
    </li>
  );
};

// Main Sidebar Component
const AppSidebar: React.FC = () => {
  const {
    isExpanded,
    isMobileOpen,
    isHovered,
    setIsHovered,
    toggleMobileSidebar,
  } = useSidebar();
  const { state } = useGlobalContext();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const id = searchParams?.get('id');
  const chapter = searchParams?.get('chapter');

  const [openSubmenu, setOpenSubmenu] = useState<SubmenuState | null>(null);
  const [subMenuHeight, setSubMenuHeight] = useState<Record<string, number>>(
    {}
  );
  const subMenuRefs = useRef<Record<string, HTMLDivElement | null>>({});

  const navItems: NavItem[] = useMemo(
    () => [
      {
        icon: <GroupIcon />,
        name: 'Project Home',
        path: '/home',
      },
      {
        icon: <GridIcon />,
        name: 'Business Context',
        path: '/context',
        subItems: state.projectDetails?.data?.projects?.chapters?.map(
          (chapterName: string) => ({
            name: formatChapterText(chapterName),
            path: '/context',
            chapter: chapterName,
          })
        ) || [{ name: 'Context', path: '/context' }],
      },
    ],
    [state.projectDetails?.data?.projects?.chapters]
  );

  // Memoize path generation functions to prevent recreating them on each render
  const getPathWithId = useCallback(
    (path: string) => {
      return id ? `${path}${path.includes('?') ? '&' : '?'}id=${id}` : path;
    },
    [id]
  );

  const getChapterPath = useCallback(
    (path: string, chapterName?: string) => {
      const basePath = getPathWithId(path);
      return chapterName
        ? `${basePath}${basePath.includes('?') ? '&' : '?'}chapter=${chapterName}`
        : basePath;
    },
    [getPathWithId]
  );

  const isActive = useCallback(
    (path: string, itemChapter?: string) => {
      return itemChapter
        ? path === pathname && itemChapter === chapter
        : path === pathname;
    },
    [pathname, chapter]
  );

  const handleSubmenuToggle = useCallback(
    (index: number, menuType: 'main' | 'others') => {
      setOpenSubmenu((prev) =>
        prev?.type === menuType && prev.index === index
          ? null
          : { type: menuType, index }
      );
    },
    []
  );

  // Memoize the active submenu detection to prevent unnecessary re-renders
  useEffect(() => {
    // Use a timeout to prevent rapid state updates
    const timer = setTimeout(() => {
      let submenuMatched = false;
      ['main', 'others'].forEach((menuType) => {
        const items = menuType === 'main' ? navItems : othersItems;
        items.forEach((nav, index) => {
          // Check if current path matches the item's path or any subItem's path
          const isItemActive =
            (nav.path && isActive(nav.path)) ||
            (nav.subItems?.some((subItem) =>
              isActive(subItem.path, subItem.chapter)
            ) ??
              false);

          if (isItemActive) {
            setOpenSubmenu({ type: menuType as 'main' | 'others', index });
            submenuMatched = true;
          }
        });
      });
      if (!submenuMatched) setOpenSubmenu(null);
    }, 50); // Small delay to batch updates

    return () => clearTimeout(timer); // Clean up timeout to prevent memory leaks
  }, [pathname, chapter, isActive, navItems]);

  useEffect(() => {
    if (openSubmenu) {
      const key = `${openSubmenu.type}-${openSubmenu.index}`;
      const height = subMenuRefs.current[key]?.scrollHeight || 0;
      setSubMenuHeight((prev) => ({ ...prev, [key]: height }));
    }
  }, [openSubmenu, state.projectDetails?.data?.projects?.chapters]); // Add chapters as dependency to recalculate heights when they change

  const renderMenuSection = (
    items: NavItem[],
    type: 'main' | 'others',
    title: string
  ) => (
    <div>
      <h2
        className={`mb-4 text-xs uppercase flex leading-[20px] text-gray-400 
        ${!isExpanded && !isHovered ? 'lg:justify-center' : 'justify-start'}`}
      >
        {isExpanded || isHovered || isMobileOpen ? (
          title
        ) : (
          <HorizontaLDots className="text-yellow-500" />
        )}
      </h2>
      <ul className="flex flex-col gap-4">
        {items.map((item, index) => (
          <MenuItem
            key={item.name}
            item={item}
            isActive={isActive}
            getPath={getChapterPath}
            isExpanded={isExpanded}
            isHovered={isHovered}
            isMobileOpen={isMobileOpen}
            isOpen={openSubmenu?.type === type && openSubmenu.index === index}
            onToggle={() => handleSubmenuToggle(index, type)}
            subMenuRef={(el) => {
              subMenuRefs.current[`${type}-${index}`] = el;
            }}
            toggleMobileSidebar={toggleMobileSidebar}
            subMenuHeight={subMenuHeight[`${type}-${index}`] || 0}
          />
        ))}
      </ul>
    </div>
  );

  return (
    <aside
      className={`fixed mt-16 lg:mt-0 top-0 px-5 left-0 bg-white dark:bg-gray-900 dark:border-gray-800 
        text-gray-900 h-screen transition-all duration-300 ease-in-out z-50 border-r border-gray-200 
        ${isExpanded || isMobileOpen ? 'w-[290px]' : isHovered ? 'w-[290px]' : 'w-[90px]'}
        ${isMobileOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0`}
      onMouseEnter={() => !isExpanded && setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        className={`py-8 flex ${!isExpanded && !isHovered ? 'lg:justify-center align-content' : 'justify-start'}`}
      >
        <Link href="/" className="flex items-center">
          {isExpanded || isHovered || isMobileOpen ? (
            <div className="flex items-center gap-1 p-1.5 rounded-lg transition-all duration-300">
              <Logo />
            </div>
          ) : (
            <div className="p-2 bg-gradient-to-r from-gray-50 to-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
              <span className="font-semibold text-lg text-gray-800 dark:text-white">
                D
              </span>
            </div>
          )}
        </Link>
      </div>

      <div className="flex flex-col flex-grow overflow-y-auto duration-300 ease-linear no-scrollbar">
        <nav className="mb-6 flex-grow">
          <div className="flex flex-col gap-4">
            {renderMenuSection(navItems, 'main', 'Menu')}
            {renderMenuSection(othersItems, 'others', 'Tools')}
          </div>
        </nav>
      </div>
    </aside>
  );
};

export default function Sidebar() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AppSidebar />
    </Suspense>
  );
}
