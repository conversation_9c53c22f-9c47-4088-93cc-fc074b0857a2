'use client';

import Sidebar from "@/components/layout/Sidebar";
import Navbar from "@/components/layout/Navbar";
import { RevenueChart, ProjectsChart } from "@/components/dashboard/Charts";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowUpRight, Users, Clock, Target } from 'lucide-react';

const MetricCard = ({ title, value, change, icon }: { title: string, value: string, change: string, icon: React.ReactNode }) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      {icon}
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      <p className="text-xs text-muted-foreground flex items-center">
        <ArrowUpRight className="mr-1 h-4 w-4 text-green-500" />
        {change} from last month
      </p>
    </CardContent>
  </Card>
);

const Index = () => {
  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="flex-1 p-6 overflow-auto">
          <h1 className="text-3xl font-bold mb-6 text-[#FE8C00]">Transform Your Ideas Into Reality</h1>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <MetricCard
              title="Total Revenue"
              value="$45,231.89"
              change="+20.1%"
              icon={<Target className="h-4 w-4 text-muted-foreground" />}
            />
            <MetricCard
              title="Active Users"
              value="2,350"
              change="+10.1%"
              icon={<Users className="h-4 w-4 text-muted-foreground" />}
            />
            <MetricCard
              title="Active Projects"
              value="12"
              change="+12.5%"
              icon={<Target className="h-4 w-4 text-muted-foreground" />}
            />
            <MetricCard
              title="Avg. Response Time"
              value="2.4h"
              change="-8.1%"
              icon={<Clock className="h-4 w-4 text-muted-foreground" />}
            />
          </div>
          <div className="grid gap-6 md:grid-cols-2 mt-6">
            <RevenueChart />
            <ProjectsChart />
          </div>
        </main>
      </div>
    </div>
  );
};

export default Index;
