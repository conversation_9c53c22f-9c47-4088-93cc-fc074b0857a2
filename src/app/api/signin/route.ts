import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const resp = await fetch(`${process.env.BACKEND_URL}/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: authHeader,
      },
      body: JSON.stringify({ ...body, firebase_token: null }),
      credentials: 'include',
    });
    if (!resp.ok) {
      throw new Error('Failed to fetch data from external API');
    }
    const data = await resp.json();
    return NextResponse.json({ data }, { status: 200 });
  } catch (error) {
    console.log(error);
    return NextResponse.json(
      {
        data: null,
        status: 500,
        message:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
        success: false,
      },
      { status: 500 }
    );
  }
}
