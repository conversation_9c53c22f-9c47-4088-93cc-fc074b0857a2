import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const url = searchParams.get('url');

  // Early return for missing URL - reduces nesting
  if (!url) {
    return NextResponse.json(
      {
        data: null,
        status: 400,
        message: 'URL parameter is required',
        success: false,
      },
      { status: 400 }
    );
  }

  try {
    // Fetch with streaming enabled and minimal headers
    const response = await fetch(url, {
      headers: {
        Accept: '*/*', // Only necessary header
      },
    });

    // Check response status
    if (!response.ok) {
      throw new Error('Failed to fetch image from S3');
    }

    // Stream the response directly
    const contentType =
      response.headers.get('Content-Type') || 'application/octet-stream';
    const filename =
      decodeURIComponent(url).split('/').pop() || 'downloaded-image';

    return new NextResponse(response.body, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    return NextResponse.json(
      {
        data: null,
        status: 500,
        message:
          error instanceof Error ? error.message : 'Unexpected error occurred',
        success: false,
      },
      { status: 500 }
    );
  }
}
