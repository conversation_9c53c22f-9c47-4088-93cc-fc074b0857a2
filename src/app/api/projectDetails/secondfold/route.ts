import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

async function fetchProjectSecondFold(projectId: string, authHeader: string) {
  const response = await fetch(
    `${process.env.BACKEND_URL}/getProjectComprehensiveInfo/${projectId}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: authHeader,
      },
    }
  );

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  const data = await response.json();
  return data;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('id');
    if (!projectId) {
      return NextResponse.json(
        { success: false, message: 'Project ID is required' },
        { status: 400 }
      );
    }
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const projectData = await fetchProjectSecondFold(projectId, authHeader);

    // Return the data with landing page and campaigns
    return NextResponse.json(projectData, { status: 200 });
  } catch (error) {
    console.error('Error fetching project second fold data:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch project details' },
      { status: 500 }
    );
  }
}
