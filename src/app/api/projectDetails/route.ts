import { NextResponse } from 'next/server';

async function fetchChatIdeas(ideaId: string, authHeader: string) {
  const response = await fetch(
    process.env.BACKEND_URL + `/getProjectInfoById?projectId=${ideaId}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: authHeader,
      },
    }
  );

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  const data = await response.json();
  return {
    id: data?.id,
    projects: {
      chapters: ['market_analysis', 'target_users', 'business_model'],
      context: data.context,
    },
    connectedAccounts: data.connected_accounts,
    socialMediaPosts: data.socialMediaPosts,
    projectMetaData: data.projectMetaData,
    ...data,
  };
}

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const ideaId = url.searchParams.get('id');
    if (!ideaId) {
      return NextResponse.json(
        { error: 'Idea ID is required' },
        { status: 400 }
      );
    }
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const chatIdeas = await fetchChatIdeas(ideaId, authHeader);
    return NextResponse.json(chatIdeas, { status: 200 });
  } catch (error) {
    console.error('Error fetching chat ideas:', error);
    return NextResponse.json(
      { error: 'Failed to fetch project details' },
      { status: 500 }
    );
  }
}
