import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { message, chapter } = await request.json();
    const authToken = request.headers.get('Authorization');
    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const saveMessagesPromise = await fetch(
      process.env.BACKEND_URL + `/saveMessages`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
        body: JSON.stringify({
          message,
          chapter,
        }),
      }
    );
    await new Promise((res) =>
      setTimeout(() => {
        res(1);
      }, 2000)
    );
    const saveMessagesResponse = await saveMessagesPromise.json();
    return NextResponse.json(saveMessagesResponse, { status: 200 });
  } catch (error) {
    console.error('Error saving messages:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
