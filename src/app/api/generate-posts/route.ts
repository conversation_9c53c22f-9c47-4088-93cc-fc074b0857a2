import { NextResponse } from 'next/server';
import {
  handleServerResponse,
  handleServerError,
} from '@/utils/serverErrorHandlers';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const tenant = body.tenant;
    const chatId = body.chatId;
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const response = await fetch(
      `${process.env.BACKEND_URL}/generate-posts/${chatId}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: authHeader, // Forward the Bearer token
        },
        body: JSON.stringify({ platform: tenant }),
      }
    );
    // Handle the response, including 402 Payment Required errors
    return handleServerResponse(response);
  } catch (error) {
    console.error('Error generating posts:', error);
    return handleServerError(error, 'Failed to generate social media posts');
  }
}
