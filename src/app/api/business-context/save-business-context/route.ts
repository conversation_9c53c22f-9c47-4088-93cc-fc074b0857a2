import { NextResponse } from 'next/server';
import {
  handleServerResponse,
  handleServerError,
} from '@/utils/serverErrorHandlers';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const projectId = body.projectId;
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const response = await fetch(
      `${process.env.BACKEND_URL}/business-ideas/${projectId}/save_user_feedback`,
      {
        method: 'POST',
        body: JSON.stringify(body.content || {}),
        headers: {
          'Content-Type': 'application/json',
          Authorization: authHeader, // Forward the Bearer token
        },
      }
    );
    // Handle the response, including 402 Payment Required errors
    return handleServerResponse(response);
  } catch (error) {
    console.error('Error saving business context:', error);
    return handleServerError(error, 'Failed to save business context');
  }
}
