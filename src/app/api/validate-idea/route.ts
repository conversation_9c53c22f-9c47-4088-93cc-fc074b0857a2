import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const requestData = await request.json();
    const { prompt, website } = requestData;

    // Extract Authorization header from the incoming request
    const authHeader = request.headers.get('Authorization');

    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await fetch(`${process.env.BACKEND_URL}/business-ideas`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: authHeader, // Forward the Bearer token
      },
      body: JSON.stringify({
        user_input: prompt,
        website,
        ...requestData, // Include all fields from the request
      }),
    });

    if (!data.ok) {
      const responseJson = await data.json();
      return NextResponse.json(
        {
          message: responseJson.detail || 'An error occurred',
        },
        { status: data.status }
      );
    }
    const responseJson = await data.json();

    return NextResponse.json(responseJson, { status: 200 });
  } catch (error) {
    console.error('Error validating idea:', error);
    return NextResponse.json(
      { error: 'Failed to validate idea' },
      { status: 500 }
    );
  }
}
