import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  try {
    // Get the platform from the URL parameters
    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform');
    const chatId = searchParams.get('chatId');
    const authToken = request.headers.get('Authorization');
    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const redirectUriPromise = await fetch(
      process.env.BACKEND_URL + `/getRedirectionUrl`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken,
        },
        body: JSON.stringify({
          tenant: platform,
          conversation_id: chatId,
        }),
      }
    );
    const redirectUris = await redirectUriPromise.json();
    return NextResponse.json(redirectUris, { status: 200 });
  } catch (error) {
    console.error('Error generating redirect URL:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
