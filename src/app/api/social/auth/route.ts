import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { code, tenant, state } = await request.json();
    const authToken = request.headers.get('Authorization');
    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const response = await fetch(`${process.env.BACKEND_URL}/saveSocialLogin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: authToken,
      },
      body: JSON.stringify({ code, tenant, state }),
    });

    if (!response.ok) {
      throw new Error('Failed to save social login');
    }

    const data = await response.json();
    return NextResponse.json(data, { status: 200 });
  } catch (error: unknown) {
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Internal Server Error',
      },
      { status: 500 }
    );
  }
}
