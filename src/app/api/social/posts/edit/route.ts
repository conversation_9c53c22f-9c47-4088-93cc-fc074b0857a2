import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

interface PostRequest {
  id: string;
  content_text: string;
}

export async function POST(request: NextRequest) {
  try {
    const body = (await request.json()) as PostRequest;
    const { id: postId, content_text } = body;

    const authToken = request.headers.get('Authorization');
    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const response = await fetch(`${process.env.BACKEND_URL}/posts/${postId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: authToken, // Forward the Bearer token
      },
      body: JSON.stringify({ content_text }),
    });
    const data = await response.json();
    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    console.error('Error publishing post:', error);
    return NextResponse.json(
      { error: 'Failed to publish post' },
      { status: 500 }
    );
  }
}
