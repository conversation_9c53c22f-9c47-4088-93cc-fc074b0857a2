import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import {
  handleServerResponse,
  handleServerError,
} from '@/utils/serverErrorHandlers';

interface PostRequest {
  postId: string;
  platform: 'instagram' | 'twitter' | 'linkedin' | 'facebook' | 'whatsapp';
}

export async function POST(request: NextRequest) {
  try {
    const body = (await request.json()) as PostRequest;
    const { postId, platform } = body;

    const authToken = request.headers.get('Authorization');
    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const response = await fetch(
      `${process.env.BACKEND_URL}/publish/${postId}/${platform}`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: authToken, // Forward the Bearer token
        },
      }
    );
    // Handle the response, including 402 Payment Required errors
    return handleServerResponse(response);
  } catch (error) {
    console.error('Error publishing post:', error);
    return handleServerError(error, 'Failed to publish post');
  }
}
