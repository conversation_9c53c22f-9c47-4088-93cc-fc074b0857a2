import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  try {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const response = await fetch(process.env.BACKEND_URL + '/business-ideas', {
      headers: {
        'Content-Type': 'application/json',
        Authorization: authHeader || '', // Forward the Bearer token
      },
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    const { ideas, sample } = data;
    return NextResponse.json({ chats: ideas, projects: sample });
  } catch (error) {
    console.error('Error in /api/home:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
