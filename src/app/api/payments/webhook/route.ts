import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const razorpay_signature = request.headers.get('x-razorpay-signature');
    
    if (!razorpay_signature) {
      return NextResponse.json(
        { error: 'Missing Razorpay signature' },
        { status: 400 }
      );
    }
    
    // Call the backend API to handle the webhook
    const response = await fetch(`${process.env.BACKEND_URL}/api/payments/webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-razorpay-signature': razorpay_signature,
      },
      body: JSON.stringify(body),
    });
    
    if (!response.ok) {
      throw new Error('Failed to process webhook');
    }
    
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json(
      { error: 'Failed to process webhook' },
      { status: 500 }
    );
  }
}
