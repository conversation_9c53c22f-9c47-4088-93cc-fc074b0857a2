import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const projectId = body.projectId;
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const response = await fetch(
      `${process.env.BACKEND_URL}/business-ideas/${projectId}/get-context`,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: authHeader, // Forward the Bearer token
        },
      }
    );
    if (!response.ok) {
      throw new Error('Failed to fetch data from external API');
    }

    const data = await response.json();
    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      {
        data: null,
        status: 500,
        message:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
        success: false,
      },
      { status: 500 }
    );
  }
}
