import { NextResponse } from 'next/server';

export async function PUT(request: Request) {
  try {
    const body = await request.json();
    const {
      name,
      description,
      platforms,
      user_input,
      business_idea_id,
      start,
      end,
      campaign_id,
    } = body;
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const response = await fetch(
      `${process.env.BACKEND_URL}/campaigns/${campaign_id}`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: authHeader, // Forward the Bearer token
        },
        body: JSON.stringify({
          name,
          user_input,
          description,
          platforms,
          business_idea_id,
          schedule: {
            start_date: start,
            end_date: end,
          },
        }),
      }
    );
    if (!response.ok) {
      throw new Error('Failed to fetch data from external API');
    }

    const data = await response.json();
    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      {
        data: null,
        status: 500,
        message:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
        success: false,
      },
      { status: 500 }
    );
  }
}
