import { NextResponse } from 'next/server';
import {
  handleServerResponse,
  handleServerError,
} from '@/utils/serverErrorHandlers';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const {
      name,
      description,
      platforms,
      user_input,
      business_idea_id,
      start,
      end,
    } = body;
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const response = await fetch(`${process.env.BACKEND_URL}/campaigns`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: authHeader, // Forward the Bearer token
      },
      body: JSON.stringify({
        name,
        user_input,
        description,
        platforms,
        business_idea_id,
        schedule: {
          start_date: start,
          end_date: end,
        },
      }),
    });
    // Handle the response, including 402 Payment Required errors
    return handleServerResponse(response);
  } catch (error) {
    console.error('Error creating campaign:', error);
    return handleServerError(error, 'Failed to create campaign');
  }
}
