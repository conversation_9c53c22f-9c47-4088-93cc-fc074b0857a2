import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  try {
    const authHeader = request.headers.get('Authorization');
    const url = new URL(request.url);
    const campaign_id = url.searchParams.get('campaign_id');
    if (!campaign_id) {
      return NextResponse.json(
        { error: 'Campaign ID is required' },
        { status: 400 }
      );
    }
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const response = await fetch(
      `${process.env.BACKEND_URL}/campaigns/${campaign_id}/posts`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: authHeader,
        },
      }
    );
    if (!response.ok) {
      throw new Error('Failed to fetch data from external API');
    }

    const data = await response.json();
    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      {
        data: null,
        status: 500,
        message:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
        success: false,
      },
      { status: 500 }
    );
  }
}
