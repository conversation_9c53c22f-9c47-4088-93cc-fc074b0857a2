import { Outfit } from 'next/font/google';
import './globals.css';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import { GlobalProvider } from '@/context/GlobalContext';
import { SidebarProvider } from '@/context/SidebarContext';
import { ThemeProvider } from '@/context/ThemeContext';
import { AuthProvider } from '@/context/AuthContext';
import { ToastProvider } from '@/context/ToastContext';
import { CreditProvider } from '@/context/CreditContext';
import { ToastContainer } from '@/components/ui/toast/ToastContainer';
import { Metadata, Viewport } from 'next';
import { SpeedInsights } from '@vercel/speed-insights/next';

// Optimize font loading with display swap
const outfit = Outfit({
  variable: '--font-outfit-sans',
  subsets: ['latin'],
  display: 'swap',
  preload: true,
});

export const metadata: Metadata = {
  title: 'Dolze AI',
  description: 'Dolze AI - Your AI-powered business assistant',
  icons: {
    icon: '/images/favicon.ico',
    apple: '/images/favicon.ico',
    shortcut: '/images/favicon.ico',
  },
  // Add manifest for PWA capabilities
  manifest: '/manifest.json',
};

// Separate viewport configuration as required by Next.js 14
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  themeColor: '#4f46e5',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* Preconnect to critical domains to improve load time */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link rel="preconnect" href="https://plus.unsplash.com" />
        <link rel="preconnect" href="https://www.youtube.com" />
        <link rel="preconnect" href="https://i.ytimg.com" />

        {/* DNS prefetch for social media platforms that might be loaded later */}
        <link rel="dns-prefetch" href="https://www.facebook.com" />
        <link rel="dns-prefetch" href="https://x.com" />
        <link rel="dns-prefetch" href="https://instagram.com" />
        <link rel="dns-prefetch" href="https://linkedin.com" />

        {/* Preload critical assets */}
        <link
          rel="preload"
          href="images/icons/dolze-icon-black.svg"
          as="image"
          type="image/svg+xml"
        />

        {/* Favicon */}
        <link rel="icon" href="images/icons/dolze-icon-black.svg" />

        {/* Add meta tags for better SEO and social sharing */}
        <meta name="format-detection" content="telephone=no" />
        <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
      </head>
      <body className={`${outfit.variable} dark:bg-gray-900`}>
        <AuthProvider>
         <CreditProvider>
          <ThemeProvider>
            <GlobalProvider>
              <ToastProvider>
                <SidebarProvider>
                  {children}
                  <SpeedInsights />
                  <ToastContainer />
                </SidebarProvider>
              </ToastProvider>
            </GlobalProvider>
          </ThemeProvider>
         </CreditProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
