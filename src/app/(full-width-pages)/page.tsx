'use client';

import { useAuth } from '@/context/AuthContext';
import React, { useEffect, useState, useRef, lazy, Suspense } from 'react';
import { useTheme } from '@/context/ThemeContext';

import { Modal } from '@/components/ui/modal';
// Lazy load non-critical components
const Card = lazy(() => import('@/components/ui/card/HomePageCard'));
import CardSkeleton from '@/components/ui/card/CardSkeleton';
import { ChatbotProvider } from '@/components/ui/chatbot-new';
const Chatbot = lazy(() => import('@/components/ui/chatbot-new/Chatbot'));
import AppHeader from '@/layout/AppHeader';
import { apiService } from '@/services/api';
import { useGlobalContext } from '@/context/GlobalContext';
import Link from 'next/link';
import { withAuth } from '@/components/auth/ProtectedRoute';
import LoadingSpinner from '@/components/loading/LoadingSpinner';
import { ThemeToggleButton } from '@/components/common/ThemeToggleButton';

import { HomeApiResponse, ApiError } from '@/types';
import { API_ENDPOINT_HOME } from '@/constants';
import Button from '@/components/ui/button/Button';
import {
  FiFile,
  FiFolder,
  FiPlus,
  FiArrowRight,
  FiSearch,
  FiClock,
  FiStar,
  FiBookmark,
} from 'react-icons/fi';
import ThreeDotLoader from '@/components/loading/ThreeDotLoader';
import Head from 'next/head';

function HomePage() {
  const { user, loading } = useAuth();
  const [isChatModalOpen, setIsChatModalOpen] = useState(false);
  const { state, dispatch } = useGlobalContext();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('recent'); // 'recent' or 'sample'
  const projectsTabRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchHomeData = async () => {
      if (Array.isArray(state?.initProjects) && state?.initProjects?.length > 0)
        return;
      setError(null);
      setIsLoading(true);
      try {
        const { data } =
          await apiService.get<HomeApiResponse>(API_ENDPOINT_HOME);
        dispatch({
          type: 'SET_INIT_PROJECTS',
          payload: {
            chats: data?.chats,
            projects: data?.projects,
          },
        });
      } catch (error) {
        const apiError = error as ApiError;
        const errorMessage = apiError.message || 'Failed to fetch home data';
        console.error('Error fetching home data:', errorMessage);
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    // Use requestIdleCallback or setTimeout to defer non-critical operations
    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      (window as any).requestIdleCallback(() => fetchHomeData());
    } else {
      setTimeout(fetchHomeData, 200); // Fallback for browsers that don't support requestIdleCallback
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps
  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Error</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  const { theme } = useTheme();

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 flex flex-col">
      {/* Add page-specific metadata */}
      <Head>
        <meta
          name="description"
          content="Transform your ideas into reality with Dolze AI - Your AI-powered business assistant"
        />
        <meta property="og:title" content="Dolze AI - Dashboard" />
        <meta
          property="og:description"
          content="Validate concepts, gather feedback, and build your business with Dolze's powerful tools"
        />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Dolze AI - Dashboard" />
        <meta
          name="twitter:description"
          content="Validate concepts, gather feedback, and build your business with Dolze's powerful tools"
        />
      </Head>
      <AppHeader />

      {/* Theme Toggle Button */}
      <div className="fixed top-20 right-4 z-50">
        <ThemeToggleButton />
      </div>

      {/* Hero Section with Call to Action */}
      <div className="w-full pt-24 px-8 pb-16 relative overflow-hidden hero-section">
        {/* Mesh gradient elements */}
        <div className="absolute top-0 left-0 w-1/3 h-1/3 bg-[#e0c3fc]/20 dark:bg-[#e0c3fc]/10 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2 z-0 animate-pulse" style={{ animationDuration: '8s' }}></div>
        <div className="absolute bottom-0 right-0 w-1/2 h-1/2 bg-[#8ec5fc]/15 dark:bg-[#8ec5fc]/10 rounded-full blur-3xl translate-x-1/4 translate-y-1/4 z-0 animate-pulse" style={{ animationDuration: '12s' }}></div>
        <div className="absolute top-1/2 right-1/4 w-1/4 h-1/4 bg-[#e0c3fc]/20 dark:bg-[#e0c3fc]/15 rounded-full blur-3xl z-0 animate-pulse" style={{ animationDuration: '10s' }}></div>
        {/* Content container */}
        <div className={`relative z-10 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>



        <style jsx global>{`
          /* Stripe-inspired hero section with animated gradient */
          .hero-section {
            position: relative;
            isolation: isolate;
          }

          .hero-section::before {
            content: '';
            position: absolute;
            inset: 0;
            background: ${theme === 'dark' ? '#101828' : '#ffffff'};
            z-index: -2;
          }

          .hero-section::after {
            content: '';
            position: absolute;
            inset: -50%;
            width: 200%;
            height: 200%;
            background: ${theme === 'dark'
              ? 'conic-gradient(from 90deg at 40% 50%, #e0c3fc 0deg, #8ec5fc 90deg, #e0c3fc 180deg, #8ec5fc 270deg, #e0c3fc 360deg)'
              : 'conic-gradient(from 90deg at 40% 50%, #e0c3fc 0deg, #8ec5fc 90deg, #e0c3fc 180deg, #8ec5fc 270deg, #e0c3fc 360deg)'};
            opacity: ${theme === 'dark' ? '0.5' : '0.7'};
            animation: rotate 20s linear infinite;
            filter: blur(${theme === 'dark' ? '80px' : '60px'});
            z-index: -1;
          }

          /* Additional gradient overlay for more vibrancy */
          .hero-section::before {
            content: '';
            position: absolute;
            inset: 0;
            background: ${theme === 'dark' ? '#101828' : '#ffffff'};
            background-image: ${theme === 'dark'
              ? 'radial-gradient(circle at 20% 30%, rgba(224, 195, 252, 0.15) 0%, transparent 50%), radial-gradient(circle at 80% 70%, rgba(142, 197, 252, 0.1) 0%, transparent 50%)'
              : 'radial-gradient(circle at 20% 30%, rgba(224, 195, 252, 0.15) 0%, transparent 50%), radial-gradient(circle at 80% 70%, rgba(142, 197, 252, 0.15) 0%, transparent 50%)'};
            z-index: -2;
          }

          @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
        `}</style>
        <div className="max-w-6xl mx-auto relative z-10">
          <div className="flex flex-col md:flex-row items-center justify-between gap-8">
            <div className="md:w-1/2 space-y-6 relative">
              <h1 className="text-title md:text-title lg:text-title font-bold leading-tight font-apercu">
                <span className="text-primary-500">Transform Your Ideas Into Reality</span>
              </h1>
              <p className={`text-heading-2 max-w-lg font-apercu ${theme === 'dark' ? 'text-white/90' : 'text-gray-700'}`}>
                Validate concepts, gather feedback, and build your business with
                Dolze's powerful tools.
              </p>
              <div className="flex flex-wrap gap-4 pt-6">
                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => setIsChatModalOpen(true)}
                  startIcon={<FiPlus />}
                >
                  New Project
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  endIcon={<FiArrowRight />}
                  onClick={() => {
                    setActiveTab('sample');
                    // Scroll to the projects tab section with smooth behavior
                    setTimeout(() => {
                      if (projectsTabRef.current) {
                        // Add scroll margin to account for header height
                        const yOffset = -80; // 10px margin from the header
                        const element = projectsTabRef.current;
                        const y =
                          element.getBoundingClientRect().top +
                          window.scrollY +
                          yOffset;

                        window.scrollTo({ top: y, behavior: 'smooth' });
                      }
                    }, 100);
                  }}
                >
                  Explore Samples
                </Button>
              </div>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <div className={`relative w-full max-w-md p-6 backdrop-blur-sm rounded-lg shadow-xl z-10 ${theme === 'dark'
                ? 'bg-gray-900/80 border border-gray-700 hover:border-primary-700/50 transition-all duration-300'
                : 'bg-white/90 border border-primary-100 hover:border-primary-300/50 transition-all duration-300'}`}>
                {/* Decorative elements */}
                <div className="absolute -top-6 -right-6 w-12 h-12 rounded-full bg-primary-500/20 dark:bg-primary-500/10 blur-lg"></div>
                <div className="absolute -bottom-4 -left-4 w-8 h-8 rounded-full bg-primary-400/20 dark:bg-primary-400/10 blur-lg"></div>
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-3 h-3 rounded-full bg-red-400"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
                  <div className="w-3 h-3 rounded-full bg-green-400"></div>
                </div>
                <div
                  onClick={() => setIsChatModalOpen(true)}
                  className="cursor-pointer"
                  // Add loading priority to this section as it's in the viewport
                  data-loading-priority="high"
                >
                  <div className={`backdrop-blur-sm rounded-lg p-4 mb-3 transform transition-all duration-300 hover:-translate-y-1 ${theme === 'dark'
                    ? 'bg-gray-800/90 text-gray-100 border border-gray-700 hover:border-primary-700/50'
                    : 'bg-gray-100/90 text-gray-800 border border-gray-200 hover:border-primary-300/50'}`}>
                    <p className="font-apercu text-heading-2">
                      I have an idea for a sustainable fashion marketplace...
                    </p>
                  </div>
                  <div className={`backdrop-blur-sm rounded-lg p-4 mb-3 transform transition-all duration-300 hover:-translate-y-1 ${theme === 'dark'
                    ? 'bg-gray-700/80 text-gray-100 border border-gray-600 hover:border-primary-600/50'
                    : 'bg-primary-50/80 text-gray-800 border border-primary-100 hover:border-primary-200/70'}`}>
                    <div className="flex items-start gap-2">
                      <div className="w-6 h-6 rounded-full bg-primary-500/20 flex items-center justify-center flex-shrink-0 mt-1">
                        <span className="text-xs text-primary-500 font-bold">AI</span>
                      </div>
                      <p className="font-apercu text-heading-2">
                        That sounds promising! Let's explore the market
                        potential...
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <div className={`backdrop-blur-sm rounded-lg p-3 inline-flex items-center gap-2 transition-all duration-300 hover:shadow-md ${theme === 'dark'
                      ? 'bg-gray-800/90 text-gray-100 border border-gray-700 hover:border-primary-700/50'
                      : 'bg-gray-100/90 text-gray-800 border border-gray-200 hover:border-primary-300/50'}`}>
                      <FiSearch className="text-primary-500 opacity-80" />
                      <span className="font-apercu text-info">Type your idea here...</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>

      {/* Projects Section with Tabs */}
      <div className="flex-1 overflow-y-auto px-4 lg:px-8 py-8 bg-white dark:bg-gray-900">
        <div className="max-w-6xl mx-auto">
          {/* Stats Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            {/* Recent Projects */}
            <div className="bg-gradient-to-br from-indigo-100 to-indigo-50 dark:from-indigo-900/30 dark:to-indigo-800/20 rounded-lg p-8 relative overflow-hidden shadow-sm">
              <div className="absolute top-0 right-0 w-32 h-32 -mr-8 -mt-8 opacity-10">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-full w-full text-indigo-600 dark:text-indigo-400"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-1-10v4h2v-4h3l-4-4-4 4h3z" />
                </svg>
              </div>
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 flex items-center justify-center bg-indigo-200 dark:bg-indigo-800/50 rounded-full mr-4">
                  <FiClock className="h-6 w-6 text-indigo-600 dark:text-indigo-300" />
                </div>
                <span className="text-gray-600 dark:text-gray-300 text-heading-2 font-apercu">Recent Projects</span>
              </div>
              <div className="text-heading-1 font-bold text-gray-900 dark:text-white font-apercu">
                {isLoading ? <ThreeDotLoader /> : (state.initProjects?.chats || []).length}
              </div>
            </div>

            {/* Sample Projects */}
            <div className="bg-gradient-to-br from-purple-50 to-purple-50/50 dark:from-purple-900/30 dark:to-purple-800/20 rounded-lg p-8 relative overflow-hidden shadow-sm">
              <div className="absolute top-0 right-0 w-32 h-32 -mr-8 -mt-8 opacity-10">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-full w-full text-purple-600 dark:text-purple-400"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm-3-7h8v2H9v-2zm2-4h4v2h-4V9z" />
                </svg>
              </div>
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 flex items-center justify-center bg-purple-200 dark:bg-purple-800/50 rounded-full mr-4">
                  <FiStar className="h-6 w-6 text-purple-600 dark:text-purple-300" />
                </div>
                <span className="text-gray-600 dark:text-gray-300 text-heading-2 font-apercu">Sample Projects</span>
              </div>
              <div className="text-heading-1 font-bold text-gray-900 dark:text-white font-apercu">
                {isLoading ? <ThreeDotLoader /> : (state.initProjects?.projects || []).length}
              </div>
            </div>

            {/* Total Projects */}
            <div className="bg-gradient-to-br from-green-50 to-green-50/50 dark:from-green-900/30 dark:to-green-800/20 rounded-lg p-8 relative overflow-hidden shadow-sm">
              <div className="absolute top-0 right-0 w-32 h-32 -mr-8 -mt-8 opacity-10">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-full w-full text-green-600 dark:text-green-400"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm-3-7h8v2H9v-2zm2-4h4v2h-4V9z" />
                </svg>
              </div>
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 flex items-center justify-center bg-green-200 dark:bg-green-800/50 rounded-full mr-4">
                  <FiBookmark className="h-6 w-6 text-green-600 dark:text-green-300" />
                </div>
                <span className="text-gray-600 dark:text-gray-300 text-heading-2 font-apercu">Total Projects</span>
              </div>
              <div className="text-heading-1 font-bold text-gray-900 dark:text-white font-apercu">
                {isLoading ? <ThreeDotLoader /> : (
                  (state.initProjects?.chats || []).length + (state.initProjects?.projects || []).length
                )}
              </div>
            </div>
          </div>

          {/* Projects Tabs */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
            <div className="border-b border-gray-100 dark:border-gray-700" ref={projectsTabRef}>
              <div className="flex">
                <button
                  onClick={() => setActiveTab('recent')}
                  className={`px-6 py-4 text-heading-3 font-apercu font-medium flex items-center gap-2 transition-all duration-200 ${
                    activeTab === 'recent'
                      ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-500'
                      : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <FiClock
                    className={
                      activeTab === 'recent'
                        ? 'text-primary-500 dark:text-primary-400'
                        : 'text-gray-400 dark:text-gray-500'
                    }
                  />
                  Recent Projects
                </button>
                <button
                  onClick={() => setActiveTab('sample')}
                  className={`px-6 py-4 text-heading-3 font-apercu font-medium flex items-center gap-2 transition-all duration-200 ${
                    activeTab === 'sample'
                      ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-500'
                      : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <FiStar
                    className={
                      activeTab === 'sample'
                        ? 'text-primary-500 dark:text-primary-400'
                        : 'text-gray-400 dark:text-gray-500'
                    }
                  />
                  Sample Projects
                </button>
              </div>
            </div>

            <div className="p-6">
              {isLoading ? (
                // Shimmer loading effect
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(6)].map((_, index) => (
                    <CardSkeleton key={`skeleton-${index}`} />
                  ))}
                </div>
              ) : activeTab === 'recent' ? (
                // Recent Projects Tab Content
                <div>
                  {(state.initProjects?.chats || []).length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {(state.initProjects?.chats || []).map((project) => (
                        <Link href={`/home?id=${project.id}`} key={project.id}>
                          <Suspense fallback={<CardSkeleton />}>
                            <Card
                              id={project.id}
                              img_url={
                                project?.image ||
                                'https://plus.unsplash.com/premium_vector-1682269608279-c30dcfc02e95?w=352&dpr=2&h=367&auto=format&fit=crop&q=60&ixlib=rb-4.0.3'
                              }
                              title={project.title || 'No Title'}
                              status={project.status ? 'active' : 'inactive'}
                              description={
                                project.description || 'No Description'
                              }
                              tags={project.tags || []}
                              category={project.category || 'Uncategorized'}
                              updated_at={project.updated_at}
                            />
                          </Suspense>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    // No recent projects state
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <div className="mb-6 bg-primary-50 dark:bg-primary-900/20 p-5 rounded-full border border-primary-100 dark:border-primary-800/30 relative overflow-hidden group transition-all duration-300 hover:shadow-lg">
                        <div className="absolute inset-0 bg-gradient-to-br from-primary-200/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <FiFile className="w-12 h-12 text-primary-500 dark:text-primary-400 opacity-80 relative z-10" />
                      </div>

                      {/* Educational content */}
                      <h3 className="text-heading-1 font-semibold text-gray-800 dark:text-gray-100 mb-3 font-apercu">
                        Get Started with Your First Project
                      </h3>
                      <p className="text-heading-2 text-gray-600 dark:text-gray-300 mb-8 max-w-lg font-apercu">
                        Projects help you validate your ideas quickly. Create
                        your first one to start testing concepts, gathering
                        feedback, and turning your vision into reality.
                      </p>

                      {/* Mini-checklist */}
                      <div className="bg-primary-50/50 dark:bg-gray-800/80 p-6 rounded-xl w-full max-w-lg mb-8 border border-primary-100 dark:border-gray-700 shadow-sm relative overflow-hidden">
                        <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-bl from-primary-200/20 via-primary-100/10 to-transparent -mr-20 -mt-20 opacity-50 dark:opacity-20"></div>
                        <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-4 text-left text-heading-2 font-apercu">
                          Quick Start Guide:
                        </h4>
                        <div className="space-y-4">
                          <div className="flex items-start space-x-4">
                            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center relative overflow-hidden group transition-all duration-300 hover:shadow-sm">
                              <div className="absolute inset-0 bg-gradient-to-br from-primary-200/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                              <span className="text-sm text-primary-600 dark:text-primary-400 font-bold relative z-10">
                                1
                              </span>
                            </div>
                            <div className="text-left">
                              <p className="text-gray-800 dark:text-gray-200 font-medium text-heading-3 font-apercu">
                                Create your first project
                              </p>
                              <p className="text-gray-500 dark:text-gray-400 text-info font-apercu">
                                Describe your idea and let Dolze help you
                                validate it
                              </p>
                            </div>
                          </div>
                          <div className="flex items-start space-x-4">
                            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center relative overflow-hidden group transition-all duration-300 hover:shadow-sm">
                              <div className="absolute inset-0 bg-gradient-to-br from-gray-200/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                              <span className="text-sm text-gray-500 dark:text-gray-400 font-bold relative z-10">
                                2
                              </span>
                            </div>
                            <div className="text-left">
                              <p className="text-gray-600 dark:text-gray-300 font-medium text-heading-3 font-apercu">
                                Define your target audience
                              </p>
                              <p className="text-gray-500 dark:text-gray-400 text-info font-apercu">
                                Identify who your product or service is for
                              </p>
                            </div>
                          </div>
                          <div className="flex items-start space-x-4">
                            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center relative overflow-hidden group transition-all duration-300 hover:shadow-sm">
                              <div className="absolute inset-0 bg-gradient-to-br from-gray-200/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                              <span className="text-sm text-gray-500 dark:text-gray-400 font-bold relative z-10">
                                3
                              </span>
                            </div>
                            <div className="text-left">
                              <p className="text-gray-600 dark:text-gray-300 font-medium text-heading-3 font-apercu">
                                Gather feedback and iterate
                              </p>
                              <p className="text-gray-500 dark:text-gray-400 text-info font-apercu">
                                Use insights to refine and improve your concept
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Create project button */}
                      <Button
                        variant="primary"
                        size="lg"
                        onClick={() => setIsChatModalOpen(true)}
                        startIcon={<FiPlus />}
                        className="mt-2"
                      >
                        Start Your First Project
                      </Button>

                      <p className="mt-6 text-info text-gray-500 dark:text-gray-400 font-apercu">
                        Or check out the{' '}
                        <button
                          onClick={() => setActiveTab('sample')}
                          className="text-primary-500 dark:text-primary-400 hover:underline font-apercu transition-colors duration-200"
                        >
                          sample projects
                        </button>{' '}
                        for inspiration
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                // Sample Projects Tab Content
                <div>
                  {(state.initProjects?.projects || []).length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {(state.initProjects?.projects || []).map((project) => (
                        <Link href={`/home?id=${project.id}`} key={project.id}>
                          <Suspense fallback={<CardSkeleton />}>
                            <Card
                              id={project.id}
                              img_url={
                                project.image ||
                                'https://plus.unsplash.com/premium_vector-1682269608279-c30dcfc02e95?w=352&dpr=2&h=367&auto=format&fit=crop&q=60&ixlib=rb-4.0.3'
                              }
                              title={project.title || 'No Title'}
                              status={
                                project.status === 'Active'
                                  ? 'active'
                                  : 'inactive'
                              }
                              description={
                                project.description || 'No Description'
                              }
                              tags={project.tags || []}
                              category={project.category || 'Uncategorized'}
                              updated_at={project.updated_at}
                            />
                          </Suspense>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    // No sample projects state
                    <div className="flex flex-col items-center justify-center py-16 text-center">
                      <div className="mb-6 bg-primary-50 dark:bg-primary-900/20 p-5 rounded-full border border-primary-100 dark:border-primary-800/30 relative overflow-hidden group transition-all duration-300 hover:shadow-lg">
                        <div className="absolute inset-0 bg-gradient-to-br from-primary-200/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <FiFolder className="w-12 h-12 text-primary-500 dark:text-primary-400 opacity-80 relative z-10" />
                      </div>
                      <h3 className="text-heading-1 font-semibold text-gray-800 dark:text-gray-100 mb-3 font-apercu">
                        Sample Projects Coming Soon
                      </h3>
                      <p className="text-heading-2 text-gray-600 dark:text-gray-300 mb-6 max-w-lg font-apercu">
                        We're working on adding sample projects to help inspire
                        you. In the meantime, why not create your own project?
                      </p>
                      <Button
                        variant="primary"
                        size="lg"
                        onClick={() => setIsChatModalOpen(true)}
                        startIcon={<FiPlus />}
                        className="mt-2"
                      >
                        Create New Project
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Only render the modal when it's open to save resources */}
      {isChatModalOpen && (
        <Modal
          isOpen={isChatModalOpen}
          onClose={() => setIsChatModalOpen(false)}
          className="max-w-6xl mx-auto h-[85vh] relative bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-2xl border border-gray-100 dark:border-gray-700"
          title={<span className="font-apercu text-heading-1 font-semibold">Create a New Project</span>}
          subtitle={<span className="font-apercu text-heading-2 text-gray-600 dark:text-gray-300">Describe your idea and Dolze will help you validate and develop it</span>}
        >
          <Suspense fallback={<LoadingSpinner />}>
            <ChatbotProvider initialNodeId="welcome">
              <Chatbot
                userPhotoURL={user?.photoURL || ''}
                userName={user?.displayName?.trim().split(' ')[0] || ''}
              />
            </ChatbotProvider>
          </Suspense>
        </Modal>
      )}
    </div>
  );
}
export default withAuth(HomePage);
