'use client';
import { ThemeProvider } from '@/context/ThemeContext';
import React from 'react';
import OptimizedImage from '@/components/ui/image/OptimizedImage';
export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="relative p-6 bg-white z-1 dark:bg-gray-900 sm:p-0">
      <ThemeProvider>
        <div className="relative flex lg:flex-row w-full h-screen justify-center flex-col dark:bg-gray-900 sm:p-0">
          {/* Left Side - Sign In / Sign Up Form */}
          {children}

          {/* Right Side - Full-Screen Image */}
          <div className="lg:w-1/2 w-full h-full hidden lg:block">
            <OptimizedImage
              width={1000}
              height={1000}
              src="/images/auth/auth1.jpg"
              alt="Authentication Illustration"
              className="w-full h-full object-cover"
            />
          </div>
        </div>
      </ThemeProvider>
    </div>
  );
}
