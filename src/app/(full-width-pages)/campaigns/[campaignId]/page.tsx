'use client';

import React, { Suspense, useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { useGlobalContext } from '@/context/GlobalContext';
import { apiService } from '@/services/api';
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  Ta<PERSON>Trigger,
} from '@/components/ui/tabs/tabs';
import { FiInstagram, FiTwitter, FiLinkedin, FiFacebook } from 'react-icons/fi';
import { FaWhatsapp } from 'react-icons/fa';
import { useToast } from '@/context/ToastContext';
import Link from 'next/link';
import ThreeDotLoader from '@/components/loading/ThreeDotLoader';
import dynamic from 'next/dynamic';

// Import post components
const TwitterPost = dynamic(
  () => import('@/components/socialMedia/posts/twitterPosts'),
  { ssr: false }
);
const InstagramPost = dynamic(
  () => import('@/components/socialMedia/posts/instagramPosts'),
  { ssr: false }
);
const LinkedInPost = dynamic(
  () => import('@/components/socialMedia/posts/linkedinPosts'),
  { ssr: false }
);
const FacebookPost = dynamic(
  () => import('@/components/socialMedia/posts/facebookPosts'),
  { ssr: false }
);
const WhatsAppPost = dynamic(
  () => import('@/components/socialMedia/posts/whatsappPosts'),
  { ssr: false }
);
import Button from '@/components/ui/button/Button';
import { Breadcrumb } from '@/components/ui/breadcrumb/breadcrumb';

interface Posts {
  instagramPosts: any[];
  twitterPosts: any[];
  linkedinPosts: any[];
  facebookPosts: any[];
  whatsappPosts: any[];
}
interface Campaign {
  id: string;
  name: string;
  user_input: string;
  description: string | null;
  goals: string | null;
  number_of_days: number;
  posts_per_day: number;
  platforms: string[];
  post_types: string[];
  status: string;
  created_at: string;
  updated_at: string;
  schedule: {
    start_date?: string;
    end_date?: string;
  } | null;
}
interface CampaignPostsResponse {
  posts: Posts;
  campaign: Campaign;
}

const CampaignDetailPage = () => {
  const { campaignId } = useParams();
  const searchParams = useSearchParams();
  const projectId = searchParams.get('id') || '';
  const { state } = useGlobalContext();
  const { showToast } = useToast();

  const [loading, setLoading] = useState(true);
  const [campaign, setCampaign] = useState<any>(null);
  const [posts, setPosts] = useState<Posts>({
    instagramPosts: [],
    twitterPosts: [],
    linkedinPosts: [],
    facebookPosts: [],
    whatsappPosts: [],
  });

  // Fetch campaign posts
  useEffect(() => {
    const fetchCampaignPosts = async () => {
      try {
        setLoading(true);
        const { data } = await apiService.get<CampaignPostsResponse>(
          `/api/campaigns/get-campaign-posts?campaign_id=${campaignId}`
        );
        setPosts(data.posts);
        setCampaign(data.campaign);
      } catch (error) {
        console.error('Error fetching campaign posts:', error);
        showToast('Failed to load campaign posts', 'error');
      } finally {
        setLoading(false);
      }
    };

    if (campaignId) {
      fetchCampaignPosts();
    }
  }, [campaignId, showToast]);

  // Get connected accounts from state
  const getConnectedAccounts = () => {
    return state?.projectDetails?.data?.connectedAccounts || {};
  };

  if (!campaign && !loading) {
    return (
      <div className="p-8 text-center">
        <h1 className="text-2xl font-bold mb-4">Campaign not found</h1>
        <p className="mb-6">
          The campaign you're looking for doesn't exist or you don't have access
          to it.
        </p>
        <Link href={`/socials/campaigns?id=${projectId}`}>
          <Button variant="primary">Back to Campaigns</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Breadcrumb */}
      <Breadcrumb
        items={[
          { label: 'Home', href: `/home?id=${projectId}` },
          { label: campaign?.name || 'Campaign Details', href: '#' },
        ]}
        className="mb-6"
      />

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <ThreeDotLoader />
        </div>
      ) : (
        <>
          {/* Campaign Header */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
            <div className="flex justify-between items-start">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  {campaign?.name}
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {campaign?.user_input}
                </p>
                <div className="flex flex-wrap gap-2 mb-4">
                  {campaign?.platforms?.map((platform: string) => (
                    <span
                      key={platform}
                      className="px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100"
                    >
                      {platform.charAt(0).toUpperCase() + platform.slice(1)}
                    </span>
                  ))}
                </div>
                {(campaign?.schedule?.start_date || campaign?.start) && (
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <span className="mr-4">
                      <strong>Start:</strong>{' '}
                      {new Date(
                        campaign?.schedule?.start_date || campaign?.start
                      ).toLocaleDateString()}
                    </span>
                    <span>
                      <strong>End:</strong>{' '}
                      {new Date(
                        campaign?.schedule?.end_date || campaign?.end
                      ).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </div>
              <div className="flex gap-2">
                <Link href={`/socials/campaigns?id=${projectId}`}>
                  <Button variant="outline">Back to Campaigns</Button>
                </Link>
              </div>
            </div>
          </div>

          {/* Campaign Posts */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              Campaign Posts
            </h2>

            <Tabs defaultValue="instagram" className="w-full">
              <TabsList className="mb-6">
                <TabsTrigger
                  value="instagram"
                  className="flex items-center gap-2"
                >
                  <FiInstagram className="text-[#E1306C]" />
                  <span>Instagram</span>
                  <span className="ml-1 px-1.5 py-0.5 text-xs rounded-full bg-gray-200 dark:bg-gray-700">
                    {posts.instagramPosts.length}
                  </span>
                </TabsTrigger>
                <TabsTrigger
                  value="twitter"
                  className="flex items-center gap-2"
                >
                  <FiTwitter className="text-[#1DA1F2]" />
                  <span>Twitter</span>
                  <span className="ml-1 px-1.5 py-0.5 text-xs rounded-full bg-gray-200 dark:bg-gray-700">
                    {posts.twitterPosts.length}
                  </span>
                </TabsTrigger>
                <TabsTrigger
                  value="linkedin"
                  className="flex items-center gap-2"
                >
                  <FiLinkedin className="text-[#0077B5]" />
                  <span>LinkedIn</span>
                  <span className="ml-1 px-1.5 py-0.5 text-xs rounded-full bg-gray-200 dark:bg-gray-700">
                    {posts.linkedinPosts.length}
                  </span>
                </TabsTrigger>
                <TabsTrigger
                  value="facebook"
                  className="flex items-center gap-2"
                >
                  <FiFacebook className="text-[#1877F2]" />
                  <span>Facebook</span>
                  <span className="ml-1 px-1.5 py-0.5 text-xs rounded-full bg-gray-200 dark:bg-gray-700">
                    {posts.facebookPosts.length}
                  </span>
                </TabsTrigger>
                <TabsTrigger
                  value="whatsapp"
                  className="flex items-center gap-2"
                >
                  <FaWhatsapp className="text-[#25D366]" />
                  <span>WhatsApp</span>
                  <span className="ml-1 px-1.5 py-0.5 text-xs rounded-full bg-gray-200 dark:bg-gray-700">
                    {posts.whatsappPosts.length}
                  </span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="instagram" className="mt-6">
                <Suspense fallback={<ThreeDotLoader />}>
                  {posts.instagramPosts.length > 0 ? (
                    <div
                      key={posts.instagramPosts[0].campaign_id}
                      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                    >
                      {posts.instagramPosts.map((post) => (
                        <InstagramPost
                          key={post.campaign_id}
                          post={post}
                          platform="instagram"
                          isConnected={!!getConnectedAccounts().instagram?.id}
                          username={
                            getConnectedAccounts().instagram?.username || ''
                          }
                          displayPicture={
                            getConnectedAccounts().instagram
                              ?.profile_picture_url || ''
                          }
                          name={getConnectedAccounts().instagram?.name || ''}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-10">
                      <p className="text-gray-500 dark:text-gray-400">
                        No Instagram posts for this campaign
                      </p>
                    </div>
                  )}
                </Suspense>
              </TabsContent>

              <TabsContent value="twitter" className="mt-6">
                <Suspense fallback={<ThreeDotLoader />}>
                  {posts.twitterPosts.length > 0 ? (
                    <div
                      key={posts.twitterPosts[0].campaign_id}
                      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                    >
                      {posts.twitterPosts.map((post) => (
                        <TwitterPost
                          key={post.campaign_id}
                          post={post}
                          platform="twitter"
                          isConnected={!!getConnectedAccounts().twitter?.id}
                          username={
                            getConnectedAccounts().twitter?.username || ''
                          }
                          displayPicture={
                            getConnectedAccounts().twitter
                              ?.profile_picture_url || ''
                          }
                          name={getConnectedAccounts().twitter?.name || ''}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-10">
                      <p className="text-gray-500 dark:text-gray-400">
                        No Twitter posts for this campaign
                      </p>
                    </div>
                  )}
                </Suspense>
              </TabsContent>
              <TabsContent value="linkedin" className="mt-6">
                <Suspense fallback={<ThreeDotLoader />}>
                  {posts.linkedinPosts.length > 0 ? (
                    <div
                      key={posts.linkedinPosts[0].campaign_id}
                      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                    >
                      {posts.linkedinPosts.map((post) => (
                        <LinkedInPost
                          key={post.campaign_id}
                          post={post}
                          platform="linkedin"
                          isConnected={!!getConnectedAccounts().linkedin?.id}
                          username={
                            getConnectedAccounts().linkedin?.username || ''
                          }
                          displayPicture={
                            getConnectedAccounts().linkedin
                              ?.profile_picture_url || ''
                          }
                          name={getConnectedAccounts().linkedin?.name || ''}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-10">
                      <p className="text-gray-500 dark:text-gray-400">
                        No LinkedIn posts for this campaign
                      </p>
                    </div>
                  )}
                </Suspense>
              </TabsContent>

              <TabsContent value="facebook" className="mt-6">
                <Suspense fallback={<ThreeDotLoader />}>
                  {posts.facebookPosts.length > 0 ? (
                    <div
                      key={posts.facebookPosts[0].campaign_id}
                      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                    >
                      {posts.facebookPosts.map((post) => (
                        <FacebookPost
                          key={post.campaign_id}
                          post={post}
                          platform="facebook"
                          isConnected={!!getConnectedAccounts().facebook?.id}
                          username={
                            getConnectedAccounts().facebook?.username || ''
                          }
                          displayPicture={
                            getConnectedAccounts().facebook
                              ?.profile_picture_url || ''
                          }
                          name={getConnectedAccounts().facebook?.name || ''}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-10">
                      <p className="text-gray-500 dark:text-gray-400">
                        No Facebook posts for this campaign
                      </p>
                    </div>
                  )}
                </Suspense>
              </TabsContent>

              <TabsContent value="whatsapp" className="mt-6">
                <Suspense fallback={<ThreeDotLoader />}>
                  {posts.whatsappPosts.length > 0 ? (
                    <div
                      key={posts.whatsappPosts[0]._id}
                      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                    >
                      {posts.whatsappPosts.map((post) => (
                        <WhatsAppPost
                          key={post._id}
                          post={post}
                          platform="whatsapp"
                          isConnected={!!getConnectedAccounts().whatsapp?.id}
                          username={
                            getConnectedAccounts().whatsapp?.username || ''
                          }
                          displayPicture={
                            getConnectedAccounts().whatsapp
                              ?.profile_picture_url || ''
                          }
                          name={getConnectedAccounts().whatsapp?.name || ''}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-10">
                      <p className="text-gray-500 dark:text-gray-400">
                        No WhatsApp posts for this campaign
                      </p>
                    </div>
                  )}
                </Suspense>
              </TabsContent>
            </Tabs>
          </div>
        </>
      )}
    </div>
  );
};

export default CampaignDetailPage;
