import React from 'react';
import { render, screen } from '@testing-library/react';
import LandingPage from '../page';

// Mock Next.js Link component
jest.mock('next/link', () => {
  const LinkComponent = ({
    children,
    href,
  }: {
    children: React.ReactNode;
    href: string;
  }) => {
    return <a href={href}>{children}</a>;
  };
  LinkComponent.displayName = 'MockedNextLink';
  return LinkComponent;
});

describe('LandingPage Component', () => {
  it('renders the page title correctly', () => {
    render(<LandingPage />);
    expect(
      screen.getByText('Build a Website That Converts')
    ).toBeInTheDocument();
  });

  it('renders the page description correctly', () => {
    render(<LandingPage />);
    expect(
      screen.getByText(
        /Create, customize, and optimize stunning landing pages that captivate visitors and drive action/
      )
    ).toBeInTheDocument();
  });

  it('renders the landing page builder label', () => {
    render(<LandingPage />);
    expect(screen.getByText('Landing Page Builder')).toBeInTheDocument();
  });

  it('renders all three landing page cards', () => {
    render(<LandingPage />);
    expect(screen.getByText('Create Your Landing Page')).toBeInTheDocument();
    expect(screen.getByText('Preview Your Landing Page')).toBeInTheDocument();
  });

  it('renders card descriptions correctly', () => {
    render(<LandingPage />);
    expect(
      screen.getByText(
        'Design a stunning landing page from scratch with our intuitive editor.'
      )
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        'See how your landing page looks and make adjustments in real-time.'
      )
    ).toBeInTheDocument();
  });

  it('renders icons in each card', () => {
    render(<LandingPage />);
    // The SectionPageCard component renders icons differently now
    // Check for the presence of the icons by their function names
    expect(screen.getByText('Create Your Landing Page')).toBeInTheDocument();
    expect(screen.getByText('Preview Your Landing Page')).toBeInTheDocument();
    expect(screen.getByText('Deploy')).toBeInTheDocument();
  });

  it('renders cards with hover effects', () => {
    const { container } = render(<LandingPage />);
    // The SectionPageCard component uses different classes now
    const cards = container.querySelectorAll('div[class*="hover:shadow-md"]');
    expect(cards.length).toBeGreaterThan(0);
  });
});
