'use client';

import React, { useRef, useState, useCallback, Suspense } from 'react';
import { useModal } from '@/hooks/useModal';
import dynamic from 'next/dynamic';
import { Modal } from '@/components/ui/modal';
import Button from '@/components/ui/button/Button';
import { FiMonitor, FiPlus, FiRefreshCcw, FiSmartphone } from 'react-icons/fi';
import { useGlobalContext } from '@/context/GlobalContext';
import { useToast } from '@/context/ToastContext';
import { useSearchParams } from 'next/navigation';
import * as ActionTypes from '@/constants/actions/actions.constants';
const LandingPageLoader = dynamic(
  () => import('@/components/loading/LandingPageLoader')
);
import { apiService } from '@/services/api';
import ThreeDotLoader from '@/components/loading/ThreeDotLoader';
import { fetchSecondFoldProjectDetails } from '@/store/actions/projectActions';
import { LOADING_STATE_LANDING_PAGE_GENERATION } from '@/constants/actions/actions.constants';
import LoadingSpinner from '@/components/loading/LoadingSpinner';
import CardWithAction from '@/components/ui/utils/CardWithAction';
import { BUTTON_GENERATE } from '@/constants';
import { PlusIcon } from '@/icons';
import { generateContext } from '@/store/actions/contextActions';
interface LandingPage {
  html?: string;
  public_url: string;
}

const LandingPage: React.FC = () => {
  const { state, dispatch } = useGlobalContext();
  const { showToast } = useToast();
  const searchParams = useSearchParams();
  const projectId = searchParams.get('id');
  const isMobileViewPortEnabled = window.innerWidth < 1024;

  const [isRefreshing, setIsRefreshing] = React.useState(false);

  const handleRefresh = async () => {
    if (!projectId) {
      showToast('Project ID is missing', 'error');
      return;
    }

    setIsRefreshing(true);

    try {
      // Refresh project data using both API calls
      await Promise.all([
        fetchSecondFoldProjectDetails(projectId, dispatch, showToast),
      ]);

      showToast('Landing page data refreshed successfully', 'success');
    } catch (error) {
      console.error('Error refreshing landing page data:', error);
      showToast('Failed to refresh landing page data', 'error');
    } finally {
      setIsRefreshing(false);
    }
  };

  const {
    isOpen: isEditorOpen,
    openModal: openEditor,
    closeModal: closeEditor,
  } = useModal(false);
  const [isMobileView, setIsMobileView] = useState<boolean>(false);
  const [editableHtml, setEditableHtml] = useState<string>('');
  // Using global loading state instead of local state for persistence across tab navigation
  const editorIframeRef = useRef<HTMLIFrameElement>(null);
  const landingPage: LandingPage = state.projectDetails?.data?.landingPage || {
    html: '',
    public_url: '',
  };

  const setupIframeEvents = useCallback((iframe: HTMLIFrameElement) => {
    if (!iframe?.contentDocument || !iframe.contentWindow) return;

    const doc = iframe.contentDocument;
    const win = iframe.contentWindow;

    const handleClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const anchor = target.closest('a');
      if (anchor) {
        e.preventDefault();
        e.stopPropagation();
        const href = anchor.getAttribute('href');
        if (href) {
          if (href.startsWith('#')) {
            const targetId = href.replace('#', '');
            const targetElement = doc.getElementById(targetId);
            if (targetElement && win) {
              const rect = targetElement.getBoundingClientRect();
              const scrollTop = win.scrollY || doc.documentElement.scrollTop;
              const top = rect.top + scrollTop;
              win.scrollTo({ top, behavior: 'smooth' });
            }
          } else if (href.startsWith('/')) {
            const targetElement = doc.querySelector(href);
            if (targetElement && win) {
              const rect = targetElement.getBoundingClientRect();
              const scrollTop = win.scrollY || doc.documentElement.scrollTop;
              const top = rect.top + scrollTop;
              win.scrollTo({ top, behavior: 'smooth' });
            }
          } else {
            window.open(href, '_blank');
          }
        }
      }
    };

    doc.addEventListener('click', handleClick);

    return () => {
      doc.removeEventListener('click', handleClick);
    };
  }, []);

  const setupEditorIframe = useCallback(
    (iframe: HTMLIFrameElement) => {
      if (!iframe?.contentDocument || !iframe.contentWindow) return;

      const doc = iframe.contentDocument;
      doc.designMode = 'on';

      const editorStyle = doc.createElement('style');
      editorStyle.setAttribute('data-editor-styles', 'true');
      editorStyle.textContent = `
        * { cursor: text; }
        *:hover { outline: 1px dashed #4338ca33; }
        *:focus { outline: 2px solid #4338ca66; }
        html, body {
          height: 100%;
          margin: 0;
          padding: 0;
          padding-bottom: 100px;
          overflow-y: auto;
          overscroll-behavior: none;
          -webkit-overflow-scrolling: touch;
        }
      `;
      doc.head.appendChild(editorStyle);

      iframe.style.overflow = 'hidden';

      return setupIframeEvents(iframe);
    },
    [setupIframeEvents]
  );

  const handleEditClick = (): void => {
    const html =
      document.querySelector('iframe')?.contentDocument?.documentElement
        ?.outerHTML || '';
    setEditableHtml(html);
    openEditor();
  };

  const handleSaveChanges = (): void => {
    const mainIframe = document.querySelector('iframe');
    const editorIframe = editorIframeRef.current;
    if (mainIframe && editorIframe?.contentDocument) {
      const editorHtml = editorIframe.contentDocument.documentElement.cloneNode(
        true
      ) as HTMLElement;
      const editorStyles = editorHtml.querySelector(
        'style[data-editor-styles]'
      );
      editorStyles?.remove();
      mainIframe.srcdoc = editorHtml.outerHTML;
    }
    closeEditor();
  };

  const handleViewLiveClick = (): void => {
    if (landingPage?.public_url) {
      window.open(landingPage.public_url, '_blank');
    }
  };

  const handleGenerateLandingPage = async (): Promise<void> => {
    if (!projectId) {
      showToast('Project ID is missing', 'error');
      return;
    }

    // Set loading state in global state
    dispatch({
      type: ActionTypes.SET_LOADING_STATE,
      payload: {
        key: LOADING_STATE_LANDING_PAGE_GENERATION,
        value: true,
      },
    });

    try {
      const response = await apiService.post('/api/landing-page/create-page', {
        projectId,
      });

      if (response.success) {
        dispatch({
          type: ActionTypes.UPDATE_LANDING_PAGE_HTML,
          payload: response.data as string,
        });
        showToast('Landing page generated successfully', 'success');
      } else {
        showToast(
          response.message || 'Failed to generate landing page',
          'error'
        );
      }
    } catch (error) {
      console.error('Error generating landing page:', error);
      showToast('Failed to generate landing page', 'error');
    } finally {
      // Reset loading state in global state
      dispatch({
        type: ActionTypes.SET_LOADING_STATE,
        payload: {
          key: LOADING_STATE_LANDING_PAGE_GENERATION,
          value: false,
        },
      });
    }
  };
  const isContextGenerated =
    state?.projectDetails?.data?.projects?.context &&
    Object.keys(state?.projectDetails?.data?.projects?.context).length ===
      state?.projectDetails?.data?.projects?.chapters?.length;

  return (
    <>
      <header className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-4">
          {landingPage?.html && !isMobileViewPortEnabled && (
            <div className="flex items-center gap-2 mr-4 p-1 bg-gray-100 dark:bg-gray-800 rounded-lg">
              <button
                onClick={() => setIsMobileView(false)}
                className={`p-2 rounded ${!isMobileView ? 'bg-white dark:bg-gray-700 shadow-sm' : ''}`}
                title="Desktop view"
                type="button"
              >
                <FiMonitor className="w-5 h-5" />
              </button>
              <button
                onClick={() => setIsMobileView(true)}
                className={`p-2 rounded ${isMobileView ? 'bg-white dark:bg-gray-700 shadow-sm' : ''}`}
                title="Mobile view"
                type="button"
              >
                <FiSmartphone className="w-5 h-5" />
              </button>
            </div>
          )}
        </div>

        {isContextGenerated && (
          <div className="flex items-center gap-4">
            {!landingPage?.html && (
              <>
                <Button
                  onClick={handleRefresh}
                  variant="outline"
                  disabled={isRefreshing}
                >
                  <FiRefreshCcw
                    className={`${isRefreshing ? 'animate-spin' : ''}`}
                  />
                  {isRefreshing ? <ThreeDotLoader /> : ''}
                </Button>

                <Button
                  onClick={handleGenerateLandingPage}
                  variant="outline"
                  disabled={
                    state.loadingStates[LOADING_STATE_LANDING_PAGE_GENERATION]
                  }
                  ignoreSampleProject={true}
                >
                  {state.loadingStates[
                    LOADING_STATE_LANDING_PAGE_GENERATION
                  ] ? (
                    <ThreeDotLoader />
                  ) : (
                    <>
                      <FiPlus /> Create
                    </>
                  )}
                </Button>
              </>
            )}
            {landingPage?.html && (
              <>
                <Button
                  onClick={handleEditClick}
                  variant="outline"
                  className="inline-flex items-center justify-center rounded-md border border-primary px-6 py-2.5 text-center font-medium text-primary dark:text-white dark:border-white hover:bg-opacity-90"
                  ignoreSampleProject={true}
                >
                  Edit
                </Button>
                <Button
                  onClick={handleViewLiveClick}
                  variant="outline"
                  className="inline-flex items-center justify-center rounded-md border border-primary px-6 py-2.5 text-center font-medium text-primary dark:text-white dark:border-white hover:bg-opacity-90"
                  disabled={!landingPage?.public_url} // Disable if no public_url exists
                  ignoreSampleProject={true}
                >
                  View Live
                </Button>
              </>
            )}
          </div>
        )}
      </header>
      {isContextGenerated ? (
        <>
          {' '}
          {landingPage?.html ? (
            <section
              className={`mx-auto bg-gray-100 dark:bg-gray-800 rounded-lg shadow-xl transition-all duration-500 ease-in-out transform ${
                isMobileView
                  ? 'w-[375px] scale-95 border-[14px] border-gray-800 dark:border-gray-700 rounded-[40px] relative'
                  : 'w-full border border-stroke dark:border-strokedark'
              }`}
            >
              {' '}
              <div className="relative">
                {isMobileView && (
                  <>
                    {/* Power button */}
                    <div className="absolute -right-[2px] top-24 w-[4px] h-16 bg-gray-700 dark:bg-gray-600 rounded-l-md shadow-inner"></div>

                    {/* Volume buttons */}
                    <div className="absolute -left-[2px] top-20 w-[4px] h-12 bg-gray-700 dark:bg-gray-600 rounded-r-md shadow-inner"></div>
                    <div className="absolute -left-[2px] top-36 w-[4px] h-12 bg-gray-700 dark:bg-gray-600 rounded-r-md shadow-inner"></div>

                    {/* Top notch */}
                    <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-gray-800 dark:bg-gray-700 rounded-b-xl z-10 flex items-center justify-center">
                      <div className="w-20 h-4 bg-black rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 rounded-full bg-gray-500 mr-1"></div>
                        <div className="w-8 h-1 rounded-full bg-gray-600"></div>
                      </div>
                    </div>

                    {/* Bottom home indicator */}
                    <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-28 h-1 bg-gray-300 dark:bg-gray-400 rounded-full"></div>
                  </>
                )}
                <div
                  className={`${isMobileView ? 'h-6' : 'h-8 rounded-t-lg'} bg-gray-800 dark:bg-gray-700 flex items-center justify-between px-4 `}
                >
                  <div className="flex space-x-2">
                    {!isMobileView && (
                      <>
                        <div className="w-3 h-3 rounded-full bg-[#FF5F57]" />
                        <div className="w-3 h-3 rounded-full bg-[#FFBD2E]" />
                        <div className="w-3 h-3 rounded-full bg-[#28C840]" />
                      </>
                    )}
                  </div>
                  {isMobileView && (
                    <div className="w-full flex justify-center items-center">
                      <div className="flex space-x-1 items-center">
                        <div className="text-xs text-white font-medium">
                          12:34
                        </div>
                        <div className="w-1 h-1 rounded-full bg-white"></div>
                        <div className="flex space-x-0.5">
                          <div className="w-1 h-3 bg-white rounded-sm"></div>
                          <div className="w-1 h-2 bg-white rounded-sm"></div>
                          <div className="w-1 h-1 bg-white rounded-sm"></div>
                        </div>
                        <div className="ml-1">
                          <svg
                            className="w-3 h-3 text-white"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M17 4h-4V2h-2v2H7v18h10V4zm-6 16H9v-2h2v2zm0-4H9v-2h2v2zm0-4H9v-2h2v2zm4 8h-2v-2h2v2zm0-4h-2v-2h2v2zm0-4h-2v-2h2v2z"></path>
                          </svg>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div
                className={`flex-1 h-[calc(100vh-220px)] bg-white dark:bg-boxdark ${isMobileView ? 'overflow-auto rounded-b-[30px]' : 'overflow-hidden rounded-b-lg'} relative `}
              >
                <iframe
                  className="w-full h-full"
                  style={{
                    overflow: isMobileView ? 'auto' : 'hidden',
                    border: 'none',
                    display: 'block',
                    margin: 0,
                    padding: 0,
                  }}
                  onLoad={(e) => setupIframeEvents(e.currentTarget)}
                  srcDoc={landingPage?.html}
                />
              </div>
            </section>
          ) : (
            <Suspense fallback={<ThreeDotLoader />}>
              {state.loadingStates[LOADING_STATE_LANDING_PAGE_GENERATION] !==
              undefined ? (
                <div className="flex flex-col items-center justify-center h-full p-8 text-center">
                  <LandingPageLoader />
                </div>
              ) : (
                <div>
                  {' '}
                  Oops! We faced error in generating the landing page , pleaase
                  click on create button on top right to manually generate
                  it{' '}
                </div>
              )}
            </Suspense>
          )}
        </>
      ) : (
        <Suspense fallback={<LoadingSpinner />}>
          <CardWithAction
            title="Context Not Generated"
            description="It seems there is some issue with generating context with one or more section of the project. Click the button below to generate the full context."
            buttonText={
              state.loadingStates?.[
                ActionTypes.LOADING_STATE_CONTEXT_GENERATION
              ]
                ? 'Generating Context...'
                : BUTTON_GENERATE + ' Context'
            }
            buttonIcon={PlusIcon}
            onButtonClick={() => {
              if (projectId) {
                generateContext(projectId, dispatch, showToast);
              }
            }}
            className="min-h-0"
            icon={'🔍'}
            loading={
              !!state.loadingStates[
                ActionTypes.LOADING_STATE_CONTEXT_GENERATION
              ]
            }
          />
        </Suspense>
      )}

      <Modal
        isOpen={isEditorOpen}
        onClose={handleSaveChanges}
        isFullscreen={false}
        showCloseButton={true}
        title="Edit Landing Page"
      >
        <div className="flex flex-col h-[calc(100vh-130px)]">
          <div
            className="flex-1"
            style={{ overflow: 'hidden', position: 'relative' }}
          >
            <iframe
              ref={editorIframeRef}
              className="w-full h-full"
              style={{ overflow: 'hidden', border: 'none' }}
              srcDoc={editableHtml}
              onLoad={() => setupEditorIframe(editorIframeRef.current!)}
            />
          </div>
          <footer className="flex justify-end gap-4 p-4 border-t border-stroke dark:border-strokedark">
            <Button
              onClick={closeEditor}
              variant="outline"
              className="px-6 py-2.5"
            >
              Cancel
            </Button>
            <Button onClick={handleSaveChanges} className="px-6 py-2.5">
              Save Changes
            </Button>
          </footer>
        </div>
      </Modal>
    </>
  );
};

export default LandingPage;
