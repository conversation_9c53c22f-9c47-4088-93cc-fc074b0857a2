'use client';

import { useState } from 'react';
import { FiGlobe, FiServer, FiExternalLink } from 'react-icons/fi';
import ComponentCard from '@/components/common/ComponentCard';
import Button from '@/components/ui/button/Button';
import { useGlobalContext } from '@/context/GlobalContext';

export default function Page() {
  const [loadingInternal, setLoadingInternal] = useState(false);
  const [loadingCustom, setLoadingCustom] = useState(false);
  const { state } = useGlobalContext();

  const handleDeploy = async (type: 'internal' | 'custom') => {
    if (type === 'internal') {
      setLoadingInternal(true);
    } else {
      setLoadingCustom(true);
    }

    try {
      // Simulate deployment process
      setTimeout(() => {
        setLoadingInternal(false);
        setLoadingCustom(false);
      }, 2000);
    } catch (error) {
      console.error('Error deploying landing page:', error);
      setLoadingInternal(false);
      setLoadingCustom(false);
    }
  };
  const publicUrl = state.projectDetails?.data?.landingPage?.public_url;

  return (
    <div className="container mx-auto px-4 py-6 sm:p-8">
      <h1 className="text-lg sm:text-xl font-semibold text-gray-800 dark:text-white/90 mb-4 text-center sm:text-left">
        Your Landing Page
      </h1>

      {publicUrl ? (
        <div className="w-full max-w-2xl mx-auto">
          <ComponentCard
            title="Deployment Status"
            desc="Your landing page is currently deployed and accessible at the URL below."
          >
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between p-4 border rounded-lg bg-white dark:bg-gray-800 space-y-3 sm:space-y-0">
              <div>
                <p className="font-medium text-sm sm:text-base">
                  Deployed URL:
                </p>
                <a
                  href={publicUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-brand-600 hover:underline flex items-center gap-1 text-sm break-all"
                >
                  {publicUrl}
                  <FiExternalLink size={14} />
                </a>
              </div>
            </div>
          </ComponentCard>
        </div>
      ) : (
        <div className="w-full max-w-4xl mx-auto">
          <div className="grid grid-cols-1 gap-4">
            <div className="border border-gray-200 dark:border-gray-700 rounded-xl p-4 sm:p-6 bg-white dark:bg-gray-800/50 hover:shadow-md transition-all duration-300">
              <div className="flex items-center gap-3 sm:gap-4 mb-4">
                <div className="p-2 sm:p-3 bg-brand-50 dark:bg-brand-900/30 rounded-lg">
                  <FiServer className="text-brand-500 text-lg sm:text-xl" />
                </div>
                <div>
                  <h3 className="text-base sm:text-lg font-medium text-gray-900 dark:text-white">
                    Internal Domain
                  </h3>
                  <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                    Deploy to our internal domain
                  </p>
                </div>
              </div>
              <p className="text-gray-600 dark:text-gray-300 mb-4 text-xs sm:text-sm">
                Deploy your landing page to our internal domain for quick
                sharing and testing.
              </p>
              <Button
                variant="outline"
                onClick={() => handleDeploy('internal')}
                isLoading={loadingInternal}
                startIcon={<FiServer />}
                className="w-full sm:w-auto text-sm"
              >
                Deploy on internal domain
              </Button>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-xl p-4 sm:p-6 bg-white dark:bg-gray-800/50 hover:shadow-md transition-all duration-300">
              <div className="flex items-center gap-3 sm:gap-4 mb-4">
                <div className="p-2 sm:p-3 bg-brand-50 dark:bg-brand-900/30 rounded-lg">
                  <FiGlobe className="text-brand-500 text-lg sm:text-xl" />
                </div>
                <div>
                  <h3 className="text-base sm:text-lg font-medium text-gray-900 dark:text-white">
                    Custom Domain
                  </h3>
                  <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                    Deploy to your own domain
                  </p>
                </div>
              </div>
              <p className="text-gray-600 dark:text-gray-300 mb-4 text-xs sm:text-sm">
                Deploy your landing page to your own custom domain for a
                professional branded experience.
              </p>
              <small className="text-gray-500 mb-2 block text-xs">
                We are working on this feature and will be made available soon
              </small>
              <Button
                variant="outline"
                onClick={() => handleDeploy('custom')}
                isLoading={loadingCustom}
                startIcon={<FiGlobe />}
                disabled={true}
                className="w-full sm:w-auto text-sm"
              >
                Deploy on your own domain
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
