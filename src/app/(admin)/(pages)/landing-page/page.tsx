'use client';

import React from 'react';
import { FiEdit, FiEye, FiCode } from 'react-icons/fi';
import SectionPage from '@/components/ui/sectionPage/SectionPage';
import { SectionPageCardProps } from '@/types';

const url = new URLSearchParams(window.location.search).toString();
const LANDING_PAGE_CARDS: SectionPageCardProps[] = [
  {
    id: 'create',
    title: 'Create Your Landing Page',
    description:
      'Design a stunning landing page from scratch with our intuitive editor.',
    icon: FiEdit,
    path: `/landing-page/view?${url}`,
  },
  {
    id: 'view',
    title: 'Preview Your Landing Page',
    description:
      'See how your landing page looks and make adjustments in real-time.',
    icon: FiEye,
    path: `/landing-page/view?${url}`,
  },
  {
    id: 'code',
    title: 'Deploy ',
    description: 'Deploy your landing page in seconds',
    icon: FiCode,
    path: `/landing-page/deploy?${url}`,
  },
];

const LandingPage: React.FC = () => {
  return (
    <SectionPage
      title="Build a Website That Converts"
      subtitle="Landing Page Builder"
      description="Create, customize, and optimize stunning landing pages that captivate visitors and drive action. Design effortlessly, enhance user experience, and track performance—all in one place."
      cards={LANDING_PAGE_CARDS}
    />
  );
};

export default LandingPage;
