'use client';
import React, { useEffect, Suspense } from 'react';
import dynamic from 'next/dist/shared/lib/dynamic';
import { useSearchParams } from 'next/navigation';
import { useGlobalContext } from '@/context/GlobalContext';
import {
  LOADING_STATE_CONTEXT_GENERATION,
  SET_CHATBOX_OPEN,
} from '@/constants/actions/actions.constants';
import { generateContext } from '@/store/actions/contextActions';
import { formatChapterText } from '@/utils';
import { EditIcon, PlusIcon } from '@/icons';
import LoadingSpinner from '@/components/loading/LoadingSpinner';
import { useToast } from '@/context/ToastContext';
import Masonry from 'react-masonry-css';
import { ChapterData } from '@/types/global';
import {
  FiBriefcase,
  FiUsers,
  FiTrendingUp,
  FiDollarSign,
  FiTarget,
  FiPieChart,
  FiPlus,
} from 'react-icons/fi';
const SectionPage = dynamic(
  () => import('@/components/ui/sectionPage/SectionPage')
);
import { SectionPageCardProps } from '@/types';
import Button from '@/components/ui/button/Button';

import ContextGenerationLoader from '@/components/loading/ContextGenerationLoader';
const RenderValue = dynamic(() => import('@/components/context/renderValue'));
const CardWithAction = dynamic(
  () => import('@/components/ui/utils/CardWithAction')
);

const CHAPTER_ICONS = [
  FiBriefcase,
  FiTrendingUp,
  FiDollarSign,
  FiUsers,
  FiTarget,
  FiPieChart,
];
function ContextPage() {
  const searchParams = useSearchParams();
  const chapter = searchParams?.get('chapter');
  const projectId = searchParams?.get('id');
  const { showToast } = useToast();
  const { state, dispatch } = useGlobalContext();

  useEffect(() => {
    const contentContainer = document.querySelector('.custom-scrollbar');
    if (contentContainer)
      contentContainer.scrollTo({ top: -10, behavior: 'auto' });
    dispatch({ type: SET_CHATBOX_OPEN, payload: false });
  }, [chapter, dispatch]);
  useEffect(() => {
    return () => {
      dispatch({ type: SET_CHATBOX_OPEN, payload: false });
    };
  }, []);
  // Using global chat functionality from admin layout

  if (state.projectDetails?.loading) return <LoadingSpinner />;
  if (state.projectDetails?.error)
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center p-8 bg-white dark:bg-gray-800/90 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700">
          <span className="text-5xl mb-4 block">⚠️</span>
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-3">
            Error
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            {state.projectDetails?.error}
          </p>
        </div>
      </div>
    );

  const contextData = state.projectDetails?.data?.projects?.context || {};
  const chapterData = chapter ? contextData[chapter] : null;
  const chapters = state.projectDetails?.data?.projects?.chapters;
  if (chapter && chapters && !chapters.includes(chapter)) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center p-8 bg-white dark:bg-gray-800/90 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 max-w-md w-full">
          <span className="text-5xl mb-4 block">⚠️</span>
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-3">
            Invalid Chapter
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            The requested chapter could not be found. Please select a valid
            chapter from the sidebar.
          </p>
          <Button
            variant="outline"
            onClick={() => window.history.back()}
            className="inline-flex items-center justify-center gap-2"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M19 12H5M12 19l-7-7 7-7" />
            </svg>
            Go Back
          </Button>
        </div>
      </div>
    );
  }
  if (!chapterData && Object.keys(contextData).length !== chapters?.length)
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Suspense fallback={<LoadingSpinner />}>
          <CardWithAction
            title="Context Not Generated"
            description="It seems there is some issue with generating context with one or more section of the project. Click the button below to generate the full context."
            buttonIcon={PlusIcon}
            buttonText={
              state.loadingStates[LOADING_STATE_CONTEXT_GENERATION]
                ? 'Generating Context...'
                : 'Generate Context'
            }
            onButtonClick={() =>
              projectId && generateContext(projectId, dispatch, showToast)
            }
            className="min-h-0"
            icon={'🔍'}
            loading={!!state.loadingStates[LOADING_STATE_CONTEXT_GENERATION]}
          />{' '}
        </Suspense>
      </div>
    );

  const handleAddChapter = () => {
    dispatch({
      type: SET_CHATBOX_OPEN,
      payload: !state.chatboxMessages.isOpen,
    });
    // Set initial message for adding a new chapter
    if (!state.chatboxMessages.isOpen) {
      dispatch({
        type: 'SET_CHATBOX_MESSAGES',
        payload: [
          {
            text: 'Would you like to add more chapters to the idea?',
            isBot: true,
            timestamp: new Date().toISOString(),
          },
        ],
      });
    }
  };
  function AddNewCard() {
    return (
      <div
        onClick={handleAddChapter}
        className="block h-40 w-full group rounded-xl border-2 border-dashed border-gray-300 bg-white p-7 shadow-sm hover:shadow-lg transition-all duration-300 dark:border-gray-600 dark:bg-gray-900/50 relative transform hover:-translate-y-1 flex flex-col items-center justify-center  gap-3 group h-full rounded-xl border border-gray-200 bg-white p-7 shadow-sm hover:shadow-xl transition-all duration-300 dark:border-gray-800 dark:bg-gray-900/50 relative transform hover:-translate-y-1 hover:border-brand-300 dark:hover:border-brand-700 overflow-hidden"
      >
        <FiPlus className="w-12 h-12 text-gray-400 group-hover:text-brand-500 transition-colors duration-300" />
        <p className="text-gray-600 dark:text-gray-300 font-lg">
          Add New Chapter
        </p>
      </div>
    );
  }

  const CONTEXT_CARDS: SectionPageCardProps[] = [
    {
      id: 'add-chapter',
      title: 'Add New Chapter',
      isActionCard: true,
      actionCardComponent: AddNewCard,
    },
    ...(chapters?.map((chapter, index) => ({
      id: chapter,
      title: formatChapterText(chapter),
      description:
        contextData?.[chapter]?.['chapter']?.['answer'] &&
        typeof contextData?.[chapter]?.['chapter']?.['answer'] === 'string'
          ? contextData?.[chapter]?.['chapter']?.['answer']
          : Array.isArray(contextData?.[chapter]?.['chapter']?.['answer'])
            ? contextData?.[chapter]?.['chapter']?.['answer']?.[0]
            : '',
      icon: CHAPTER_ICONS[index % CHAPTER_ICONS.length],
      path: `/context?chapter=${chapter}&id=${projectId}`,
    })) || []),
  ];

  if (!chapter)
    if (chapters && chapters?.length > 0)
      return (
        <>
          <Suspense fallback={<LoadingSpinner />}>
            <SectionPage
              title="Manage Your Business Context"
              subtitle="Business Context"
              description="Organize and manage all your business context in one place. Add new chapters, edit existing ones, and keep your business information up-to-date."
              cards={CONTEXT_CARDS}
            />
          </Suspense>
        </>
      );
    else return null;

  const chapterName =
    contextData?.[chapter]?.['chapter']?.['answer'] &&
    typeof contextData?.[chapter]?.['chapter']?.['answer'] === 'string'
      ? contextData?.[chapter]?.['chapter']?.['answer']
      : Array.isArray(contextData?.[chapter]?.['chapter']?.['answer'])
        ? contextData?.[chapter]?.['chapter']?.['answer']?.[0]
        : '';
  if (!chapterData)
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center p-8 bg-white dark:bg-gray-800/90 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700">
          <span className="text-5xl mb-4 block">🔍</span>
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-3">
            Chapter Not Found
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            The requested chapter does not exist.
          </p>
        </div>
      </div>
    );

  const breakpointColumnsObj = { default: 3, 1100: 2, 700: 1 };

  return (
    <>
      <div className="min-h-screen  from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="overflow custom-scrollbar mb-10">
            <div className="grid grid-cols-1 gap-6">
              <div className="backdrop-blur-sm dark:bg-gray-800/90 rounded-2xl dark:border-gray-700 p-8">
                <div className="flex flex-col sm:flex-row sm:flex-wrap justify-between items-start sm:items-center mb-6 gap-4">
                  <h3 className="text-2xl font-bold min-w-0 flex-shrink-0 w-full sm:w-auto sm:max-w-[calc(100%-14rem)]">
                    {chapterName}
                  </h3>
                  <div className="flex justify-end flex-shrink-0 w-full sm:w-auto">
                    <Button
                      onClick={() => {
                        dispatch({
                          type: SET_CHATBOX_OPEN,
                          payload: !state.chatboxMessages.isOpen,
                        });
                        // Set initial message for the current chapter
                        if (!state.chatboxMessages.isOpen) {
                          dispatch({
                            type: 'SET_CHATBOX_MESSAGES',
                            payload: [
                              {
                                text: `What do you want to add about ${formatChapterText(chapter || '') || 'Main Business Context'}?`,
                                isBot: true,
                                timestamp: new Date().toISOString(),
                              },
                            ],
                          });
                        }
                      }}
                      variant="outline"
                      className="w-full sm:w-40 flex items-center gap-2 border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 px-4 py-2 rounded-md"
                    >
                      Chat <EditIcon />
                    </Button>
                  </div>
                </div>

                <Masonry
                  breakpointCols={breakpointColumnsObj}
                  className="flex -ml-8"
                  columnClassName="pl-8"
                >
                  {Object.entries(chapterData as unknown as ChapterData).map(
                    ([sectionKey, sectionData], index) => {
                      if (sectionKey === 'chapter') return null;
                      return (
                        <div
                          key={sectionKey}
                          className={`rounded-xl bg-transparent shadow-sm hover:shadow-md transition-all duration-200 mb-8 h-fit flex flex-col overflow-hidden border`}
                        >
                          <div className="p-4 sm:p-6">
                            <div className="mb-4">
                              {CHAPTER_ICONS[index % CHAPTER_ICONS.length] &&
                                React.createElement(
                                  CHAPTER_ICONS[index % CHAPTER_ICONS.length],
                                  {
                                    className:
                                      'w-6 h-6 text-brand-500 dark:text-brand-400',
                                  }
                                )}
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                              {sectionData?.title}
                            </h3>
                            <div className="text-gray-500 dark:text-gray-400 prose prose-sm max-w-none">
                              <Suspense fallback={<LoadingSpinner />}>
                                <RenderValue value={sectionData?.answer} />
                              </Suspense>
                            </div>
                          </div>
                        </div>
                      );
                    }
                  )}
                </Masonry>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default function ContextPageWrapper() {
  return (
    <ContextGenerationLoader>
      <ContextPage />
    </ContextGenerationLoader>
  );
}
