import React from 'react';
import { render, screen } from '@testing-library/react';
import ChatLayout from '../layout';

describe('ChatLayout Component', () => {
  it('renders children correctly', () => {
    // Arrange
    const testId = 'test-child';
    const childComponent = <div data-testid={testId}>Test Child Content</div>;

    // Act
    render(<ChatLayout>{childComponent}</ChatLayout>);

    // Assert
    expect(screen.getByTestId(testId)).toBeInTheDocument();
    expect(screen.getByTestId(testId)).toHaveTextContent('Test Child Content');
  });

  it('applies correct CSS classes for layout', () => {
    // Arrange
    const childComponent = <div>Test Child</div>;

    // Act
    const { container } = render(<ChatLayout>{childComponent}</ChatLayout>);

    // Assert
    const outerDiv = container.firstChild;
    expect(outerDiv).toHaveClass('flex', 'h-screen');

    const innerDiv = outerDiv?.firstChild;
    expect(innerDiv).toHaveClass(
      'relative',
      'flex',
      'flex-1',
      'flex-col',
      'overflow-y-auto',
      'overflow-x-hidden'
    );
  });

  it('renders main element with proper structure', () => {
    // Arrange
    const childComponent = <div>Test Child</div>;

    // Act
    const { container } = render(<ChatLayout>{childComponent}</ChatLayout>);

    // Assert
    const mainElement = container.querySelector('main');
    expect(mainElement).toBeInTheDocument();

    const contentDiv = mainElement?.firstChild;
    expect(contentDiv).toHaveClass(
      'mx-auto',
      'max-w-screen-2xl',
      'overflow-y-auto'
    );
  });
});
