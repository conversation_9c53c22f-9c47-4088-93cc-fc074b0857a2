'use client';

import React, { JSX, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useGlobalContext } from '@/context/GlobalContext';
import { withAuth } from '@/components/auth/ProtectedRoute';
import {
  FiBarChart2,
  FiCalendar,
  FiMessageSquare,
  FiTarget,
  FiUsers,
  FiGlobe,
  FiTrendingUp,
  FiEdit3,
  FiClock,
  FiChevronRight,
  FiTwitter,
  FiLinkedin,
  FiFacebook,
  FiCheck,
} from 'react-icons/fi';
import Button from '@/components/ui/button/Button';
import Link from 'next/link';
import { FaWhatsapp } from 'react-icons/fa';
import {
  LOADING_STATE_CONTEXT_GENERATION,
  LOADING_STATE_TWITTER_POST_GENERATION,
  LOADING_STATE_INSTAGRAM_POST_GENERATION,
  LOADING_STATE_LINKEDIN_POST_GENERATION,
  LOADING_STATE_FACEBOOK_POST_GENERATION,
  LOADING_STATE_WHATSAPP_POST_GENERATION,
  LOADING_STATE_LANDING_PAGE_GENERATION,
  LOADING_STATE_CONTENT_CALENDAR_GENERATION,
  LOADING_STATE_SOCIAL_MEDIA_GENERATION,
} from '@/constants';
import dynamic from 'next/dynamic';
const SampleProjectBanner = dynamic(
  () => import('@/components/ui/banner/SampleProjectBanner')
);

function ProjectHomePage() {
  const searchParams = useSearchParams();
  const projectId = searchParams.get('id');
  const { state } = useGlobalContext();
  const [activeTab, setActiveTab] = useState('overview');

  // Use project data from global state
  const projectData = state.projectDetails?.data;
  const businessMeta = projectData?.projectMetaData;
  const loading = state.projectDetails.loading || false;
  const isSampleProject = state?.projectDetails?.data?.isSampleProject || false;
  // Define the featured actions and all actions
  const renderMainContent = () => {
    if (loading) {
      return (
        <div className="min-h-screen bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="flex justify-center items-center h-64">
              <div className="animate-pulse flex space-x-4">
                <div className="rounded-full bg-gray-200 h-12 w-12"></div>
                <div className="flex-1 space-y-4 py-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    if (!businessMeta && !projectData) {
      return (
        <div className="min-h-screen bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="bg-white shadow rounded-lg p-6">
              <h1 className="text-2xl font-bold text-gray-800 mb-4">
                Project Not Found
              </h1>
              <p className="text-gray-600 mb-6">
                The project you're looking for could not be found.
              </p>
              <Link href="/">
                <Button variant="primary">Return to Dashboard</Button>
              </Link>
            </div>
          </div>
        </div>
      );
    }

    const featuredActions = [
      {
        title: 'Business Context',
        description: 'Track performance metrics and insights',
        icon: <FiBarChart2 className="w-6 h-6 text-brand-500" />,
        link: `/context?id=${projectId}`,
        color: 'bg-blue-50',
        loadingStateKey: LOADING_STATE_CONTEXT_GENERATION,
      },
      {
        title: 'Content Calendar',
        description: 'Schedule and manage your content',
        icon: <FiCalendar className="w-6 h-6 text-brand-500" />,
        link: `/socials/campaigns?id=${projectId}`,
        color: 'bg-green-50',
        loadingStateKey: LOADING_STATE_CONTENT_CALENDAR_GENERATION,
      },
      {
        title: 'Social Media',
        description: 'Create and schedule posts across platforms',
        icon: <FiGlobe className="w-6 h-6 text-brand-500" />,
        link: `/socials?id=${projectId}`,
        color: 'bg-purple-50',
        loadingStateKey: LOADING_STATE_SOCIAL_MEDIA_GENERATION,
      },
      {
        title: 'Landing Page builder',
        description: 'Design Landing page for your site',
        icon: <FiTarget className="w-6 h-6 text-brand-500" />,
        link: `/landing-page?id=${projectId}`,
        color: 'bg-orange-50',
        loadingStateKey: LOADING_STATE_LANDING_PAGE_GENERATION,
      },
    ];

    const allActions = [
      ...featuredActions,
      {
        title: 'Audience Insights',
        description: 'Understand your target audience',
        icon: <FiUsers className="w-6 h-6 text-brand-500" />,
        link: `/context?id=${projectId}&chapter=target_users`,
        color: 'bg-purple-50',
        loadingStateKey: LOADING_STATE_CONTEXT_GENERATION,
      },
      {
        title: 'Business Model',
        description: 'Actionable plans to grow your business',
        icon: <FiTrendingUp className="w-6 h-6 text-brand-500" />,
        link: `/context?id=${projectId}&chapter=business_model`,
        color: 'bg-yellow-50',
        loadingStateKey: LOADING_STATE_CONTEXT_GENERATION,
      },
      {
        title: 'Content Creation on Instagram',
        description: 'Generate engaging content for your audience',
        icon: <FiEdit3 className="w-6 h-6 text-brand-500" />,
        link: `/socials/instagram?id=${projectId}`,
        color: 'bg-teal-50',
        loadingStateKey: LOADING_STATE_INSTAGRAM_POST_GENERATION,
      },
      {
        title: 'Content Creation on Twitter',
        description: 'Generate engaging content for your audience',
        icon: <FiTwitter className="w-6 h-6 text-brand-500" />,
        link: `/socials/twitter?id=${projectId}`,
        color: 'bg-teal-50',
        loadingStateKey: LOADING_STATE_TWITTER_POST_GENERATION,
      },
      {
        title: 'Content Creation on LinkedIn',
        description: 'Generate engaging content for your audience',
        icon: <FiLinkedin className="w-6 h-6 text-brand-500" />,
        link: `/socials/linkedin?id=${projectId}`,
        color: 'bg-pink-50',
        loadingStateKey: LOADING_STATE_LINKEDIN_POST_GENERATION,
      },
      {
        title: 'Content Creation on Facebook',
        description: 'Generate engaging content for your audience',
        icon: <FiFacebook className="w-6 h-6 text-brand-500" />,
        link: `/socials/facebook?id=${projectId}`,
        color: 'bg-green-50',
        loadingStateKey: LOADING_STATE_FACEBOOK_POST_GENERATION,
      },
      {
        title: 'Content Creation on WhatsApp',
        description: 'Generate engaging content for your audience',
        icon: <FaWhatsapp className="w-6 h-6 text-brand-500" />,
        link: `/socials/whatsapp?id=${projectId}`,
        color: 'bg-red-50',
        loadingStateKey: LOADING_STATE_WHATSAPP_POST_GENERATION,
      },
    ];
    function renderFeatureCard(
      action: {
        title: string;
        description: string;
        icon: JSX.Element;
        link: string;
        color: string;
        loadingStateKey: string;
      },
      index: number
    ) {
      return (
        <Link href={action.link} key={index}>
          <div
            className={`${action.color} rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 h-full flex flex-col relative`}
          >
            <div className="absolute top-4 right-4">
              {
                state.loadingStates[action.loadingStateKey] === true ? (
                  <div className="w-5 h-5 border-2 border-brand-300 border-t-brand-500 rounded-full animate-spin"></div>
                ) : state.loadingStates[action.loadingStateKey] === false ? (
                  <FiCheck className="w-6 h-6 text-green-500" />
                ) : null /* Show nothing for error or undefined state */
              }
            </div>

            <div className="mb-4">{action.icon}</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {action.title}
            </h3>
            <p className="text-gray-500 text-sm mb-4 flex-grow">
              {action.description}
            </p>
            <div className="flex items-center text-brand-600 text-sm font-medium">
              Get started
              <FiChevronRight className="ml-1 h-4 w-4" />
            </div>
          </div>
        </Link>
      );
    }
    return (
      <div className="min-h-screen w-full overflow-y-auto bg-gray-50 pb-20">
        {/* Project Header */}

        <div className="bg-white max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4 sm:pt-6">
          {isSampleProject && <SampleProjectBanner />}
        </div>
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div className="w-full">
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900 break-words">
                  {businessMeta?.title ||
                    projectData?.name ||
                    'Untitled Project'}
                </h1>
                <p className="text-gray-500 mt-1 text-sm sm:text-base">
                  {businessMeta?.description ||
                    projectData?.description ||
                    'No description available'}
                </p>
                <div className="flex flex-wrap items-center gap-2 mt-2">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      businessMeta?.status === 'Active' ||
                      businessMeta?.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {businessMeta?.status || 'Status unknown'}
                  </span>
                  {businessMeta?.tags && businessMeta.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {businessMeta.tags
                        .slice(0, 3)
                        .map((tag: string, index: number) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-brand-100 text-brand-800"
                          >
                            {tag}
                          </span>
                        ))}
                      {businessMeta.tags.length > 3 && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          +{businessMeta.tags.length - 3} more
                        </span>
                      )}
                    </div>
                  )}
                  <div className="flex items-center text-sm text-gray-500">
                    <FiClock className="mr-1.5 h-4 w-4 text-gray-400" />
                    {businessMeta?.updated_at
                      ? new Date(businessMeta.updated_at).toLocaleDateString()
                      : 'Recently updated'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8 overflow-visible">
          {/* Tabs */}
          <div className="border-b border-gray-200 mb-4 sm:mb-8">
            <nav
              className="-mb-px flex space-x-4 sm:space-x-8 overflow-x-auto"
              aria-label="Tabs"
            >
              <button
                onClick={() => setActiveTab('overview')}
                className={`${
                  activeTab === 'overview'
                    ? 'border-brand-500 text-brand-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Overview
              </button>
              <button
                onClick={() => setActiveTab('all')}
                className={`${
                  activeTab === 'all'
                    ? 'border-brand-500 text-brand-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                All Tools
              </button>
            </nav>
          </div>

          {activeTab === 'overview' && (
            <>
              {/* Featured Actions */}
              <div className="mb-6 sm:mb-10">
                <h2 className="text-lg font-medium text-gray-900 mb-4 sm:mb-6">
                  Featured Tools
                </h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                  {featuredActions.map((action, index) =>
                    renderFeatureCard(action, index)
                  )}
                </div>
              </div>

              {/* Project Summary */}
              <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6 mb-6 sm:mb-10">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Project Summary
                </h2>
                <div className="prose max-w-none">
                  <p className="text-gray-600">
                    {businessMeta?.description ||
                      projectData?.description ||
                      `This project was created to help you build and grow your business. 
                    Use the tools above to manage your marketing, track analytics, and create content.`}
                  </p>

                  {businessMeta?.business_idea_config?.biz_type && (
                    <div className="mt-4">
                      <h3 className="text-sm font-medium text-gray-500">
                        Business Type
                      </h3>
                      <p className="text-gray-900">
                        {businessMeta.business_idea_config.biz_type}
                      </p>
                    </div>
                  )}

                  {businessMeta?.location && (
                    <div className="mt-4">
                      <h3 className="text-sm font-medium text-gray-500">
                        Location
                      </h3>
                      <p className="text-gray-900">{businessMeta.location}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Recent Activity */}
              <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Recent Activity
                </h2>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-brand-100 flex items-center justify-center">
                        <FiMessageSquare className="h-4 w-4 text-brand-600" />
                      </div>
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-900">
                        Project Created
                      </p>
                      <p className="text-sm text-gray-500">
                        {businessMeta?.created_at
                          ? new Date(businessMeta.created_at).toLocaleString()
                          : 'Recently'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <FiEdit3 className="h-4 w-4 text-blue-600" />
                      </div>
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-900">
                        Last Updated
                      </p>
                      <p className="text-sm text-gray-500">
                        {businessMeta?.updated_at
                          ? new Date(businessMeta.updated_at).toLocaleString()
                          : 'Recently'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          {activeTab === 'all' && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
              {allActions.map((action, index) =>
                renderFeatureCard(action, index)
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  return <>{renderMainContent()}</>;
}

export default withAuth(ProjectHomePage);
