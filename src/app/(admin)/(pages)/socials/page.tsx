'use client';

import React from 'react';
import {
  FiCode,
  FiFacebook,
  FiInstagram,
  FiLinkedin,
  FiTwitter,
} from 'react-icons/fi';
import { FaWhatsapp } from 'react-icons/fa';
import SectionPage from '@/components/ui/sectionPage/SectionPage';
import { SectionPageCardProps } from '@/types';

const url = new URLSearchParams(window.location.search).toString();

// Constants for reusability and maintainability
const SOCIAL_MEDIA_CARDS: SectionPageCardProps[] = [
  {
    id: 'Campaigns',
    title: 'Create Campaigns ',
    description: 'Create campaigns for your social media accounts!',
    icon: FiCode,
    path: `/socials/campaigns?${url}`,
  },
  {
    id: 'instagram',
    title: 'Be seen on Instagram!',
    description:
      'Connect your account to schedule, analyze, and optimize your posts.',
    icon: FiInstagram,
    path: `/socials/instagram?${url}`,
  },
  {
    id: 'twitter',
    title: 'Amplify Your Voice on Twitter!',
    description:
      'Schedule tweets, monitor performance, and engage with your followers.',
    icon: FiTwitter,
    path: `/socials/twitter?${url}`,
  },
  {
    id: 'linkedin',
    title: 'Connect on LinkedIn!',
    description: 'Increase your visibility with professionals on LinkedIn!',
    icon: FiLinkedin,
    path: `/socials/linkedin?${url}`,
  },
  {
    id: 'facebook',
    title: 'Connect on Facebook!',
    description: 'Increase your visibility with professionals on Facebook!',
    icon: FiFacebook,
    path: `/socials/facebook?${url}`,
  },
  {
    id: 'whatsapp',
    title: 'Connect on WhatsApp!',
    description: 'Increase your visibility with professionals on WhatsApp!',
    icon: FaWhatsapp,
    path: `/socials/whatsapp?${url}`,
  },
];

export default function SocialMediaLandingPage() {
  return (
    <SectionPage
      title="Supercharge Your Social Media Presence"
      subtitle="Social Media Management"
      description="Effortlessly manage, schedule, and analyze your social media content—all from one place. Connect your accounts, engage with your audience, and grow your brand with ease."
      cards={SOCIAL_MEDIA_CARDS}
    />
  );
}
