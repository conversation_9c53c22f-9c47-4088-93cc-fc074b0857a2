'use client';

import React, { useState, useMemo, Suspense, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { useParams, useSearchParams } from 'next/navigation';
import { useGlobalContext } from '@/context/GlobalContext';
import { apiService } from '@/services/api';
import { fetchProjectDetails } from '@/store/actions/projectActions';
import { generateContext } from '@/store/actions/contextActions';
import { updateSocialMediaPosts } from '@/store/actions/globalActions';
import { useToast } from '@/context/ToastContext';
import { PlusIcon } from '@/icons';
import PostLoadingSkeleton from '@/components/socialMedia/components/PostLoadingSkeleton';

// Types
import { RedirectUrlResponse } from '@/types/api';

// Constants
import {
  API_ENDPOINT_GENERATE_SOCIAL_POSTS,
  API_ENDPOINT_GET_REDIRECT_URL,
  BUTTON_GENERATE,
  LOADING_STATE_CONTEXT_GENERATION,
} from '@/constants';

// Components
import ThreeDotLoader from '@/components/loading/ThreeDotLoader';
import LoadingSpinner from '@/components/loading/LoadingSpinner';

// Lazy loaded components
const CardWithAction = dynamic(
  () => import('@/components/ui/utils/CardWithAction')
);
const PlatformCard = dynamic(
  () => import('@/components/socialMedia/components/PlatformCard')
);
const ConnectedCard = dynamic(
  () => import('@/components/socialMedia/components/ConnectedCard')
);
const PlatformPosts = dynamic(
  () => import('@/components/socialMedia/components/PlatformPosts')
);
const PlatformActions = dynamic(
  () => import('@/components/socialMedia/components/PlatformActions')
);

// Utils
import { initialPlatforms } from '@/components/socialMedia/components/platformList';
import {
  getPlatformLoadingStateKey,
  getPlatformPosts,
} from '@/components/socialMedia/utils/platformUtils';
// Main Component
export default function SocialsPage() {
  const params = useParams();
  const { state, dispatch } = useGlobalContext();
  const searchParams = useSearchParams();
  const tenant = params?.tenant as string;
  const chatId = searchParams?.get('id');
  const { showToast } = useToast();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Scroll to top whenever tenant changes
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }, [tenant]);

  // Get the loading state key for the current tenant
  const loadingStateKey = getPlatformLoadingStateKey(tenant);

  // Get the current platform
  const currentPlatform = useMemo(() => {
    return initialPlatforms.find((p) => p.id === tenant) || initialPlatforms[0];
  }, [tenant]);

  // Get connected account data based on tenant
  const connectedAccount = useMemo(() => {
    const accounts = state?.projectDetails?.data?.connectedAccounts;
    if (!accounts) return null;

    // Safely access the account based on tenant
    switch (tenant) {
      case 'twitter':
        return accounts.twitter;
      case 'instagram':
        return accounts.instagram;
      case 'linkedin':
        return accounts.linkedin;
      case 'facebook':
        return accounts.facebook;
      case 'whatsapp':
        return accounts.whatsapp;
      default:
        return null;
    }
  }, [state?.projectDetails?.data?.connectedAccounts, tenant]);

  const handleConnect = async (platformId: string) => {
    try {
      if (!chatId) {
        showToast('Project ID is missing', 'error');
        return;
      }

      const { data } = await apiService.get<RedirectUrlResponse>(
        `${API_ENDPOINT_GET_REDIRECT_URL}?platform=${platformId}&chatId=${chatId}`
      );

      if (data?.url) window.location.href = data.url;
    } catch (error) {
      console.error('Error connecting to platform:', error);
      showToast('Failed to connect to platform', 'error');
    }
  };

  const handleRefresh = async () => {
    if (!chatId) {
      showToast('Project ID is missing', 'error');
      return;
    }

    setIsRefreshing(true);

    try {
      // Refresh project data
      await fetchProjectDetails(chatId, dispatch, showToast);
      showToast('Social media posts refreshed successfully', 'success');
    } catch (error) {
      console.error('Error refreshing posts:', error);
      showToast('Failed to refresh posts', 'error');
    } finally {
      setIsRefreshing(false);
    }
  };

  const generatePosts = async () => {
    if (!chatId) {
      showToast('Project ID is missing', 'error');
      return;
    }

    // Set the appropriate loading state
    dispatch({
      type: 'SET_LOADING_STATE',
      payload: {
        key: loadingStateKey,
        value: true,
      },
    });

    try {
      const response = await apiService.post<{
        success: boolean;
        message: string;
        data: {
          posts: {
            instagramPost?: any[];
            twitterPost?: any[];
            linkedinPost?: any[];
            facebookPost?: any[];
            whatsappPost?: any[];
          };
        };
      }>(`${API_ENDPOINT_GENERATE_SOCIAL_POSTS}`, {
        tenant,
        chatId,
      });

      if (response.data?.success && response.data?.data?.posts) {
        // Ensure all required properties exist with default empty arrays
        const posts = {
          instagramPost: response.data.data.posts.instagramPost || [],
          twitterPost: response.data.data.posts.twitterPost || [],
          linkedinPost: response.data.data.posts.linkedinPost || [],
          facebookPost: response.data.data.posts.facebookPost || [],
          whatsappPost: response.data.data.posts.whatsappPost || [],
        };

        dispatch(updateSocialMediaPosts(posts));
        showToast('Posts generated successfully', 'success');
      } else {
        showToast(response?.message || 'Failed to generate posts', 'error');
      }
    } catch (error) {
      console.error('Error generating posts:', error);
      showToast('Failed to generate posts', 'error');
    } finally {
      // Reset the loading state
      dispatch({
        type: 'SET_LOADING_STATE',
        payload: {
          key: loadingStateKey,
          value: false,
        },
      });
    }
  };

  // Check if context is generated
  const isContextGenerated =
    state?.projectDetails?.data?.projects?.context &&
    Object.keys(state?.projectDetails?.data?.projects?.context).length ===
      state?.projectDetails?.data?.projects?.chapters?.length;
  return (
    <>
      {isContextGenerated ? (
        <div>
          <div className="mb-8 grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-2">
            <Suspense fallback={<ThreeDotLoader />}>
              {!connectedAccount?.user_id ? (
                <PlatformCard
                  platform={currentPlatform}
                  onConnect={handleConnect}
                />
              ) : (
                <ConnectedCard
                  platform={currentPlatform}
                  getConnectedAccount={connectedAccount}
                />
              )}
            </Suspense>
          </div>

          <div className="mb-8">
            <Suspense fallback={<ThreeDotLoader />}>
              <PlatformActions
                platform={currentPlatform}
                isRefreshing={isRefreshing}
                isGenerating={state.loadingStates[loadingStateKey] || false}
                onRefresh={handleRefresh}
                onGenerate={generatePosts}
              />
            </Suspense>

            <Suspense
              fallback={
                <div className="mt-4">
                  <PostLoadingSkeleton />
                </div>
              }
            >
              <PlatformPosts
                platform={currentPlatform}
                posts={getPlatformPosts(state?.projectDetails, tenant)}
                connectedAccount={connectedAccount}
              />
            </Suspense>
          </div>
        </div>
      ) : (
        <Suspense fallback={<LoadingSpinner />}>
          <CardWithAction
            title="Context Not Generated"
            description="It seems there is some issue with generating context with one or more section of the project. Click the button below to generate the full context."
            buttonText={
              state.loadingStates?.[LOADING_STATE_CONTEXT_GENERATION]
                ? 'Generating Context...'
                : BUTTON_GENERATE + ' Context'
            }
            buttonIcon={PlusIcon}
            onButtonClick={() => {
              if (chatId) {
                generateContext(chatId, dispatch, showToast);
              }
            }}
            className="min-h-0"
            icon={'🔍'}
            loading={!!state.loadingStates[LOADING_STATE_CONTEXT_GENERATION]}
          />
        </Suspense>
      )}
    </>
  );
}
