import React from 'react';

export default function ConnectLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex overflow-y-auto">
      <div className="relative flex flex-1 flex-col  overflow-x-hidden">
        <main>
          <div className="mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10 mb-20">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
