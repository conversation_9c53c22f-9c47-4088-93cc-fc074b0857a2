import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { useRouter, useSearchParams, useParams } from 'next/navigation';
import SocialCallback from '../page';
import { apiService } from '@/services/api';
import { useGlobalContext } from '@/context/GlobalContext';

// Mock Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
  useParams: jest.fn(),
}));

// Mock API service
jest.mock('@/services/api', () => ({
  apiService: {
    post: jest.fn(),
  },
}));

// Mock context hooksplatform
jest.mock('@/context/GlobalContext', () => ({
  useGlobalContext: jest.fn(),
}));

// Mock window.location.href to prevent navigation errors
const originalLocation = window.location;
Object.defineProperty(window, 'location', {
  writable: true,
  value: { href: '' },
});

describe('SocialCallback Component', () => {
  // Restore original window.location after tests
  afterAll(() => {
    Object.defineProperty(window, 'location', {
      writable: true,
      value: originalLocation,
    });
  });

  // Mock data
  const mockRouter = {
    push: jest.fn(),
  };
  const mockSearchParams = {
    get: jest.fn(),
  };
  const mockParams = { tenant: 'twitter' };
  const mockDispatch = jest.fn();

  // Mock successful API response
  const mockSuccessResponse = {
    data: {
      success: true,
      message: 'Authentication successful',
      data: {
        user_id: 'user123',
        name: 'Test User',
        username: 'testuser',
        profile_picture_url: 'https://example.com/profile.jpg',
      },
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mocks
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
    (useParams as jest.Mock).mockReturnValue(mockParams);
    (useGlobalContext as jest.Mock).mockReturnValue({ dispatch: mockDispatch });

    // Default search params
    mockSearchParams.get.mockImplementation((param) => {
      if (param === 'code') return 'auth_code_123';
      if (param === 'state') return 'chat_id_456';
      return null;
    });

    // Default API response
    (apiService.post as jest.Mock).mockResolvedValue(mockSuccessResponse);
  });

  it('renders loading spinner during authentication', () => {
    // Act
    render(<SocialCallback />);

    // Assert
    const spinner = screen.getByTestId('loading-spinner');
    expect(spinner).toBeInTheDocument();
    expect(spinner).toHaveClass('animate-spin');
  });

  it('redirects to tenant page if code is missing', async () => {
    // Arrange
    const originalConsoleError = console.error;
    console.error = jest.fn();

    mockSearchParams.get.mockImplementation((param) => {
      if (param === 'code') return null;
      if (param === 'state') return 'chat_id_456';
      return null;
    });

    // Act
    render(<SocialCallback />);

    // Assert
    await waitFor(() => {
      expect(console.error).toHaveBeenCalledWith(
        'No authentication code found'
      );
      expect(mockRouter.push).toHaveBeenCalledWith('/socials/twitter');
    });

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('redirects to tenant page if state is missing', async () => {
    // Arrange
    const originalConsoleError = console.error;
    console.error = jest.fn();

    mockSearchParams.get.mockImplementation((param) => {
      if (param === 'code') return 'auth_code_123';
      if (param === 'state') return null;
      return null;
    });

    // Act
    render(<SocialCallback />);

    // Assert
    await waitFor(() => {
      expect(console.error).toHaveBeenCalledWith('No state parameter found');
      expect(mockRouter.push).toHaveBeenCalledWith('/socials/twitter');
    });

    // Restore console.error
    console.error = originalConsoleError;
  });

  it('makes API call with correct parameters', async () => {
    // Act
    render(<SocialCallback />);

    // Assert
    await waitFor(() => {
      expect(apiService.post).toHaveBeenCalledWith(expect.any(String), {
        code: 'auth_code_123',
        tenant: 'twitter',
        state: 'chat_id_456',
      });
    });
  });

  it('updates Twitter account in global state on successful authentication', async () => {
    // Act
    render(<SocialCallback />);

    // Assert
    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith({
        type: 'UPDATE_TWITTER_ACCOUNT',
        payload: {
          id: 'twitter',
          user_id: 'user123',
          name: 'Test User',
          username: 'testuser',
          profile_picture_url: 'https://example.com/profile.jpg',
        },
      });
    });
  });

  it('updates Instagram account in global state when tenant is instagram', async () => {
    // Arrange
    (useParams as jest.Mock).mockReturnValue({ tenant: 'instagram' });

    // Act
    render(<SocialCallback />);

    // Assert
    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith({
        type: 'UPDATE_INSTAGRAM_ACCOUNT',
        payload: {
          id: 'instagram',
          user_id: 'user123',
          name: 'Test User',
          username: 'testuser',
          profile_picture_url: 'https://example.com/profile.jpg',
        },
      });
    });
  });

  it('redirects to tenant page with chat ID after successful authentication', async () => {
    // Act
    render(<SocialCallback />);

    // Assert
    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith(
        '/socials/twitter?id=chat_id_456'
      );
    });
  });

  it('handles API errors gracefully', async () => {
    // Arrange
    const originalConsoleError = console.error;
    console.error = jest.fn();
    (apiService.post as jest.Mock).mockRejectedValue(new Error('API Error'));

    // Act
    render(<SocialCallback />);

    // Assert
    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith(
        '/socials/twitter?id=chat_id_456'
      );
      expect(console.error).toHaveBeenCalled();
    });

    // Cleanup
    console.error = originalConsoleError;
  });
});
