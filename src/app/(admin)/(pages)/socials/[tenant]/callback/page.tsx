'use client';

import { Suspense, useEffect } from 'react';
import { useRouter, useSearchParams, useParams } from 'next/navigation';
import { apiService } from '@/services/api';
import { useGlobalContext } from '@/context/GlobalContext';
import { API_ENDPOINT_SOCIAL_AUTH } from '@/constants';
import {
  updateTwitterAccount,
  updateInstagramAccount,
  updateLinkedInAccount,
  updateFacebookAccount,
} from '@/store/actions/globalActions';

interface SocialAuthResponse {
  success: boolean;
  message: string;
  data: {
    user_id: string;
    name: string;
    username: string;
    profile_picture_url: string;
  };
}

function SocialCallback() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = useParams();
  const { dispatch } = useGlobalContext();

  useEffect(() => {
    const handleCallback = async () => {
      const code = searchParams?.get('code');
      const state = searchParams?.get('state');
      const tenant = params?.tenant as string;
      if (!code) {
        console.error('No authentication code found');
        router.push(`/socials/${tenant}`);
        return;
      }

      if (!state) {
        console.error('No state parameter found');
        router.push(`/socials/${tenant}`);
        return;
      }

      try {
        // Make API call to backend with the code
        const { data } = await apiService.post<SocialAuthResponse>(
          `${API_ENDPOINT_SOCIAL_AUTH}`,
          {
            code,
            tenant,
            state,
          }
        );
        // Store the connected account in the global state based on tenant
        if (data && data.success && data.data) {
          const accountData = {
            id: tenant,
            user_id: data.data.user_id,
            name: data.data.name,
            username: data.data.username,
            profile_picture_url: data.data.profile_picture_url,
          };
          if (tenant === 'twitter') {
            dispatch(updateTwitterAccount(accountData));
          } else if (tenant === 'instagram') {
            dispatch(updateInstagramAccount(accountData));
          } else if (tenant === 'linkedin') {
            dispatch(updateLinkedInAccount(accountData));
          } else if (tenant === 'facebook') {
            dispatch(updateFacebookAccount(accountData));
          }
        }

        // Redirect to the social platform page with the state as ID
        router.push(`/socials/${tenant}?id=${state}`);
      } catch (error) {
        console.log(error);
        console.error('Authentication error:', error);
        router.push(`/socials/${tenant}?id=${state}`);
      }
    };

    handleCallback();
  }, [router, searchParams, params, dispatch]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div
        data-testid="loading-spinner"
        className="w-16 h-16 border-4 border-brand-500 border-t-transparent rounded-full animate-spin"
      ></div>
    </div>
  );
}
export default function Callback() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SocialCallback />
    </Suspense>
  );
}
