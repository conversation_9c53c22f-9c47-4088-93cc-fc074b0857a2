import {
  mockSocialPlatforms,
  mockConnectedAccounts,
  mockSocialMediaPosts,
  mockApiResponses,
  createMockGlobalState,
} from './test-utils';

describe('Social Media Test Utilities', () => {
  it('provides valid mock social platforms data', () => {
    expect(mockSocialPlatforms).toBeDefined();
    expect(mockSocialPlatforms.length).toBe(2);
    expect(mockSocialPlatforms[0].id).toBe('instagram');
    expect(mockSocialPlatforms[1].id).toBe('twitter');
  });

  it('provides valid mock connected accounts data', () => {
    expect(mockConnectedAccounts).toBeDefined();
    expect(mockConnectedAccounts.twitter).toBeDefined();
    expect(mockConnectedAccounts.instagram).toBeDefined();
    expect(mockConnectedAccounts.twitter.username).toBe('twitteruser');
    expect(mockConnectedAccounts.instagram.username).toBe('instagramuser');
  });

  it('provides valid mock social media posts', () => {
    expect(mockSocialMediaPosts).toBeDefined();
    expect(mockSocialMediaPosts.twitterPost).toBeInstanceOf(Array);
    expect(mockSocialMediaPosts.instagramPost).toBeInstanceOf(Array);
    expect(mockSocialMediaPosts.twitterPost.length).toBeGreaterThan(0);
    expect(mockSocialMediaPosts.instagramPost.length).toBeGreaterThan(0);
  });

  it('provides valid mock API responses', () => {
    expect(mockApiResponses).toBeDefined();
    expect(mockApiResponses.redirectUrl).toBeDefined();
    expect(mockApiResponses.socialAuth).toBeDefined();
    expect(mockApiResponses.generatePosts).toBeDefined();
  });

  it('creates a valid mock global state', () => {
    const mockState = createMockGlobalState();
    expect(mockState).toBeDefined();
    expect(mockState.projectDetails).toBeDefined();
    expect(mockState.loadingStates).toBeDefined();
  });

  it('allows overriding mock global state properties', () => {
    const mockState = createMockGlobalState({
      loadingStates: {
        twitterPostGeneration: true,
        instaPostGeneration: true,
        contextGeneration: false,
      },
    });

    expect(mockState.loadingStates.twitterPostGeneration).toBe(true);
    expect(mockState.loadingStates.instaPostGeneration).toBe(true);
    expect(mockState.loadingStates.contextGeneration).toBe(false);
  });
});
