import { SocialPlatform, MockPost } from '@/types/api';

/**
 * Test utilities for social media components testing
 */

// Mock social platforms data
export const mockSocialPlatforms: SocialPlatform[] = [
  {
    id: 'instagram',
    name: 'Instagram',
    icon: '/images/social/instagram.svg',
    description: 'Share your photos and stories with your followers.',
    isConnected: false,
    isLoading: false,
  },
  {
    id: 'twitter',
    name: 'Twitter',
    icon: '/images/social/twitter.svg',
    description: 'Engage with your audience in real-time.',
    isConnected: false,
    isLoading: false,
  },
];

// Mock connected account data
export const mockConnectedAccounts = {
  twitter: {
    user_id: 'twitter_user_123',
    name: 'Twitter User',
    username: 'twitteruser',
    profile_picture_url: 'https://example.com/twitter-profile.jpg',
    id: 'twitter',
  },
  instagram: {
    user_id: 'instagram_user_456',
    name: 'Instagram User',
    username: 'instagramuser',
    profile_picture_url: 'https://example.com/instagram-profile.jpg',
    id: 'instagram',
  },
};

// Mock social media posts
export const mockSocialMediaPosts = {
  twitterPost: [
    {
      id: 'tweet1',
      _id: 'tweet1',
      content_text: 'This is a test tweet #1',
      likes: 10,
      comments: 5,
      retweets: 3,
      shares: 2,
      timestamp: '2025-03-15T12:00:00Z',
      username: 'twitteruser',
      userAvatar: 'https://example.com/twitter-profile.jpg',
      handle: '@twitteruser',
      post_status: true,
    },
    {
      id: 'tweet2',
      _id: 'tweet2',
      content_text: 'This is a test tweet #2 with hashtags #dolze #marketing',
      likes: 15,
      comments: 8,
      retweets: 5,
      shares: 3,
      timestamp: '2025-03-15T13:00:00Z',
      username: 'twitteruser',
      userAvatar: 'https://example.com/twitter-profile.jpg',
      handle: '@twitteruser',
      post_status: true,
    },
  ] as MockPost[],
  instagramPost: [
    {
      id: 'insta1',
      _id: 'insta1',
      content_text: 'This is a test Instagram post #1',
      media_url: 'https://example.com/image1.jpg',
      likes: 45,
      comments: 12,
      timestamp: '2025-03-15T12:00:00Z',
      username: 'instagramuser',
      userAvatar: 'https://example.com/instagram-profile.jpg',
      location: 'New York',
      post_status: true,
    },
    {
      id: 'insta2',
      _id: 'insta2',
      content_text: 'This is a test Instagram post #2 #dolze #marketing',
      media_url: 'https://example.com/image2.jpg',
      likes: 78,
      comments: 23,
      timestamp: '2025-03-15T13:00:00Z',
      username: 'instagramuser',
      userAvatar: 'https://example.com/instagram-profile.jpg',
      location: 'San Francisco',
      post_status: true,
    },
  ] as MockPost[],
};

// Define the type for the global state
interface GlobalState {
  projectDetails: {
    data: {
      connectedAccounts: {
        twitter: {
          user_id: string;
          name: string;
          username: string;
          profile_picture_url: string;
          id: string;
        } | null;
        instagram: {
          user_id: string;
          name: string;
          username: string;
          profile_picture_url: string;
          id: string;
        } | null;
      };
      socialMediaPosts: {
        twitterPost: MockPost[];
        instagramPost: MockPost[];
      };
      projects: {
        context: Record<string, unknown>;
        chapters: Array<{ id: string }>;
      };
    };
  };
  loadingStates: {
    twitterPostGeneration: boolean;
    instaPostGeneration: boolean;
    contextGeneration: boolean;
    [key: string]: boolean;
  };
}

// Define the type for overrides
type StateOverrides = Partial<GlobalState>;

// Mock global state for testing
export const createMockGlobalState = (
  overrides: StateOverrides = {}
): GlobalState => {
  return {
    projectDetails: {
      data: {
        connectedAccounts: {
          twitter: null,
          instagram: null,
        },
        socialMediaPosts: {
          twitterPost: [],
          instagramPost: [],
        },
        projects: {
          context: { chapter1: {}, chapter2: {} },
          chapters: [{ id: 'chapter1' }, { id: 'chapter2' }],
        },
      },
    },
    loadingStates: {
      twitterPostGeneration: false,
      instaPostGeneration: false,
      contextGeneration: false,
    },
    ...overrides,
  } as GlobalState;
};

// Define types for API responses
interface RedirectUrlResponse {
  data: {
    url: string;
  };
}

interface SocialAuthResponse {
  data: {
    success: boolean;
    message: string;
    data: {
      user_id: string;
      name: string;
      username: string;
      profile_picture_url: string;
    };
  };
}

interface GeneratePostsResponse {
  data: {
    success: boolean;
    message: string;
    data: {
      posts: {
        twitterPost: MockPost[];
        instagramPost: MockPost[];
      };
    };
  };
}

// Mock API responses
export const mockApiResponses = {
  redirectUrl: {
    data: {
      url: 'https://example.com/auth',
    },
  } as RedirectUrlResponse,
  socialAuth: {
    data: {
      success: true,
      message: 'Authentication successful',
      data: {
        user_id: 'user123',
        name: 'Test User',
        username: 'testuser',
        profile_picture_url: 'https://example.com/profile.jpg',
      },
    },
  } as SocialAuthResponse,
  generatePosts: {
    data: {
      success: true,
      message: 'Posts generated successfully',
      data: {
        posts: {
          twitterPost: [
            {
              id: 'tweet1',
              _id: 'tweet1',
              content_text: 'Test tweet',
              likes: 0,
              comments: 0,
              timestamp: '2025-03-15T12:00:00Z',
              post_status: false,
            },
          ],
          instagramPost: [
            {
              id: 'insta1',
              _id: 'insta1',
              content_text: 'Test post',
              likes: 0,
              comments: 0,
              timestamp: '2025-03-15T12:00:00Z',
              post_status: false,
            },
          ],
        },
      },
    },
  } as GeneratePostsResponse,
};
