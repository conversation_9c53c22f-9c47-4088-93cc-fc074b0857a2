'use client';

import React from 'react';


const dummyData = {
  followers: {
    total: 8234,
    growth: 156,
    growthPercentage: 1.8,
  },
  engagement: {
    rate: 3.2,
    likes: 23456,
    retweets: 1234,
    replies: 789,
    impressions: 45678,
  },
  tweets: {
    total: 892,
    impressionRate: 25.3,
    topPerforming: [
      {
        id: 1,
        type: 'text',
        likes: 567,
        retweets: 123,
        replies: 45,
        impressions: 3456,
      },
      {
        id: 2,
        type: 'image',
        likes: 789,
        retweets: 234,
        replies: 67,
        impressions: 5678,
      },
      {
        id: 3,
        type: 'video',
        likes: 1234,
        retweets: 345,
        replies: 89,
        impressions: 7890,
      },
    ],
  },
  audience: {
    demographics: {
      age: [
        { group: '13-17', percentage: 3 },
        { group: '18-24', percentage: 28 },
        { group: '25-34', percentage: 38 },
        { group: '35-44', percentage: 18 },
        { group: '45+', percentage: 13 },
      ],
      gender: {
        male: 48,
        female: 49,
        other: 3,
      },
      topLocations: [
        { country: 'United States', percentage: 38 },
        { country: 'United Kingdom', percentage: 14 },
        { country: 'India', percentage: 12 },
        { country: 'Canada', percentage: 8 },
        { country: 'Japan', percentage: 6 },
      ],
    },
  },
};

export default function TwitterAnalytics() {
  return (
    <>
      <h1 className="text-xl font-semibold text-gray-800 dark:text-white/90 mb-4">
        Twitter Analytics
      </h1>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-4">
        {/* Follower Stats */}
        <div className="rounded-lg border border-stroke/50 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 p-4 shadow-lg hover:shadow-xl transition-all duration-300">
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-primary/10 transform transition-transform hover:scale-110 duration-300 dark:bg-primary/20">
            <svg
              className="h-6 w-6 text-primary dark:text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
          </div>

          <div className="mt-4">
            <h4 className="text-2xl font-bold text-black dark:text-white">
              {dummyData.followers.total.toLocaleString()}
            </h4>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Followers
              </span>
              <span className="flex items-center gap-1 text-sm font-medium text-meta-3">
                +{dummyData.followers.growthPercentage}%
              </span>
            </div>
          </div>
        </div>

        {/* Engagement Stats */}
        <div className="rounded-lg border border-stroke/50 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 p-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-primary/10 transform transition-transform hover:scale-110 duration-300 dark:bg-primary/20">
            <svg
              className="h-6 w-6 text-primary dark:text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"
              />
            </svg>
          </div>

          <div className="mt-4">
            <h4 className="text-2xl font-bold text-black dark:text-white">
              {dummyData.engagement.rate}%
            </h4>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Engagement Rate
              </span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {dummyData.engagement.likes.toLocaleString()} likes
              </span>
            </div>
          </div>
        </div>

        {/* Tweet Performance */}
        <div className="rounded-lg border border-stroke/50 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 p-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-primary/10 transform transition-transform hover:scale-110 duration-300 dark:bg-primary/20">
            <svg
              className="h-6 w-6 text-primary dark:text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>

          <div className="mt-4">
            <h4 className="text-2xl font-bold text-black dark:text-white">
              {dummyData.tweets.total}
            </h4>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Total Tweets
              </span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {dummyData.tweets.impressionRate}% reach
              </span>
            </div>
          </div>
        </div>

        {/* Audience Demographics */}
        <div className="rounded-lg border border-stroke/50 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 p-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-primary/10 transform transition-transform hover:scale-110 duration-300 dark:bg-primary/20">
            <svg
              className="h-6 w-6 text-primary dark:text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
              />
            </svg>
          </div>

          <div className="mt-4">
            <h4 className="text-2xl font-bold text-black dark:text-white">
              {dummyData.audience.demographics.gender.male}%
            </h4>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Male Audience
              </span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {dummyData.audience.demographics.gender.female}% female
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Top Performing Tweets */}
      <div className="mt-6">
        <div className="rounded-lg border border-stroke/50 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 p-4 shadow-lg hover:shadow-xl transition-all duration-300">
          <h4 className="mb-6 text-lg font-semibold text-black dark:text-white">
            Top Performing Tweets
          </h4>
          <div className="space-y-4">
            {dummyData.tweets.topPerforming.map((tweet) => (
              <div
                key={tweet.id}
                className="flex items-center justify-between border-b border-stroke/50 pb-4 last:border-0 dark:border-strokedark hover:bg-gray-50 dark:hover:bg-gray-800 p-3 rounded-lg transition-all duration-300"
              >
                <div>
                  <h5 className="text-sm font-medium text-black dark:text-white">
                    Top{' '}
                    {tweet.type.charAt(0).toUpperCase() + tweet.type.slice(1)}{' '}
                    Tweet
                  </h5>
                  <span className="text-xs text-gray-600 dark:text-gray-400">
                    {tweet.likes.toLocaleString()} likes
                  </span>
                </div>
                <span className="text-sm font-medium text-meta-3">
                  {tweet.impressions.toLocaleString()} impressions
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Age Demographics */}
    </>
  );
}
