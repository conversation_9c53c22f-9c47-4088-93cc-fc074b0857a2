'use client';

import React from 'react';
import { FiInstagram, FiTwitter, FiCode } from 'react-icons/fi';
import SectionPage from '@/components/ui/sectionPage/SectionPage';
import { SectionPageCardProps } from '@/types';

const url = new URLSearchParams(window.location.search).toString();

const ANALYTICS_CARDS: SectionPageCardProps[] = [
  {
    id: 'overview',
    title: 'Instagram Overview',
    description: 'Get a comprehensive view of your Instagram campaigns.',
    icon: FiInstagram,
    path: `/analytics/instagram?${url}`,
  },
  {
    id: 'revenue',
    title: 'Twitter Overview',
    description: 'Get a comprehensive view of your Twitter campaigns.',
    icon: FiTwitter,
    path: `/analytics/twitter?${url}`,
  },
  {
    id: 'users',
    title: 'Landing Page Analytics',
    description: 'Get insights into your landing page performance.',
    icon: FiCode,
    path: `/analytics/landing-page?${url}`,
  },
];

export default function Page() {
  return (
    <SectionPage
      title="Gain Insights into Your Business"
      subtitle="Analytics Dashboard"
      description="Track, analyze, and optimize your business performance with our comprehensive analytics tools. Get real-time insights and make data-driven decisions."
      cards={ANALYTICS_CARDS}
    />
  );
}
