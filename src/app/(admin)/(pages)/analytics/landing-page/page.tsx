'use client';

import React from 'react';

const dummyData = {
  pageViews: {
    total: 25678,
    growth: 1234,
    growthPercentage: 4.8,
  },
  engagement: {
    averageTime: '2:45',
    bounceRate: 35.2,
    pagesPerSession: 2.8,
  },
  conversions: {
    rate: 3.2,
    total: 821,
    topSources: [
      { source: 'Direct', conversions: 312, rate: 3.8 },
      { source: 'Organic Search', conversions: 245, rate: 2.9 },
      { source: 'Social Media', conversions: 164, rate: 2.4 },
    ],
  },
  userBehavior: {
    newUsers: 65,
    returningUsers: 35,
    deviceDistribution: [
      { device: 'Mobile', percentage: 58 },
      { device: 'Desktop', percentage: 35 },
      { device: 'Tablet', percentage: 7 },
    ],
    topPages: [
      { path: '/', views: 12345, avgTime: '1:30' },
      { path: '/features', views: 8765, avgTime: '2:15' },
      { path: '/pricing', views: 6543, avgTime: '3:00' },
    ],
  },
};

export default function LandingPageAnalytics() {
  return (
    <>
      <h1 className="text-xl font-semibold text-gray-800 dark:text-white/90 mb-4">
        Landing Page Analytics
      </h1>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-4">
        {/* Page Views Stats */}
        <div className="rounded-lg border border-stroke/50 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 p-4 shadow-lg hover:shadow-xl transition-all duration-300">
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-primary/10 transform transition-transform hover:scale-110 duration-300 dark:bg-primary/20">
            <svg
              className="h-6 w-6 text-primary dark:text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
              />
            </svg>
          </div>

          <div className="mt-4">
            <h4 className="text-2xl font-bold text-black dark:text-white">
              {dummyData.pageViews.total.toLocaleString()}
            </h4>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Page Views
              </span>
              <span className="flex items-center gap-1 text-sm font-medium text-meta-3">
                +{dummyData.pageViews.growthPercentage}%
              </span>
            </div>
          </div>
        </div>

        {/* Engagement Stats */}
        <div className="rounded-lg border border-stroke/50 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 p-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-primary/10 transform transition-transform hover:scale-110 duration-300 dark:bg-primary/20">
            <svg
              className="h-6 w-6 text-primary dark:text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
          </div>

          <div className="mt-4">
            <h4 className="text-2xl font-bold text-black dark:text-white">
              {dummyData.engagement.bounceRate}%
            </h4>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Bounce Rate
              </span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {dummyData.engagement.averageTime} avg. time
              </span>
            </div>
          </div>
        </div>

        {/* Conversion Stats */}
        <div className="rounded-lg border border-stroke/50 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 p-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-primary/10 transform transition-transform hover:scale-110 duration-300 dark:bg-primary/20">
            <svg
              className="h-6 w-6 text-primary dark:text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
              />
            </svg>
          </div>

          <div className="mt-4">
            <h4 className="text-2xl font-bold text-black dark:text-white">
              {dummyData.conversions.rate}%
            </h4>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Conversion Rate
              </span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {dummyData.conversions.total} total
              </span>
            </div>
          </div>
        </div>

        {/* User Behavior */}
        <div className="rounded-lg border border-stroke/50 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 p-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-primary/10 transform transition-transform hover:scale-110 duration-300 dark:bg-primary/20">
            <svg
              className="h-6 w-6 text-primary dark:text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
              />
            </svg>
          </div>

          <div className="mt-4">
            <h4 className="text-2xl font-bold text-black dark:text-white">
              {dummyData.userBehavior.newUsers}%
            </h4>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                New Users
              </span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {100 - dummyData.userBehavior.newUsers}% returning
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Top Pages - Simplified */}
      <div className="mt-6">
        <div className="rounded-lg border border-stroke/50 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 p-4 shadow-lg hover:shadow-xl transition-all duration-300">
          <h4 className="mb-6 text-lg font-semibold text-black dark:text-white">
            Most Visited Pages
          </h4>
          <div className="space-y-4">
            {dummyData.userBehavior.topPages.slice(0, 3).map((page, index) => (
              <div
                key={index}
                className="flex items-center justify-between border-b border-stroke/50 pb-4 last:border-0 dark:border-strokedark hover:bg-gray-50 dark:hover:bg-gray-800 p-3 rounded-lg transition-all duration-300"
              >
                <div>
                  <h5 className="text-sm font-medium text-black dark:text-white">
                    {page.path}
                  </h5>
                  <span className="text-xs text-gray-600 dark:text-gray-400">
                    {page.avgTime} avg. time
                  </span>
                </div>
                <span className="text-sm font-medium text-meta-3">
                  {page.views.toLocaleString()} views
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Device Distribution */}
      <div className="mt-4">
        <div className="rounded-lg border border-stroke/50 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 p-4 shadow-lg hover:shadow-xl transition-all duration-300">
          <h4 className="text-title-sm font-medium text-black dark:text-white mb-4">
            Device Distribution
          </h4>
          <div className="flex justify-between gap-4">
            {dummyData.userBehavior.deviceDistribution.map((device) => (
              <div key={device.device} className="flex-1">
                <div className="h-[100px] bg-gray-200 dark:bg-gray-700 relative">
                  <div
                    className="absolute bottom-0 w-full bg-primary"
                    style={{ height: `${device.percentage}%` }}
                  ></div>
                </div>
                <div className="text-center mt-2">
                  <span className="text-sm font-medium">{device.device}</span>
                  <span className="text-xs block">{device.percentage}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
