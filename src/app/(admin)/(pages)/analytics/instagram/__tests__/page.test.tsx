import React from 'react';
import { render, screen } from '@testing-library/react';
import InstagramAnalytics from '../page';

describe('InstagramAnalytics Component', () => {
  it('renders the page title correctly', () => {
    render(<InstagramAnalytics />);
    expect(screen.getByText('Instagram Analytics')).toBeInTheDocument();
  });

  it('renders engagement rate card correctly', () => {
    render(<InstagramAnalytics />);
    expect(screen.getByText('4.2%')).toBeInTheDocument();
    expect(screen.getByText('Engagement Rate')).toBeInTheDocument();
    expect(screen.getByText('45,123 likes')).toBeInTheDocument();
  });

  it('renders followers card correctly', () => {
    render(<InstagramAnalytics />);
    expect(screen.getByText('15,234')).toBeInTheDocument();
    expect(screen.getByText('Followers')).toBeInTheDocument();
    expect(screen.getByText('+2.1%')).toBeInTheDocument();
  });

  it('renders posts card correctly', () => {
    render(<InstagramAnalytics />);
    expect(screen.getByText('142')).toBeInTheDocument();
    expect(screen.getByText('Total Posts')).toBeInTheDocument();
    expect(screen.getByText('28.5% reach')).toBeInTheDocument();
  });

  it('renders top performing posts section', () => {
    render(<InstagramAnalytics />);
    expect(screen.getByText('Top Performing Posts')).toBeInTheDocument();
  });

  it('renders audience demographics section', () => {
    render(<InstagramAnalytics />);
    expect(screen.getByText('Female Audience')).toBeInTheDocument();
    expect(screen.getByText('45% male')).toBeInTheDocument();
  });

  it('renders gender statistics correctly', () => {
    render(<InstagramAnalytics />);
    expect(screen.getByText('52%')).toBeInTheDocument();
    expect(screen.getByText('45% male')).toBeInTheDocument();
  });
});
