'use client';

import React from 'react';

const dummyData = {
  followers: {
    total: 15234,
    growth: 324,
    growthPercentage: 2.1,
  },
  engagement: {
    rate: 4.2,
    likes: 45123,
    comments: 2341,
    shares: 1234,
  },
  posts: {
    total: 142,
    reachRate: 28.5,
    topPerforming: [
      { id: 1, type: 'image', likes: 1234, comments: 89, reach: 5678 },
      { id: 2, type: 'carousel', likes: 987, comments: 76, reach: 4532 },
      { id: 3, type: 'video', likes: 2345, comments: 123, reach: 8901 },
    ],
  },
  audience: {
    demographics: {
      age: [
        { group: '13-17', percentage: 5 },
        { group: '18-24', percentage: 25 },
        { group: '25-34', percentage: 35 },
        { group: '35-44', percentage: 20 },
        { group: '45+', percentage: 15 },
      ],
      gender: {
        male: 45,
        female: 52,
        other: 3,
      },
      topLocations: [
        { country: 'United States', percentage: 35 },
        { country: 'United Kingdom', percentage: 15 },
        { country: 'Canada', percentage: 12 },
        { country: 'Australia', percentage: 8 },
        { country: 'Germany', percentage: 6 },
      ],
    },
  },
};

export default function InstagramAnalytics() {
  return (
    <div className="h-screen overflow-y-auto">
      <h1 className="text-xl font-semibold text-gray-800 dark:text-white/90 mb-4">
        Instagram Analytics
      </h1>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-4">
        {/* Follower Stats */}
        <div className="rounded-lg border border-stroke/50 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 p-4 shadow-lg hover:shadow-xl transition-all duration-300">
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-primary/10 transform transition-transform hover:scale-110 duration-300 dark:bg-primary/20">
            <svg
              className="h-6 w-6 text-primary dark:text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
          </div>

          <div className="mt-4">
            <h4 className="text-2xl font-bold text-black dark:text-white">
              {dummyData.followers.total.toLocaleString()}
            </h4>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Followers
              </span>
              <span className="flex items-center gap-1 text-sm font-medium text-meta-3">
                +{dummyData.followers.growthPercentage}%
              </span>
            </div>
          </div>
        </div>

        {/* Engagement Stats */}
        <div className="rounded-lg border border-stroke/50 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 p-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-primary/10 transform transition-transform hover:scale-110 duration-300 dark:bg-primary/20">
            <svg
              className="h-6 w-6 text-primary dark:text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"
              />
            </svg>
          </div>

          <div className="mt-4">
            <h4 className="text-2xl font-bold text-black dark:text-white">
              {dummyData.engagement.rate}%
            </h4>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Engagement Rate
              </span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {dummyData.engagement.likes.toLocaleString()} likes
              </span>
            </div>
          </div>
        </div>

        {/* Post Performance */}
        <div className="rounded-lg border border-stroke/50 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 p-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-primary/10 transform transition-transform hover:scale-110 duration-300 dark:bg-primary/20">
            <svg
              className="h-6 w-6 text-primary dark:text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>

          <div className="mt-4">
            <h4 className="text-2xl font-bold text-black dark:text-white">
              {dummyData.posts.total}
            </h4>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Total Posts
              </span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {dummyData.posts.reachRate}% reach
              </span>
            </div>
          </div>
        </div>

        {/* Audience Demographics */}
        <div className="rounded-lg border border-stroke/50 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 p-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-primary/10 transform transition-transform hover:scale-110 duration-300 dark:bg-primary/20">
            <svg
              className="h-6 w-6 text-primary dark:text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
          </div>

          <div className="mt-4">
            <h4 className="text-2xl font-bold text-black dark:text-white">
              {dummyData.audience.demographics.gender.female}%
            </h4>
            <div className="mt-2 flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Female Audience
              </span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {dummyData.audience.demographics.gender.male}% male
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Top Performing Posts */}
      <div className="mt-6">
        <div className="rounded-lg border border-stroke/50 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 p-4 shadow-lg hover:shadow-xl transition-all duration-300">
          <h4 className="mb-6 text-lg font-semibold text-black dark:text-white">
            Top Performing Posts
          </h4>
          <div className="space-y-4">
            {dummyData.posts.topPerforming.slice(0, 3).map((post) => (
              <div
                key={post.id}
                className="flex items-center justify-between border-b border-stroke/50 pb-4 last:border-0 dark:border-strokedark hover:bg-gray-50 dark:hover:bg-gray-800 p-3 rounded-lg transition-all duration-300"
              >
                <div>
                  <h5 className="text-sm font-medium text-black dark:text-white">
                    {post.type.charAt(0).toUpperCase() + post.type.slice(1)}{' '}
                    Post
                  </h5>
                  <span className="text-xs text-gray-600 dark:text-gray-400">
                    {post.likes.toLocaleString()} likes
                  </span>
                </div>
                <span className="text-sm font-medium text-meta-3">
                  {post.reach.toLocaleString()} reach
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Age Demographics */}
    </div>
  );
}
