'use client';

import { useSidebar } from '@/context/SidebarContext';
import { useGlobalContext } from '@/context/GlobalContext';
import AppHeader from '@/layout/AppHeader';
import AppSidebar from '@/layout/AppSidebar';
import Backdrop from '@/layout/Backdrop';
import React, { useEffect, useState, useRef } from 'react';
import { withAuth } from '@/components/auth/ProtectedRoute';
import { useSearchParams } from 'next/navigation';
import { useToast } from '@/context/ToastContext';
import ChatBox from '@/components/ui/chatbox/ChatBox';
// Import the extracted project action functions
import {
  fetchProjectDetails,
  fetchSecondFoldProjectDetails,
} from '@/store/actions/projectActions';
import { apiService } from '@/services/api';
import * as ActionTypes from '@/constants/actions/actions.constants';
import LoadingSpinner from '@/components/loading/LoadingSpinner';
import { listenForLandingPageUpdate } from '@/services/realtimeServices/getWebsiteData';
import {
  IN_PROGRESS_STATUS,
  LOADING_STATE_CONTENT_CALENDAR_GENERATION,
  LOADING_STATE_CONTEXT_GENERATION,
  LOADING_STATE_LANDING_PAGE_GENERATION,
  LOADING_STATE_SOCIAL_MEDIA_GENERATION,
} from '@/constants';
import {
  listenForCampaigns,
  listenForSocialMediaUpdate,
} from '@/services/realtimeServices/getSocialMediaData';
import { listenForContextGeneration } from '@/services/realtimeServices/getContextData';

function AdminLayout({ children }: { children: React.ReactNode }) {
  const { isExpanded, isHovered, isMobileOpen } = useSidebar();
  const { dispatch } = useGlobalContext();
  const [isLoading, setIsLoading] = useState(true);
  const router = useSearchParams();
  const id = router?.get('id') || router?.get('state');

  const { state } = useGlobalContext();
  const { showToast } = useToast();

  // Track the completion status of API calls
  const [firstApiCompleted, setFirstApiCompleted] = useState(false);
  const [secondApiCompleted, setSecondApiCompleted] = useState(false);

  useEffect(() => {
    // If both APIs have completed, set loading to false
    if (firstApiCompleted && secondApiCompleted) {
      setIsLoading(false);
    }
  }, [firstApiCompleted, secondApiCompleted]);

  useEffect(() => {
    const loadProjectDetails = async () => {
      // Check if project details data is already present and matches the current ID
      if (state.projectDetails.data && state.projectDetails.data.id === id) {
        setFirstApiCompleted(true);
        return;
      }

      // Call the extracted function
      await fetchProjectDetails(
        id as string,
        dispatch,
        showToast,
        setFirstApiCompleted
      );
    };

    loadProjectDetails();
  }, []);

  useEffect(() => {
    const loadSecondFoldProjectDetails = async () => {
      // Check if project details data is already present and matches the current ID
      if (state.projectDetails.data && state.projectDetails.data.id === id) {
        setSecondApiCompleted(true);
        return;
      }

      // Call the extracted function
      await fetchSecondFoldProjectDetails(
        id as string,
        dispatch,
        showToast,
        setSecondApiCompleted
      );
    };

    loadSecondFoldProjectDetails();
  }, []);
  useEffect(() => {
    return () => {
      // Clear chatbox messages when component unmounts
      dispatch({
        type: ActionTypes.CLEAR_CHATBOX_MESSAGES,
      });

      // Reset project details and loading states when component unmounts
      dispatch({
        type: ActionTypes.RESET_PROJECT_DETAILS,
      });
    };
  }, [dispatch]);
  // Refs to track previous status states
  const prevLandingPageStatus = useRef<string | null>(null);
  const prevContentCalendarStatus = useRef<string | null>(null);
  const prevSocialMediaStatus = useRef<string | null>(null);
  const prevContextStatus = useRef<string | null>(null);

  // First load flags to avoid API calls on initial load
  const isFirstLandingPageLoad = useRef(true);
  const isFirstContentCalendarLoad = useRef(true);
  const isFirstSocialMediaLoad = useRef(true);
  const isFirstContextLoad = useRef(true);

  useEffect(() => {
    const unsubscribeLandingPageUpdate = listenForLandingPageUpdate(
      id as string,
      (data) => {
        // Check if transitioning from in_progress to done or error
        const isTransitioningToDone =
          prevLandingPageStatus.current === IN_PROGRESS_STATUS &&
          (data.status === 'done' || data.status === 'error');

        // Update state - only show loading for in_progress
        dispatch({
          type: ActionTypes.SET_LOADING_STATE,
          payload: {
            key: LOADING_STATE_LANDING_PAGE_GENERATION,
            value:
              data.status === IN_PROGRESS_STATUS
                ? true
                : data.status === 'error'
                  ? undefined
                  : false,
          },
        });

        // If transitioning to done and not first load, fetch data
        if (isTransitioningToDone && !isFirstLandingPageLoad.current) {
          fetchSecondFoldProjectDetails(id as string, dispatch, showToast);
        }

        // Update previous status and mark first load complete
        prevLandingPageStatus.current = data.status;
        isFirstLandingPageLoad.current = false;
      }
    );

    const unsubscribeCampaigns = listenForCampaigns(id as string, (data) => {
      // Check if transitioning from in_progress to done or error
      const isTransitioningToDone =
        prevContentCalendarStatus.current === IN_PROGRESS_STATUS &&
        (data.status === 'done' || data.status === 'error');

      // Update state - only show loading for in_progress
      dispatch({
        type: ActionTypes.SET_LOADING_STATE,
        payload: {
          key: LOADING_STATE_CONTENT_CALENDAR_GENERATION,
          value:
            data.status === IN_PROGRESS_STATUS
              ? true
              : data.status === 'error'
                ? undefined
                : false,
        },
      });

      // If transitioning to done and not first load, fetch data
      if (isTransitioningToDone && !isFirstContentCalendarLoad.current) {
        fetchSecondFoldProjectDetails(id as string, dispatch, showToast);
      }

      // Update previous status and mark first load complete
      prevContentCalendarStatus.current = data.status;
      isFirstContentCalendarLoad.current = false;
    });

    const unsubscribeSocialMediaUpdate = listenForSocialMediaUpdate(
      id as string,
      (data) => {
        // Check if transitioning from in_progress to done or error
        const isTransitioningToDone =
          prevSocialMediaStatus.current === IN_PROGRESS_STATUS &&
          (data.status === 'done' || data.status === 'error');

        // Update state - only show loading for in_progress
        dispatch({
          type: ActionTypes.SET_LOADING_STATE,
          payload: {
            key: LOADING_STATE_SOCIAL_MEDIA_GENERATION,
            value:
              data.status === IN_PROGRESS_STATUS
                ? true
                : data.status === 'error'
                  ? undefined
                  : false,
          },
        });

        // If transitioning to done and not first load, fetch data
        if (isTransitioningToDone && !isFirstSocialMediaLoad.current) {
          fetchProjectDetails(id as string, dispatch, showToast);
        }

        // Update previous status and mark first load complete
        prevSocialMediaStatus.current = data.status;
        isFirstSocialMediaLoad.current = false;
      }
    );

    const unsubscribeContextGeneration = listenForContextGeneration(
      id as string,
      (data) => {
        // Check if transitioning from in_progress to done or error
        const isTransitioningToDone =
          prevContextStatus.current === IN_PROGRESS_STATUS &&
          (data.status === 'done' || data.status === 'error');

        // Update state - only show loading for in_progress
        dispatch({
          type: ActionTypes.SET_LOADING_STATE,
          payload: {
            key: LOADING_STATE_CONTEXT_GENERATION,
            value:
              data.status === IN_PROGRESS_STATUS
                ? true
                : data.status === 'error'
                  ? undefined
                  : false,
          },
        });

        // If transitioning to done and not first load, fetch data
        if (isTransitioningToDone && !isFirstContextLoad.current) {
          fetchProjectDetails(id as string, dispatch, showToast);
        }

        // Update previous status and mark first load complete
        prevContextStatus.current = data.status;
        isFirstContextLoad.current = false;
      }
    );

    return () => {
      unsubscribeLandingPageUpdate();
      unsubscribeCampaigns();
      unsubscribeSocialMediaUpdate();
      unsubscribeContextGeneration();
    };
  }, []);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  // Dynamic class for main content margin based on sidebar state
  const mainContentMargin = isMobileOpen
    ? 'ml-0'
    : isExpanded || isHovered
      ? 'lg:ml-[290px]'
      : 'lg:ml-[90px]';

  // Function to handle chat submission
  const handleChatSubmit = async (
    message: string,
    conversationId?: string,
    businessIdeaId?: string
  ) => {
    try {
      // Make API call with the message
      const response = await apiService.post<{
        response?: string;
        conversation_id?: string;
      }>('/api/chat', {
        query: message,
        conversation_id: conversationId,
        business_idea_id: businessIdeaId || id, // Use the current project ID if no business_idea_id is provided
      });

      if (response.success) {
        return {
          success: true,
          response: response.data?.response || 'Message received',
          conversation_id: response.data?.conversation_id,
        };
      } else {
        return {
          success: false,
          error: response.message || 'Failed to process message',
        };
      }
    } catch (error) {
      console.error('Error submitting chat message:', error);
      return {
        success: false,
        error: 'An error occurred while processing your message',
      };
    }
  };

  // Get chatbox state from global context
  const { isOpen } = state.chatboxMessages;

  return (
    <div className="flex h-screen mt-12 overflow-hidden">
      {/* Sidebar and Backdrop */}
      <AppSidebar />
      <Backdrop />
      {/* Main Content Area */}
      <main
        className={`flex-1 transition-all relative overflow-auto duration-300 ease-in-out ${mainContentMargin}`}
      >
        {/* Header */}
        <AppHeader />
        {/* Page Content */}
        <div className="mx-auto max-w-screen-2xl bg-white">{children}</div>

        {/* ChatBox component using global state */}
        <ChatBox
          isOpen={isOpen}
          onClose={() =>
            dispatch({ type: ActionTypes.SET_CHATBOX_OPEN, payload: false })
          }
          onSubmit={handleChatSubmit}
          initialMessage="Hello! How can I help you today?"
        />
      </main>
    </div>
  );
}

export default withAuth(AdminLayout);
