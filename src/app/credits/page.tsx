'use client';

import React, { useEffect, useState } from 'react';
import {
  creditService,
  CreditTransaction,
  CreditFeature,
  CreditPlan,
} from '@/services/credit';
// Button component is now replaced with native buttons
import ThreeDotLoader from '@/components/loading/ThreeDotLoader';
import AppHeader from '@/layout/AppHeader';
import { useToast } from '@/context/ToastContext';
import { useAuth } from '@/context/AuthContext';
import { useCredit } from '@/context/CreditContext';
import { apiService, CREDIT_BALANCE_UPDATE_EVENT } from '@/services/api';
import {
  API_ENDPOINT_CREATE_PAYMENT_ORDER,
  API_ENDPOINT_VERIFY_PAYMENT,
} from '@/constants/api/api.constants';
import Script from 'next/script';
import PurchaseButton from '@/components/credits/PurchaseButton';

// Define Razorpay window object
declare global {
  interface Window {
    Razorpay: any;
  }
}

export default function CreditsPage() {
  const { showToast } = useToast();
  const { user } = useAuth();
  const { balance } = useCredit();
  const [transactions, setTransactions] = useState<CreditTransaction[]>([]);
  const [features, setFeatures] = useState<CreditFeature[]>([]);
  const [plans, setPlans] = useState<CreditPlan[]>([]);
  const [transactionsLoading, setTransactionsLoading] = useState(true);
  const [featuresLoading, setFeaturesLoading] = useState(true);
  const [plansLoading, setPlansLoading] = useState(true);
  // No need for processingPlanId state as each button manages its own state
  const [openFaqs, setOpenFaqs] = useState<number[]>([]);

  // Toggle FAQ accordion
  const toggleFaq = (index: number) => {
    if (openFaqs.includes(index)) {
      setOpenFaqs(openFaqs.filter((i) => i !== index));
    } else {
      setOpenFaqs([...openFaqs, index]);
    }
  };

  // Fetch transactions
  const fetchTransactions = async () => {
    setTransactionsLoading(true);
    const data = await creditService.getTransactions(10, 0);
    setTransactions(data);
    setTransactionsLoading(false);
  };

  // Fetch features
  const fetchFeatures = async () => {
    setFeaturesLoading(true);
    const data = await creditService.getFeatures();
    setFeatures(data);
    setFeaturesLoading(false);
  };

  // Fetch plans
  const fetchPlans = async () => {
    setPlansLoading(true);
    const data = await creditService.getPlans();
    setPlans(data);
    setPlansLoading(false);
  };

  useEffect(() => {
    fetchTransactions();
    fetchFeatures();
    fetchPlans();

    // Trigger credit balance update event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent(CREDIT_BALANCE_UPDATE_EVENT));
    }
  }, []);

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  // Format price
  const formatPrice = (price: number, currency: string) => {
    if (currency === 'INR') {
      return `₹${Math.floor(price / 100)}`;
    }
    return `${Math.floor(price / 100)} ${currency}`;
  };

  // Get icon background color based on feature name
  const getIconBgColor = (featureName: string) => {
    const name = featureName.toLowerCase();
    if (name.includes('project') || name.includes('workspace')) {
      return 'bg-blue-100';
    } else if (name.includes('campaign') || name.includes('marketing')) {
      return 'bg-purple-100';
    } else if (name.includes('content') || name.includes('text') || name.includes('copy')) {
      return 'bg-green-100';
    } else if (name.includes('landing') || name.includes('page') || name.includes('website')) {
      return 'bg-orange-100';
    } else if (name.includes('image') || name.includes('photo') || name.includes('design')) {
      return 'bg-pink-100';
    } else if (name.includes('analytics') || name.includes('report')) {
      return 'bg-yellow-100';
    } else {
      return 'bg-indigo-100';
    }
  };

  // Get feature icon based on feature name
  const getFeatureIcon = (featureName: string) => {
    const name = featureName.toLowerCase();

    if (name.includes('project') || name.includes('workspace')) {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
          <path d="M3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1zm1 2v14h16V5H4zm3 8h2v4H7v-4zm4-6h2v10h-2V7zm4 3h2v7h-2v-7z" />
        </svg>
      );
    } else if (name.includes('campaign') || name.includes('marketing')) {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-600" viewBox="0 0 24 24" fill="currentColor">
          <path d="M21 3a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h18zm-1 2H4v14h16V5zM8 7v10H6V7h2zm6 0v6h-2V7h2zm4 0v8h-2V7h2z" />
        </svg>
      );
    } else if (name.includes('content') || name.includes('text') || name.includes('copy')) {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-600" viewBox="0 0 24 24" fill="currentColor">
          <path d="M21 8v12.993A1 1 0 0 1 20.007 22H3.993A.993.993 0 0 1 3 21.008V2.992C3 2.455 3.449 2 4.002 2h10.995L21 8zm-2 1h-5V4H5v16h14V9zM8 7h3v2H8V7zm0 4h8v2H8v-2zm0 4h8v2H8v-2z" />
        </svg>
      );
    } else if (name.includes('landing') || name.includes('page') || name.includes('website')) {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-orange-600" viewBox="0 0 24 24" fill="currentColor">
          <path d="M3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1zm17 8H4v8h16v-8zm0-2V5H4v4h16zm-5-3h4v2h-4V6z" />
        </svg>
      );
    } else if (name.includes('image') || name.includes('photo') || name.includes('design')) {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-pink-600" viewBox="0 0 24 24" fill="currentColor">
          <path d="M5 11.1l2-2 5.5 5.5 3.5-3.5 3 3V5H5v6.1zm0 2.829V19h3.1l2.55-2.55-2.55-2.55L5 16.9zm2 1.414l1.414-1.414 1.414 1.414-1.414 1.414L7 15.243zM4 3h16a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1zm11.3 11.3l-3.5 3.5L17 22h1a1 1 0 0 0 1-1v-1l-3.7-5.7z" />
        </svg>
      );
    } else if (name.includes('analytics') || name.includes('report')) {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-600" viewBox="0 0 24 24" fill="currentColor">
          <path d="M5 3v16h16v2H3V3h2zm15.293 3.293l1.414 1.414L16 13.414l-3-2.999-4.293 4.292-1.414-1.414L13 7.586l3 2.999 4.293-4.292z" />
        </svg>
      );
    } else {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-indigo-600" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-1-11v6h2v-6h-2zm0-4v2h2V7h-2z" />
        </svg>
      );
    }
  };

  // Handle credit purchase
  const handlePurchaseCredits = async (plan: CreditPlan, setProcessing: (isProcessing: boolean) => void) => {
    if (!user) {
      showToast('Please log in to purchase credits', 'error');
      setProcessing(false);
      return;
    }

    try {
      // Create a Razorpay order
      // Convert price to paise (Razorpay requires amount in smallest currency unit)
      const amountInPaise = plan.price;

      const response = await apiService.post(
        API_ENDPOINT_CREATE_PAYMENT_ORDER,
        {
          amount: amountInPaise,
          currency: plan.currency || 'INR',
          receipt: `credit_purchase_${Date.now()}`,
          notes: {
            purpose: `Credit Purchase - ${plan.name}`,
            plan_id: plan.id,
            credits: plan.credits,
            type: 'credit_purchase',
          },
          user_id: user.uid,
        }
      );

      console.log('Create order response:', response);

      if (response.success && response.data) {
        // Use type assertion to handle the unknown structure
        const data = response.data as { order_id?: string; key_id?: string };
        console.log('Order data:', data);

        if (data.order_id) {
          console.log('Order ID received:', data.order_id);

          // Initialize Razorpay payment
          initializeRazorpayPayment(data.order_id, plan, data.key_id, setProcessing);
        } else {
          console.error('Order ID missing in response:', data);
          showToast(
            'Failed to create payment order: Missing order ID',
            'error'
          );
          setProcessing(false);
        }
      } else {
        console.error('Failed order response:', response);
        showToast('Failed to create payment order', 'error');
        setProcessing(false);
      }
    } catch (error) {
      console.error('Error creating order:', error);
      showToast('Failed to create payment order', 'error');
      setProcessing(false);
    }
  };

  // Initialize Razorpay payment
  const initializeRazorpayPayment = (
    orderId: string,
    plan: CreditPlan,
    keyId?: string,
    setProcessing?: (isProcessing: boolean) => void
  ) => {
    // Check if Razorpay key is available
    const razorpayKeyId = keyId || process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID;
    if (!razorpayKeyId) {
      console.error(
        'Razorpay Key ID is missing. Please check your environment variables.'
      );
      showToast(
        'Payment configuration error. Please contact support.',
        'error'
      );
      if (setProcessing) setProcessing(false);
      return;
    }

    console.log('Using Razorpay Key:', razorpayKeyId);

    // Initialize Razorpay payment
    const options = {
      key: razorpayKeyId,
      amount: plan.price,
      currency: plan.currency || 'INR',
      name: 'Dolze AI',
      description: `Purchase ${plan.credits} Credits`,
      order_id: orderId,
      handler: async function (response: any) {
        try {
          // Verify the payment with our backend
          const verificationResponse = await apiService.post(
            API_ENDPOINT_VERIFY_PAYMENT,
            {
              razorpay_order_id: response.razorpay_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
              user_id: user?.uid,
            }
          );

          if (verificationResponse.success) {
            showToast(
              `Successfully purchased ${plan.credits} credits!`,
              'success'
            );

            // Fetch updated data instead of refreshing the page
            setTimeout(async () => {
              try {
                // Trigger credit balance update event
                if (typeof window !== 'undefined') {
                  window.dispatchEvent(
                    new CustomEvent(CREDIT_BALANCE_UPDATE_EVENT)
                  );
                }

                // Refresh transactions
                await fetchTransactions();

                // Reset payment state
                if (setProcessing) setProcessing(false);
              } catch (error) {
                console.error('Error refreshing data after payment:', error);
                showToast(
                  "Payment was successful, but we couldn't refresh your data. Please reload the page.",
                  'warning'
                );
              }
            }, 0);
          } else {
            showToast(
              'Payment verification failed. Please contact support.',
              'error'
            );
            if (setProcessing) setProcessing(false);
          }
        } catch (error) {
          console.error('Payment verification error:', error);
          showToast(
            'Payment verification failed. Please contact support.',
            'error'
          );
          if (setProcessing) setProcessing(false);
        }
      },
      prefill: {
        name: user?.displayName || '',
        email: user?.email || '',
      },
      theme: {
        color: '#6366F1',
      },
      modal: {
        ondismiss: function () {
          if (setProcessing) setProcessing(false);
          showToast('Payment cancelled', 'info');
        },
      },
    };

    try {
      console.log('Initializing Razorpay with options:', {
        ...options,
        key: options.key ? options.key.substring(0, 5) + '...' : 'undefined', // Log partial key for security
      });

      // Check if Razorpay is loaded
      if (!window.Razorpay) {
        console.error('Razorpay SDK not loaded');
        showToast(
          'Payment gateway not loaded. Please refresh the page.',
          'error'
        );
        if (setProcessing) setProcessing(false);
        return;
      }

      const razorpayInstance = new window.Razorpay(options);
      console.log('Razorpay instance created successfully');
      razorpayInstance.open();
    } catch (error) {
      console.error('Razorpay initialization error:', error);
      showToast('Failed to initialize payment gateway', 'error');
      if (setProcessing) setProcessing(false);
    }
  };

  return (
    <div>
      <Script
        src="https://checkout.razorpay.com/v1/checkout.js"
        strategy="lazyOnload"
      />
      <AppHeader />
      <div className="container mx-auto px-4 py-8">
        <div className="pt-10"></div>

        {/* Credit Summary Section */}
        <div className="bg-white rounded shadow-sm p-6 mb-8">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-xl font-semibold text-gray-800">Credit Summary</h2>
            <button
              className="flex items-center justify-center bg-gray-50 hover:bg-gray-100 text-gray-600 text-sm rounded-full px-2.5 py-1.5 whitespace-nowrap border border-gray-200 transition-colors"
              onClick={() => {
                if (typeof window !== 'undefined') {
                  window.dispatchEvent(
                    new CustomEvent(CREDIT_BALANCE_UPDATE_EVENT)
                  );
                  showToast('Credit balance refreshed', 'success');
                }
              }}
              title="Refresh balance"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-1"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2" />
              </svg>
              Refresh
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Available Credits */}
            <div className="bg-gradient-to-br from-indigo-100 to-indigo-50 rounded-lg p-8 relative overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 -mr-8 -mt-8 opacity-10">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-full w-full text-indigo-600"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-1-10v4h2v-4h3l-4-4-4 4h3z" />
                </svg>
              </div>
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 flex items-center justify-center bg-indigo-200 rounded-full mr-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-indigo-600"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-1-10v4h2v-4h3l-4-4-4 4h3z" />
                  </svg>
                </div>
                <span className="text-gray-600 text-lg">Available Credits</span>
              </div>
              <div className="text-4xl font-bold text-gray-900">
                {balance ? new Intl.NumberFormat().format(balance.balance) : <ThreeDotLoader />}
              </div>
            </div>

            {/* Total Purchased */}
            <div className="bg-gradient-to-br from-green-50 to-green-50/50 rounded-lg p-8 relative overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 -mr-8 -mt-8 opacity-10">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-full w-full text-green-600"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm-3-7h8v2H9v-2zm2-4h4v2h-4V9z" />
                </svg>
              </div>
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 flex items-center justify-center bg-green-100 rounded-full mr-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-green-600"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm-3-7h8v2H9v-2zm2-4h4v2h-4V9z" />
                  </svg>
                </div>
                <span className="text-gray-600 text-lg">Total Purchased</span>
              </div>
              <div className="text-4xl font-bold text-gray-900">
                {balance ? new Intl.NumberFormat().format(balance.total_purchased) : <ThreeDotLoader />}
              </div>
            </div>

            {/* Total Used */}
            <div className="bg-gradient-to-br from-orange-50 to-orange-50/50 rounded-lg p-8 relative overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 -mr-8 -mt-8 opacity-10">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-full w-full text-orange-600"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm1-10v4h3l-4 4-4-4h3V10h2z" />
                </svg>
              </div>
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 flex items-center justify-center bg-orange-100 rounded-full mr-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-orange-600"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm1-10v4h3l-4 4-4-4h3V10h2z" />
                  </svg>
                </div>
                <span className="text-gray-600 text-lg">Total Used</span>
              </div>
              <div className="text-4xl font-bold text-gray-900">
                {balance ? new Intl.NumberFormat().format(balance.total_used) : <ThreeDotLoader />}
              </div>
            </div>
          </div>
        </div>

        {/* Add Credits Section */}
        <div className="bg-gradient-to-b from-white to-gray-50 rounded-xl shadow-sm p-12 mb-16" id="credit-plans">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Credit Package</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Select the package that best fits your needs. All credits never expire and can be used across our entire platform.
            </p>
          </div>

          {plansLoading ? (
            <div className="flex items-center justify-center p-8">
              <ThreeDotLoader />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto relative">
              {plans.map((plan, index) => {
                // Determine plan icon and name based on index or plan name
                let planIcon: React.ReactNode;
                let planName = plan.name;

                if (index === 0 || (planName && planName.toLowerCase().includes('starter'))) {
                  planIcon = (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-indigo-600" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M5.64 3.64L21.36 12 5.64 20.36V3.64z" />
                    </svg>
                  );
                  planName = planName || "Starter";
                } else if (index === 1 || (planName && planName.toLowerCase().includes('pro'))) {
                  planIcon = (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-indigo-600" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2l2.582 6.953L22 9.257l-5.822 4.602L18.18 21 12 16.891 5.82 21l2.002-7.141L2 9.257l7.418-.304L12 2z" />
                    </svg>
                  );
                  planName = planName || "Pro";
                } else {
                  planIcon = (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-indigo-600" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M21 19h2v2H1v-2h2V4a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v15h2V9h3a1 1 0 0 1 1 1v9zM5 5v14h8V5H5z" />
                    </svg>
                  );
                  planName = planName || "Business";
                }

                // Calculate price per credit
                const pricePerCredit = (plan.price / 100 / plan.credits).toFixed(2);

                return (
                  <div
                    key={plan.id || `plan-${index}`}
                    className={`bg-white rounded-xl p-8 ${index === 1 ? 'shadow-xl border-2 border-indigo-600 relative transform hover:-translate-y-1' : 'shadow-sm hover:shadow-xl transform hover:-translate-y-1'} transition-all duration-300`}
                  >
                    {/* Popular badge for middle plan */}
                    {index === 1 && (
                      <div className="absolute -top-4 left-1/2 -translate-x-1/2 bg-indigo-600 text-white text-sm font-medium px-6 py-1.5 rounded-full whitespace-nowrap shadow-lg">
                        Most Popular
                      </div>
                    )}

                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-2xl font-bold text-gray-900">{planName}</h3>
                      <div className="w-12 h-12 flex items-center justify-center bg-indigo-50 rounded-full">
                        {planIcon}
                      </div>
                    </div>

                    <p className="text-gray-600 mb-6 h-12">
                      {plan.description ||
                        (index === 0 ? 'Perfect for individuals just getting started' :
                         index === 1 ? 'Best value for growing businesses' :
                         'Ideal for large scale operations')}
                    </p>

                    <div className="flex items-baseline mb-2">
                      <span className="text-4xl font-bold text-gray-900">
                        {formatPrice(plan.price, plan.currency)}
                      </span>
                      <span className="text-gray-500 ml-2">one-time</span>
                    </div>

                    <div className="text-sm text-indigo-600 font-medium mb-8">
                      ₹{pricePerCredit} per credit
                    </div>

                    <ul className="mb-8 space-y-4">
                      <li className="flex items-center">
                        <div className="w-5 h-5 flex items-center justify-center text-indigo-600 mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-gray-700">{plan.credits.toLocaleString()} credits included</span>
                      </li>

                      <li className="flex items-center">
                        <div className="w-5 h-5 flex items-center justify-center text-indigo-600 mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-gray-700">Access to all features</span>
                      </li>

                      <li className="flex items-center">
                        <div className="w-5 h-5 flex items-center justify-center text-indigo-600 mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-gray-700">
                          {index === 0 ? 'Basic email support' :
                           index === 1 ? 'Priority support' :
                           '24/7 priority support'}
                        </span>
                      </li>

                      {/* Removed dedicated account manager */}
                    </ul>

                    <div className="w-full mb-4">
                      <PurchaseButton
                        plan={plan}
                        index={index}
                        onPurchase={handlePurchaseCredits}
                      />
                    </div>

                    {/* Removed secure payment message */}
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* How Credits Work Section */}
        <div className="bg-white rounded shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-8 text-center">How Credits Work</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="border border-gray-100 rounded-lg p-6 shadow-sm">
              <div className="w-16 h-16 flex items-center justify-center bg-indigo-50 rounded-full mb-4 mx-auto">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-indigo-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M4 3h16a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1zm1 2v14h14V5H5zm6 3h2v8h-2v-6H9V9h2zm6 0v2h-2V8h2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 text-center mb-3">Purchase Credits</h3>
              <p className="text-gray-600 text-center">Buy credit packages that fit your needs. The more credits you purchase at once, the better value you get.</p>
            </div>

            <div className="border border-gray-100 rounded-lg p-6 shadow-sm">
              <div className="w-16 h-16 flex items-center justify-center bg-indigo-50 rounded-full mb-4 mx-auto">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-indigo-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M3 4h18v2H3V4zm0 7h18v2H3v-2zm0 7h18v2H3v-2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 text-center mb-3">Use Features</h3>
              <p className="text-gray-600 text-center">Each feature in our platform costs a specific number of credits. Use your credits to access the features you need.</p>
            </div>

            <div className="border border-gray-100 rounded-lg p-6 shadow-sm">
              <div className="w-16 h-16 flex items-center justify-center bg-indigo-50 rounded-full mb-4 mx-auto">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-indigo-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm-1-5h2v2h-2v-2zm0-8h2v6h-2V7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 text-center mb-3">Track Usage</h3>
              <p className="text-gray-600 text-center">Monitor your credit balance and usage history on this page. Purchase more credits whenever you need them.</p>
            </div>
          </div>
        </div>

        {/* Credit Cost Per Feature Section */}
        <div className="bg-white rounded shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-6">Credit Cost Per Feature</h2>
          {featuresLoading ? (
            <div className="flex items-center justify-center p-8">
              <ThreeDotLoader />
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Feature</th>
                    <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th className="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Cost (Credits)</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {features.map((feature, index) => (
                    <tr
                      key={feature.id || `feature-${feature.feature_key}-${index}`}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className={`w-8 h-8 flex items-center justify-center rounded-full mr-3 ${getIconBgColor(feature.name)}`}>
                            {getFeatureIcon(feature.name)}
                          </div>
                          <span className="font-medium text-gray-900">{feature.name}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className="text-gray-600">{feature.description}</span>
                      </td>
                      <td className="px-6 py-4 text-right">
                        <span className="font-medium text-gray-900">{feature.credit_cost}</span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Transaction History Section */}
        <div className="bg-white rounded shadow-sm p-6 mb-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-800">Transaction History</h2>
            <button
              className="flex items-center justify-center bg-gray-50 hover:bg-gray-100 text-gray-600 text-sm rounded-full px-2.5 py-1.5 whitespace-nowrap border border-gray-200 transition-colors"
              onClick={() => {
                fetchTransactions();
                showToast('Transactions refreshed', 'success');
              }}
              title="Refresh transactions"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-1"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2" />
              </svg>
              Refresh
            </button>
          </div>

          {transactionsLoading ? (
            <div className="flex items-center justify-center p-8">
              <ThreeDotLoader />
            </div>
          ) : transactions.length > 0 ? (
            <div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr>
                      <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                      <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                      <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                      <th className="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Credit Used</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {transactions.map((transaction, index) => (
                      <tr
                        key={transaction.id || `transaction-${index}`}
                        className="hover:bg-gray-50 transition-colors"
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                          {formatDate(transaction.created_at)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 py-1 text-xs font-medium ${['purchase', 'bonus'].includes(transaction.transaction_type) ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} rounded-full`}
                          >
                            {['purchase', 'bonus'].includes(transaction.transaction_type) ? 'Purchase' : 'Usage'}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-600">
                          {transaction.description}
                        </td>
                        <td
                          className={`px-6 py-4 text-right text-sm font-medium ${transaction.amount > 0 ? 'text-green-600' : 'text-red-600'}`}
                        >
                          {transaction.amount > 0 ? '+' : ''}
                          {transaction.amount}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="flex justify-between items-center mt-4">
                <div className="text-xs text-gray-500">
                  Showing 1 to {Math.min(transactions.length, 5)} of {transactions.length} entries
                </div>
                <div className="flex space-x-1">
                  <button
                    className="flex items-center justify-center bg-white border border-gray-200 text-gray-500 text-xs rounded px-2 py-0.5 whitespace-nowrap disabled:opacity-50"
                    disabled={true}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-0.5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    Previous
                  </button>
                  <button
                    className="flex items-center justify-center bg-white border border-gray-200 text-gray-500 text-xs rounded px-2 py-0.5 whitespace-nowrap"
                    onClick={() => fetchTransactions()}
                  >
                    Next
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 ml-0.5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-8 w-8 text-indigo-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <h3 className="text-xl font-medium text-gray-900 mb-2">
                  No Transactions Yet
                </h3>
                <p className="text-gray-600 mb-6">
                  Your transaction history will appear here once you start using credits.
                </p>
                <button
                  className="bg-white border-2 border-indigo-600 text-gray-800 hover:bg-indigo-600 hover:text-white transition-colors duration-300 rounded-lg px-4 py-2 font-medium"
                  onClick={() => {
                    const plansElement = document.getElementById('credit-plans');
                    if (plansElement) {
                      window.scrollTo({
                        top: plansElement.offsetTop - 100,
                        behavior: 'smooth',
                      });
                    }
                  }}
                >
                  Buy Credits
                </button>
              </div>
            </div>
          )}
        </div>

        {/* FAQ Section */}
        <div className="bg-white rounded shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-6">Frequently Asked Questions</h2>

          <div className="space-y-4">
            {/* FAQ Item 1 */}
            <div className="border border-gray-200 rounded-lg">
              <button
                className="w-full flex justify-between items-center p-4 text-left focus:outline-none"
                onClick={() => toggleFaq(0)}
              >
                <span className="font-medium text-gray-800">How do credits work?</span>
                <svg
                  className={`w-5 h-5 text-gray-500 transform transition-transform ${openFaqs.includes(0) ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
              <div
                className={`overflow-hidden transition-all duration-300 ${openFaqs.includes(0) ? 'max-h-96' : 'max-h-0'}`}
              >
                <div className="p-4 border-t border-gray-100 text-gray-600 text-sm">
                  Credits are the currency used to access premium features in our platform. Each feature costs a specific number of credits, and you can purchase credit packages to use these features. Credits give you flexibility to pay only for what you use, without recurring subscription fees.
                </div>
              </div>
            </div>

            {/* FAQ Item 2 */}
            <div className="border border-gray-200 rounded-lg">
              <button
                className="w-full flex justify-between items-center p-4 text-left focus:outline-none"
                onClick={() => toggleFaq(1)}
              >
                <span className="font-medium text-gray-800">Do credits expire?</span>
                <svg
                  className={`w-5 h-5 text-gray-500 transform transition-transform ${openFaqs.includes(1) ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
              <div
                className={`overflow-hidden transition-all duration-300 ${openFaqs.includes(1) ? 'max-h-96' : 'max-h-0'}`}
              >
                <div className="p-4 border-t border-gray-100 text-gray-600 text-sm">
                  No, credits do not expire. Once purchased, they remain in your account until used. You can buy credits now and use them whenever you need them.
                </div>
              </div>
            </div>

            {/* FAQ Item 3 */}
            <div className="border border-gray-200 rounded-lg">
              <button
                className="w-full flex justify-between items-center p-4 text-left focus:outline-none"
                onClick={() => toggleFaq(2)}
              >
                <span className="font-medium text-gray-800">Can I get a refund for unused credits?</span>
                <svg
                  className={`w-5 h-5 text-gray-500 transform transition-transform ${openFaqs.includes(2) ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
              <div
                className={`overflow-hidden transition-all duration-300 ${openFaqs.includes(2) ? 'max-h-96' : 'max-h-0'}`}
              >
                <div className="p-4 border-t border-gray-100 text-gray-600 text-sm">
                  We do not offer refunds for purchased credits. However, if you encounter any issues with our service, please contact our support team and we'll do our best to resolve the issue.
                </div>
              </div>
            </div>

            {/* FAQ Item 4 */}
            <div className="border border-gray-200 rounded-lg">
              <button
                className="w-full flex justify-between items-center p-4 text-left focus:outline-none"
                onClick={() => toggleFaq(3)}
              >
                <span className="font-medium text-gray-800">How do I know when I'm running low on credits?</span>
                <svg
                  className={`w-5 h-5 text-gray-500 transform transition-transform ${openFaqs.includes(3) ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
              <div
                className={`overflow-hidden transition-all duration-300 ${openFaqs.includes(3) ? 'max-h-96' : 'max-h-0'}`}
              >
                <div className="p-4 border-t border-gray-100 text-gray-600 text-sm">
                  You'll see a low credit indicator when your balance falls below a certain threshold. You can always check your current balance at the top of this page. We'll also notify you when you're running low on credits.
                </div>
              </div>
            </div>

            {/* FAQ Item 5 */}
            <div className="border border-gray-200 rounded-lg">
              <button
                className="w-full flex justify-between items-center p-4 text-left focus:outline-none"
                onClick={() => toggleFaq(4)}
              >
                <span className="font-medium text-gray-800">Can I transfer credits to another account?</span>
                <svg
                  className={`w-5 h-5 text-gray-500 transform transition-transform ${openFaqs.includes(4) ? 'rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
              <div
                className={`overflow-hidden transition-all duration-300 ${openFaqs.includes(4) ? 'max-h-96' : 'max-h-0'}`}
              >
                <div className="p-4 border-t border-gray-100 text-gray-600 text-sm">
                  Currently, credits cannot be transferred between accounts. They are tied to the account that purchased them. If you need credits for multiple accounts, you'll need to purchase them separately for each account.
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* How Credits Work Section moved up */}

        {/* Popular Use Cases section removed */}
      </div>
    </div>
  );
}
