import { MockPost } from '@/types/api';
import { apiService } from './api';
import { API_ENDPOINT_UPDATE_POST } from '@/constants/api/api.constants';
/**
 * Updates a social media post
 * @param post The updated post data
 * @returns Promise with the updated post data
 */
export const updatePost = async (post: MockPost): Promise<MockPost> => {
  try {
    const response = await apiService.post<MockPost>(
      API_ENDPOINT_UPDATE_POST,
      post
    );

    if (!response.success) {
      throw new Error(`Error: ${response.message}`);
    }

    return response.data;
  } catch (error) {
    console.error('Error updating post:', error);
    throw error;
  }
};
