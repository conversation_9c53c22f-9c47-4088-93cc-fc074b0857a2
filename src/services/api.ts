import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
} from 'axios';
import { getAuth } from 'firebase/auth';
import {
  HTTP_METHOD_GET,
  HTTP_METHOD_POST,
  HTTP_METHOD_PUT,
  HTTP_METHOD_DELETE,
  STATUS_CODE_UNAUTHORIZED,
  STATUS_CODE_SERVER_ERROR,
  ERROR_NETWORK,
  ERROR_SERVER,
} from '@/constants';

interface ApiResponse<T = unknown> {
  data: T;
  status: number;
  message: string;
  success: boolean;
}

// Credit-consuming endpoints that should trigger a balance refresh
const CREDIT_CONSUMING_ENDPOINTS = [
  '/api/validate-idea',
  '/api/business-context/get-business-context',
  '/api/business-context/save-business-context',
  '/api/landing-page/create-page',
  '/api/campaigns/create-campaign',
  '/api/generate-posts',
  '/api/social/post',
];

// Endpoints that should NOT trigger a balance refresh (to prevent infinite loops)
const EXCLUDED_ENDPOINTS = [
  '/api/credits/balance',
  '/api/credits/transactions',
  '/api/credits/features',
  '/api/credits/plans',
  '/api/credits/check',
];

// Event for credit balance updates
export const CREDIT_BALANCE_UPDATE_EVENT = 'credit-balance-update';

class ApiService {
  private static instance: ApiService;
  private axiosInstance: AxiosInstance;

  private constructor() {
    this.axiosInstance = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || '',
      timeout: 500000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  private setupInterceptors(): void {
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        const auth = getAuth();
        let user = auth.currentUser;

        if (!user) {
          // Wait for user to be available
          await new Promise<void>((resolve) => {
            const unsubscribe = auth.onAuthStateChanged((currentUser) => {
              user = currentUser;
              if (user) {
                unsubscribe(); // Unsubscribe from listener
                resolve();
              }
            });
          });
        }

        if (user) {
          const token = await user.getIdToken(); // Get fresh Firebase accessToken
          config.headers.Authorization = `Bearer ${token}`;
        }

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    this.axiosInstance.interceptors.response.use(
      (response) => response,
      async (error: AxiosError) => {
        if (error.response?.status === STATUS_CODE_UNAUTHORIZED) {
          const auth = getAuth();
          await auth.signOut(); // Sign out user on unauthorized error
          // Optionally, redirect to login page
        }
        return Promise.reject(error);
      }
    );
  }

  public async get<T>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse = await this.axiosInstance.request({
        method: HTTP_METHOD_GET,
        url,
        ...config,
      });
      return this.formatResponse(response);
    } catch (error) {
      return this.handleError(error as AxiosError);
    }
  }

  /**
   * Makes a GET request with lower priority
   * This is useful for non-critical data fetching that shouldn't block more important operations
   */
  public async getLowPriority<T>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      // Use a longer timeout for low priority requests
      const lowPriorityConfig: AxiosRequestConfig = {
        ...config,
        timeout: 600000, // 10 minutes timeout for low priority requests
        headers: {
          ...config?.headers,
          'X-Priority': 'low',
        },
      };

      // Add a small delay to deprioritize the request
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Make the request after the delay
      const response: AxiosResponse = await this.axiosInstance.request({
        method: HTTP_METHOD_GET,
        url,
        ...lowPriorityConfig,
      });

      return this.formatResponse(response);
    } catch (error) {
      return this.handleError(error as AxiosError);
    }
  }
  public async post<T>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse = await this.axiosInstance.request({
        method: HTTP_METHOD_POST,
        url,
        data,
        ...config,
      });
      return this.formatResponse(response);
    } catch (error) {
      return this.handleError(error as AxiosError);
    }
  }

  public async put<T>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse = await this.axiosInstance.request({
        method: HTTP_METHOD_PUT,
        url,
        data,
        ...config,
      });
      return this.formatResponse(response);
    } catch (error) {
      return this.handleError(error as AxiosError);
    }
  }

  public async delete<T>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse = await this.axiosInstance.request({
        method: HTTP_METHOD_DELETE,
        url,
        ...config,
      });
      return this.formatResponse(response);
    } catch (error) {
      return this.handleError(error as AxiosError);
    }
  }

  private formatResponse<T>(response: AxiosResponse): ApiResponse<T> {
    // Check if this is a credit-consuming endpoint
    const url = response.config.url;
    if (url && typeof window !== 'undefined') {
      // Check if the URL matches any excluded endpoint
      const isExcludedEndpoint = EXCLUDED_ENDPOINTS.some((endpoint) =>
        url.includes(endpoint)
      );

      // Skip excluded endpoints to prevent infinite loops
      if (!isExcludedEndpoint) {
        // Check if the URL matches any credit-consuming endpoint
        const isCreditConsumingEndpoint = CREDIT_CONSUMING_ENDPOINTS.some(
          (endpoint) => url.includes(endpoint)
        );

        // Only dispatch the event for credit-consuming endpoints
        if (
          isCreditConsumingEndpoint &&
          response.status >= 200 &&
          response.status < 300
        ) {
          // Use a custom event to notify about credit balance updates
          window.dispatchEvent(new CustomEvent(CREDIT_BALANCE_UPDATE_EVENT));
        }
      }
    }

    return {
      data: response.data,
      status: response.status,
      message: response.statusText,
      success: true,
    };
  }

  private handleError<T>(error: AxiosError): ApiResponse<T> {
    console.log(error);
    return {
      data: null as unknown as T,
      status: error.response?.status || STATUS_CODE_SERVER_ERROR,
      message: error.response
        ? error.response?.data?.detail?.message ||
          error.response?.data?.message?.message ||
          error.response?.data?.message ||
          ERROR_SERVER
        : ERROR_NETWORK,
      success: false,
    };
  }
}

export const apiService = ApiService.getInstance();
