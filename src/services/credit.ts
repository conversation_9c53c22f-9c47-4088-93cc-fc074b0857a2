import { apiService } from './api';
import {
  API_ENDPOINT_CREDIT_BALANCE,
  API_ENDPOINT_CREDIT_FEATURES,
  API_ENDPOINT_CREDIT_PLANS,
  API_ENDPOINT_CREDIT_TRANSACTIONS,
} from '@/constants/api/api.constants';

export interface CreditBalance {
  balance: number;
  total_purchased: number;
  total_used: number;
  last_transaction?: {
    amount: number;
    transaction_type: string;
    description: string;
    created_at: string;
  };
}

export interface CreditTransaction {
  id: string;
  user_id: string;
  amount: number;
  transaction_type: string;
  description: string;
  feature_id?: string;
  payment_id?: string;
  created_at: string;
  metadata?: Record<string, any>;
}

export interface CreditFeature {
  id: string;
  name: string;
  description: string;
  credit_cost: number;
  feature_key: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreditPlan {
  id: string;
  name: string;
  description: string;
  credits: number;
  price: number;
  currency: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreditCheckResult {
  has_enough_credits: boolean;
  required_credits: number;
  available_credits: number;
  message: string;
  feature_key: string;
}

export const creditService = {
  /**
   * Get the current credit balance
   */
  async getBalance(): Promise<CreditBalance | null> {
    try {
      const response = await apiService.get<CreditBalance>(
        API_ENDPOINT_CREDIT_BALANCE
      );
      if (response.success && response.data) {
        return response.data;
      }
      return null;
    } catch (error) {
      console.error('Error fetching credit balance:', error);
      return null;
    }
  },

  /**
   * Get credit transaction history
   */
  async getTransactions(limit = 10, skip = 0): Promise<CreditTransaction[]> {
    try {
      const response = await apiService.get<CreditTransaction[]>(
        `${API_ENDPOINT_CREDIT_TRANSACTIONS}?limit=${limit}&skip=${skip}`
      );
      if (response.success && response.data) {
        return response.data;
      }
      return [];
    } catch (error) {
      console.error('Error fetching credit transactions:', error);
      return [];
    }
  },

  /**
   * Get all credit features
   */
  async getFeatures(): Promise<CreditFeature[]> {
    try {
      const response = await apiService.get<CreditFeature[]>(
        API_ENDPOINT_CREDIT_FEATURES
      );
      if (response.success && response.data) {
        return response.data;
      }
      return [];
    } catch (error) {
      console.error('Error fetching credit features:', error);
      return [];
    }
  },

  /**
   * Get all credit plans
   */
  async getPlans(): Promise<CreditPlan[]> {
    try {
      const response = await apiService.get<CreditPlan[]>(
        API_ENDPOINT_CREDIT_PLANS
      );
      if (response.success && response.data) {
        return response.data;
      }
      return [];
    } catch (error) {
      console.error('Error fetching credit plans:', error);
      return [];
    }
  },

  /**
   * Handle API error related to insufficient credits
   */
  isInsufficientCreditsError(error: any): boolean {
    return error?.status === 402 || error?.statusCode === 402;
  },

  /**
   * Extract credit information from error
   */
  extractCreditInfoFromError(error: any): {
    requiredCredits: number;
    availableCredits: number;
    featureKey: string;
    message: string;
  } | null {
    if (!this.isInsufficientCreditsError(error)) {
      return null;
    }

    try {
      const detail = error.detail || error.data?.detail;
      if (detail) {
        return {
          requiredCredits: detail.required_credits || 0,
          availableCredits: detail.available_credits || 0,
          featureKey: detail.feature_key || '',
          message: detail.message || 'Insufficient credits',
        };
      }
    } catch (e) {
      console.error('Error extracting credit info from error:', e);
    }

    return null;
  },
};
