import { ref, onValue } from 'firebase/database';
import { db } from '@/config/firebase';

export function listenForLandingPageUpdate(
  businessId: string,
  callback: (data: any) => void
) {
  const landingPageRef = ref(db, `businesses/${businessId}/landing_page`);

  // Real-time listener
  const unsubscribe = onValue(landingPageRef, (snapshot) => {
    const data = snapshot.val();
    callback(data || { status: null }); // Returns {status: "in_progress"} or null if no data
  });

  // Return unsubscribe function for cleanup
  return unsubscribe;
}
