import { ref, onValue } from 'firebase/database';
import { db } from '@/config/firebase';

export function listenForContextGeneration(
  businessId: string,
  callback: (data: any) => void
) {
  const contextRef = ref(db, `businesses/${businessId}/context`);

  // Real-time listener
  const unsubscribe = onValue(contextRef, (snapshot) => {
    const data = snapshot.val();
    callback(data || { status: null }); // Returns {status: "in_progress"} or null if no data
  });

  // Return unsubscribe function for cleanup
  return unsubscribe;
}
