import { ref, onValue } from 'firebase/database';
import { db } from '@/config/firebase';

export function listenForSocialMediaUpdate(
  businessId: string,
  callback: (data: any) => void
) {
  const socialsRef = ref(db, `businesses/${businessId}/socials`);

  // Real-time listener
  const unsubscribe = onValue(socialsRef, (snapshot) => {
    const data = snapshot.val();
    callback(data || { status: null }); // Returns {status: "in_progress"} or null if no data
  });

  // Return unsubscribe function for cleanup
  return unsubscribe;
}
export function listenForInstagram(
  businessId: string,
  callback: (data: any) => void
) {
  const instagramRef = ref(db, `businesses/${businessId}/socials/instagram`);

  // Real-time listener
  const unsubscribe = onValue(instagramRef, (snapshot) => {
    const data = snapshot.val();
    callback(data || { status: null }); // Returns {status: "in_progress"} or null if no data
  });

  // Return unsubscribe function for cleanup
  return unsubscribe;
}
export function listenForFacebook(
  businessId: string,
  callback: (data: any) => void
) {
  const facebookRef = ref(db, `businesses/${businessId}/socials/facebook`);

  // Real-time listener
  const unsubscribe = onValue(facebookRef, (snapshot) => {
    const data = snapshot.val();
    callback(data || { status: null }); // Returns {status: "in_progress"} or null if no data
  });

  // Return unsubscribe function for cleanup
  return unsubscribe;
}
export function listenForLinkedin(
  businessId: string,
  callback: (data: any) => void
) {
  const linkedinRef = ref(db, `businesses/${businessId}/socials/linkedin`);

  // Real-time listener
  const unsubscribe = onValue(linkedinRef, (snapshot) => {
    const data = snapshot.val();
    callback(data || { status: null }); // Returns {status: "in_progress"} or null if no data
  });

  // Return unsubscribe function for cleanup
  return unsubscribe;
}
export function listenForTwitter(
  businessId: string,
  callback: (data: any) => void
) {
  const twitterRef = ref(db, `businesses/${businessId}/socials/twitter`);

  // Real-time listener
  const unsubscribe = onValue(twitterRef, (snapshot) => {
    const data = snapshot.val();
    callback(data || { status: null }); // Returns {status: "in_progress"} or null if no data
  });

  // Return unsubscribe function for cleanup
  return unsubscribe;
}
export function listenForWhatsapp(
  businessId: string,
  callback: (data: any) => void
) {
  const whatsappRef = ref(db, `businesses/${businessId}/socials/whatsapp`);

  // Real-time listener
  const unsubscribe = onValue(whatsappRef, (snapshot) => {
    const data = snapshot.val();
    callback(data || { status: null }); // Returns {status: "in_progress"} or null if no data
  });

  // Return unsubscribe function for cleanup
  return unsubscribe;
}

export function listenForCampaigns(
  businessId: string,
  callback: (data: any) => void
) {
  const campaignsRef = ref(db, `businesses/${businessId}/campaigns`);

  // Real-time listener
  const unsubscribe = onValue(campaignsRef, (snapshot) => {
    const data = snapshot.val();
    callback(data || { status: null }); // Returns {status: "in_progress"} or null if no data
  });

  // Return unsubscribe function for cleanup
  return unsubscribe;
}
