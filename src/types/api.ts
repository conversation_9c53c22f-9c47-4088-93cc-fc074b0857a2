export interface Project {
  id: string;
  userId: string;
  title: string;
  description: string;
  category: string;
  goal: string;
  created_at: string;
  status: string;
  updated_at: string;
  tags: string[];
  image: string;
  user_input: string;
}

export interface Chat {
  id: string;
  userId: string;
  title: string;
  description: string;
  category: string;
  goal: string;
  created_at: string;
  status: string;
  updated_at: string;
  tags: string[];
  img_url: string;
  user_input: string;
  image: string;
}

export interface HomeApiResponse {
  success: boolean;
  message: string;
  chats: Chat[];
  projects: Project[];
}

export interface ApiError {
  message: string;
  code?: string;
  status?: number;
}
export interface SocialPlatform {
  id: 'twitter' | 'instagram' | 'linkedin' | 'facebook' | 'whatsapp';
  name: string;
  icon: string;
  description: string;
  isConnected: boolean;
  isLoading: boolean;
}

export interface RedirectUrlResponse {
  url: string;
}

export interface ConnectedAccount {
  id?: string;
  user_id?: string;
  name?: string;
  username?: string;
  profile_picture_url?: string;
}
export interface ConnectedAccounts {
  twitter?: ConnectedAccount;
  instagram?: ConnectedAccount;
  linkedin?: ConnectedAccount;
  facebook?: ConnectedAccount;
  whatsapp?: ConnectedAccount;
}

export interface LandingPageCardProps {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  label?: string;
  path: string;
}
export interface MockPost {
  id: string;
  _id?: string;
  content_text: string;
  media_url?: string;
  likes: number;
  comments: number;
  shares?: number;
  retweets?: number;
  timestamp: string;
  created_at?: string;
  username?: string;
  userAvatar?: string;
  location?: string;
  handle?: string;
  post_status?: boolean;
}

export interface SocialMediaPosts {
  instagramPost: MockPost[];
  twitterPost: MockPost[];
  linkedinPost: MockPost[];
  facebookPost: MockPost[];
  whatsappPost: MockPost[];
}
export interface SectionPageCardProps {
  id: string;
  title: string;
  description?: string;
  icon?: React.ComponentType<{ className?: string }> | React.ReactNode;
  label?: string;
  path?: string;
  isActionCard?: boolean;
  actionCardComponent?: () => React.JSX.Element;
  color?: string;
}
