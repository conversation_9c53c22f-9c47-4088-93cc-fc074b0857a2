import { CampaignResponse } from '@/components/calendar/Calendar';
import { MockPost, Project, Chat, ConnectedAccount } from './api';
import * as ActionTypes from '@/constants/actions/actions.constants';

export interface User {
  id: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  preferences?: Record<string, unknown>;
}

export interface Message {
  id: string;
  content: React.ReactNode | string | Record<string, unknown>;
  sender: 'user' | 'bot';
  timestamp: Date;
  avatar?: string;
  editableFields?: { [key: string]: string | undefined };
  originalContent?: string;
  isCard?: boolean;
  userInput?: { [key: string]: string[] };
  canEdit: boolean;
  // Additional fields for enhanced chatbot functionality
  actions?: {
    type: 'button' | 'link';
    label: string;
    value?: string;
    url?: string;
    onClick?: () => void;
  }[];
  metadata?: Record<string, unknown>;
}

export interface ProjectDetails {
  data: ProjectData | null;
  loading: boolean;
  error: string | null;
}

export interface ProjectData {
  id?: string;
  name?: string;
  description?: string;
  isSampleProject?: boolean;
  connectedAccounts?: {
    twitter?: ConnectedAccount;
    instagram?: ConnectedAccount;
    linkedin?: ConnectedAccount;
    facebook?: ConnectedAccount;
    whatsapp?: ConnectedAccount;
  };
  projects?: {
    context: Record<string, Record<string, ContextSection>>;
    chapters: string[];
  };
  socialMediaPosts?: {
    instagramPost: MockPost[];
    twitterPost: MockPost[];
    linkedinPost: MockPost[];
    facebookPost: MockPost[];
    whatsappPost: MockPost[];
  };
  landingPage?: {
    html: string;
    public_url: string;
  };
  campaigns?: CampaignResponse[];
  [key: string]: unknown;
  projectMetaData?: {
    description: string;
    tags: string[];
    status: string;
    title: string;
    updated_at: string;
    created_at: string;
    name: string;
    business_idea_config: {
      biz_type: string;
      location: string;
    };
    location: string;
  };
}

export interface ContextSection {
  title?: string;
  content?: string | Record<string, unknown>;
  userInput?: string[];
  [key: string]: unknown;
}

export interface InitProjects {
  chats?: (Project | Chat)[];
  projects?: Project[];
  [key: string]: unknown;
}

export interface ChatIdeas {
  ideas: Record<string, unknown>;
  loading: boolean;
  error: string | null;
}

export interface LoadingStates {
  contextGeneration?: boolean;
  twitterPostGeneration?: boolean;
  instaPostGeneration?: boolean;
  linkedinPostGeneration?: boolean;
  facebookPostGeneration?: boolean;
  whatsappPostGeneration?: boolean;
  [key: string]: boolean | undefined;
}

export interface GlobalState {
  isDarkMode: boolean;
  user: User | null;
  messages: Message[];
  isTyping: boolean;
  showButtons: boolean;
  proceedCount: number;
  initProjects: InitProjects;
  chatIdeas: ChatIdeas;
  projectDetails: ProjectDetails;
  loadingStates: LoadingStates;
  chatboxMessages: {
    messages: ChatboxMessage[];
    isOpen: boolean;
  };
}

export interface ChatboxMessage {
  id?: string;
  text: string;
  isBot: boolean;
  timestamp: string;
  context?: string; // Optional context identifier (e.g., chapter, section)
  conversation_id?: string; // ID of the conversation from the API
  business_idea_id?: string; // ID of the business idea
  actions?: Array<{
    id: string;
    label: string;
    type: 'primary' | 'secondary' | 'danger' | 'success' | 'warning' | 'info';
    actionType: string;
    data?: any;
  }>;
}

export type ActionType =
  | { type: typeof ActionTypes.SET_DARK_MODE; payload: boolean }
  | { type: typeof ActionTypes.SET_USER; payload: User | null }
  | {
      type: typeof ActionTypes.UPDATE_USER_PREFERENCES;
      payload: Record<string, unknown>;
    }
  | {
      type: typeof ActionTypes.UPDATE_TWITTER_ACCOUNT;
      payload: ConnectedAccount;
    }
  | {
      type: typeof ActionTypes.UPDATE_INSTAGRAM_ACCOUNT;
      payload: ConnectedAccount;
    }
  | {
      type: typeof ActionTypes.UPDATE_LINKEDIN_ACCOUNT;
      payload: ConnectedAccount;
    }
  | { type: typeof ActionTypes.SET_MESSAGES; payload: Message[] }
  | { type: typeof ActionTypes.ADD_MESSAGES; payload: Message[] }
  | {
      type: typeof ActionTypes.UPDATE_MESSAGE;
      payload: { messageId: string; updates: Partial<Message> };
    }
  | { type: typeof ActionTypes.SET_IS_TYPING; payload: boolean }
  | { type: typeof ActionTypes.SET_SHOW_BUTTONS; payload: boolean }
  | { type: typeof ActionTypes.SET_PROCEED_COUNT; payload: number }
  | { type: typeof ActionTypes.SET_INIT_PROJECTS; payload: InitProjects }
  | { type: typeof ActionTypes.FETCH_IDEAS_REQUEST }
  | {
      type: typeof ActionTypes.FETCH_IDEAS_SUCCESS;
      payload: Record<string, unknown>;
    }
  | { type: typeof ActionTypes.FETCH_IDEAS_FAILURE; payload: string }
  | { type: typeof ActionTypes.FETCH_PROJECT_DETAILS_REQUEST }
  | {
      type: typeof ActionTypes.FETCH_PROJECT_DETAILS_SUCCESS;
      payload: ProjectData;
    }
  | { type: typeof ActionTypes.FETCH_PROJECT_DETAILS_FAILURE; payload: string }
  | { type: typeof ActionTypes.FETCH_BUSINESS_META_SUCCESS; payload: any }
  | { type: typeof ActionTypes.FETCH_BUSINESS_META_FAILURE; payload: string }
  | {
      type: typeof ActionTypes.UPDATE_PROJECT_USER_INPUT;
      payload: { chapter: string; section: string; userInput: string[] };
    }
  | {
      type: typeof ActionTypes.UPDATE_SOCIAL_MEDIA_POSTS;
      payload: {
        instagramPost: MockPost[];
        twitterPost: MockPost[];
        linkedinPost: MockPost[];
        facebookPost: MockPost[];
        whatsappPost: MockPost[];
      };
    }
  | { type: typeof ActionTypes.UPDATE_LANDING_PAGE_HTML; payload: string }
  | {
      type: typeof ActionTypes.UPDATE_PROJECT_CHAPTERS;
      payload: Record<string, Record<string, ContextSection>>;
    }
  | {
      type: typeof ActionTypes.SET_LOADING_STATE;
      payload: { key: string; value: boolean | undefined };
    }
  | {
      type: typeof ActionTypes.FETCH_CAMPAIGN_DATA_SUCCESS;
      payload: { campaigns: any[] };
    }
  | {
      type: typeof ActionTypes.UPDATE_FACEBOOK_ACCOUNT;
      payload: ConnectedAccount;
    }
  | { type: typeof ActionTypes.SET_CHATBOX_MESSAGES; payload: ChatboxMessage[] }
  | { type: typeof ActionTypes.ADD_CHATBOX_MESSAGE; payload: ChatboxMessage }
  | { type: typeof ActionTypes.CLEAR_CHATBOX_MESSAGES }
  | { type: typeof ActionTypes.SET_CHATBOX_OPEN; payload: boolean }
  | { type: typeof ActionTypes.SET_IS_SAMPLE_PROJECT; payload: boolean }
  | { type: typeof ActionTypes.RESET_PROJECT_DETAILS };

export interface ChatbotState {
  messages: Message[];
  isTyping: boolean;
  showButtons: boolean;
  proceedCount: number;
}
export interface SocialsPostApiResponse {
  success: boolean;
  message: string;
  posts: {
    instagramPost: MockPost[];
    twitterPost: MockPost[];
    linkedinPost: MockPost[];
    facebookPost: MockPost[];
    whatsappPost: MockPost[];
  };
}

export type PrimitiveValue = string | number | boolean | null;
export interface ObjectValue {
  [key: string]: ContextValue;
}
export type ArrayValue = Array<PrimitiveValue | ObjectValue>;
export type ContextValue = PrimitiveValue | ArrayValue | ObjectValue;
export interface SectionData {
  title: string;
  answer: ContextValue;
  userInput?: string[];
  [key: string]: unknown;
}
export type ChapterData = Record<string, SectionData>;
