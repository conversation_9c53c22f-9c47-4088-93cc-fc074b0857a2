'use client';

import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { GlobalState, ActionType } from '@/types';
import { globalReducer, initialState } from '@/store/reducers/globalReducer';

type GlobalContextType = {
  state: GlobalState;
  dispatch: React.Dispatch<ActionType>;
};

const GlobalContext = createContext<GlobalContextType | undefined>(undefined);

export function GlobalProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(globalReducer, initialState);

  return (
    <GlobalContext.Provider value={{ state, dispatch }}>
      {children}
    </GlobalContext.Provider>
  );
}

export function useGlobalContext() {
  const context = useContext(GlobalContext);
  if (context === undefined) {
    throw new Error('useGlobalContext must be used within a GlobalProvider');
  }
  return context;
}
