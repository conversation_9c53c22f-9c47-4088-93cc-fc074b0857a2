'use client';
import { createContext, useContext, useEffect, useState } from 'react';
import { auth } from '@/config/firebase';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  GoogleAuthProvider,
  signInWithPopup,
  User,
  sendEmailVerification,
  updateProfile,
  sendPasswordResetEmail as firebaseSendPasswordResetEmail,
} from 'firebase/auth';
import { apiService } from '@/services/api';
import { API_ENDPOINT_PAYMENT_STATUS } from '@/constants/api/api.constants';

// Define payment status types
type PaymentStatus = 'free' | 'paid' | 'expired';

interface PaymentInfo {
  status: PaymentStatus;
  expiry: string | null;
  last_payment: string | null;
  is_paid: boolean;
}

type AuthContextType = {
  user: User | null;
  loading: boolean;
  isEmailVerified: boolean;
  paymentInfo: PaymentInfo | null;
  paymentLoading: boolean;
  signUpWithEmail: (
    email: string,
    password: string,
    name: string
  ) => Promise<void>;
  signInWithEmail: (email: string, password: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  logout: () => Promise<void>;
  sendVerificationEmail: () => Promise<void>;
  sendPasswordResetEmail: (email: string) => Promise<void>;
  checkPaymentStatus: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | null>(null);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [paymentInfo, setPaymentInfo] = useState<PaymentInfo | null>(null);
  const [paymentLoading, setPaymentLoading] = useState(false);

  // Function to check payment status
  const checkPaymentStatus = async () => {
    if (!user) return;

    try {
      setPaymentLoading(true);
      const response = await apiService.get<PaymentInfo>(
        API_ENDPOINT_PAYMENT_STATUS
      );

      if (response.success && response.data) {
        setPaymentInfo(response.data as PaymentInfo);
      } else {
        // Set default payment info if API call fails
        setPaymentInfo({
          status: 'free',
          expiry: null,
          last_payment: null,
          is_paid: false,
        });
      }
    } catch (error) {
      console.error('Error checking payment status:', error);
      // Set default payment info if API call fails
      setPaymentInfo({
        status: 'free',
        expiry: null,
        last_payment: null,
        is_paid: false,
      });
    } finally {
      setPaymentLoading(false);
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user);
      // Check if the user's email is verified
      if (user) {
        setIsEmailVerified(user.emailVerified);
        // Reload user to get the latest emailVerified status
        user.reload().then(() => {
          setIsEmailVerified(user.emailVerified);
        });
        // Check payment status when user is authenticated
        checkPaymentStatus();
      } else {
        setIsEmailVerified(false);
        setPaymentInfo(null);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []); // We intentionally don't add checkPaymentStatus as a dependency to avoid infinite loops

  const signUpWithEmail = async (
    email: string,
    password: string,
    name: string
  ) => {
    try {
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        email,
        password
      );
      const user = userCredential.user;
      await sendEmailVerification(user);
      await updateProfile(user, {
        displayName: `${name}`,
      });
    } catch (error) {
      throw error;
    }
  };

  const signInWithEmail = async (email: string, password: string) => {
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password
      );
      const user = userCredential.user;

      // Check if email is verified
      if (!user.emailVerified) {
        // Sign out the user if email is not verified
        await signOut(auth);
        throw new Error('Please verify your email before signing in.');
      }
    } catch (error) {
      throw error;
    }
  };

  const signInWithGoogle = async () => {
    try {
      const provider = new GoogleAuthProvider();
      const firebaseData = await signInWithPopup(auth, provider);
      const { user } = firebaseData;
      // Google accounts are considered verified by default
      setIsEmailVerified(true);
      await apiService.post('/api/signin', {
        uid: user?.uid,
        email: user?.email,
        name: user?.displayName,
        image: user?.photoURL,
        provider_data: user?.providerData,
      });
    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    try {
      await signOut(auth);
    } catch (error) {
      throw error;
    }
  };

  // Function to send verification email
  const sendVerificationEmail = async () => {
    try {
      if (user && !user.emailVerified) {
        await sendEmailVerification(user);
      }
    } catch (error) {
      throw error;
    }
  };

  // Function to send password reset email
  const sendPasswordResetEmail = async (email: string) => {
    try {
      await firebaseSendPasswordResetEmail(auth, email);
    } catch (error) {
      throw error;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        isEmailVerified,
        paymentInfo,
        paymentLoading,
        signUpWithEmail,
        signInWithEmail,
        signInWithGoogle,
        logout,
        sendVerificationEmail,
        sendPasswordResetEmail,
        checkPaymentStatus,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
