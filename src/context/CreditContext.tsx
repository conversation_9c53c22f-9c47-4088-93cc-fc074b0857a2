'use client';

import React, { createContext, useContext, ReactNode, useEffect } from 'react';
import { useCredits } from '@/hooks/useCredits';
import { CreditBalance, CreditFeature } from '@/services/credit';
import { CREDIT_BALANCE_UPDATE_EVENT } from '@/services/api';

// Credit threshold for low balance warning
export const LOW_CREDIT_THRESHOLD = 200;

interface CreditContextType {
  balance: CreditBalance | null;
  features: CreditFeature[];
  loading: boolean;
  error: string | null;
  fetchBalance: () => Promise<void>;
  fetchFeatures: () => Promise<void>;
  handleApiError: (error: any) => boolean;
  isLowBalance: () => boolean;
  getLowBalanceThreshold: () => number;
}

const CreditContext = createContext<CreditContextType | undefined>(undefined);

export function CreditProvider({ children }: { children: ReactNode }) {
  const {
    balance,
    features,
    loading,
    error,
    fetchBalance,
    fetchFeatures,
    handleApiError,
  } = useCredits();

  // Listen for credit balance update events
  useEffect(() => {
    let debounceTimer: NodeJS.Timeout | null = null;

    // Handler function to refresh credit balance with debounce
    const handleCreditBalanceUpdate = () => {
      // Clear any existing timer
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }

      // Set a new timer to fetch balance after a delay
      debounceTimer = setTimeout(() => {
        fetchBalance();
      }, 500); // 500ms debounce
    };

    // Add event listener for credit balance updates
    if (typeof window !== 'undefined') {
      window.addEventListener(
        CREDIT_BALANCE_UPDATE_EVENT,
        handleCreditBalanceUpdate
      );
    }

    // Clean up the event listener and any pending timers when the component unmounts
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener(
          CREDIT_BALANCE_UPDATE_EVENT,
          handleCreditBalanceUpdate
        );
      }

      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [fetchBalance]);

  // Check if the user has a low balance
  const isLowBalance = () => {
    if (!balance) return false;
    return balance.balance <= LOW_CREDIT_THRESHOLD;
  };

  // Get the low balance threshold
  const getLowBalanceThreshold = () => {
    return LOW_CREDIT_THRESHOLD;
  };

  return (
    <CreditContext.Provider
      value={{
        balance,
        features,
        loading,
        error,
        fetchBalance,
        fetchFeatures,
        handleApiError,
        isLowBalance,
        getLowBalanceThreshold,
      }}
    >
      {children}
    </CreditContext.Provider>
  );
}

export function useCredit() {
  const context = useContext(CreditContext);
  if (context === undefined) {
    throw new Error('useCredit must be used within a CreditProvider');
  }
  return context;
}
