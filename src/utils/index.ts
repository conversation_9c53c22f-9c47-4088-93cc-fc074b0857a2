export const formatChapterText = (text: string) => {
  return text
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};
export function convertToIST(utcTimestamp: string): string {
  // Check if the timestamp already includes IST offset (+05:30)
  const isAlreadyIST = utcTimestamp.includes('+05:30');

  let date = new Date(utcTimestamp);
  let istOffset = 5.5 * 60 * 60 * 1000; // 5 hours 30 minutes in milliseconds

  // Only add offset if not already in IST
  let istTime = isAlreadyIST ? date : new Date(date.getTime() + istOffset);

  // Explicitly type options as Intl.DateTimeFormatOptions
  let options: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: 'short',
    hour: 'numeric', // Single-digit hour when applicable
    minute: '2-digit',
    hour12: true,
  };

  return istTime
    .toLocaleString('en-IN', options)
    .replace(
      /(\d{2}) (\w{3}), (\d{1,2}):(\d{2}) (AM|PM)/i,
      '$1 $2, $3:$4 $5'.toLowerCase()
    );
}

/**
 * Calculate relative time based on IST timezone
 * @param timestamp - UTC timestamp string
 * @returns Relative time string in IST (e.g., "2h ago", "Just now")
 */
export function getRelativeTimeIST(timestamp: string): string {
  // Convert the timestamp to IST
  const date = new Date(timestamp);
  const istOffset = 5.5 * 60 * 60 * 1000; // 5 hours 30 minutes in milliseconds
  const istDate = new Date(date.getTime() + istOffset);

  // Get current time in IST
  const now = new Date();
  const istNow = new Date(now.getTime());

  // Calculate time difference
  const diffMs = istNow.getTime() - istDate.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  // Return appropriate relative time
  if (diffDay > 7) {
    return convertToIST(timestamp); // Return full date for older posts
  } else if (diffDay > 1) {
    return `${diffDay}d ago`;
  } else if (diffDay === 1) {
    return '1d ago';
  } else if (diffHour >= 1) {
    return `${diffHour}h ago`;
  } else if (diffMin >= 1) {
    return `${diffMin}m ago`;
  } else {
    return 'Just now';
  }
}

export const getPlatformProfileUrl = (
  platformId: string,
  username: string
): string => {
  switch (platformId) {
    case 'instagram':
      return `https://instagram.com/${username}`;
    case 'twitter':
      return `https://x.com/${username}`;
    case 'facebook':
      return `https://facebook.com/${username}`;
    case 'linkedin':
      return `https://linkedin.com/in/me`;
    case 'whatsapp':
      // WhatsApp doesn't have public profile URLs in the same way
      return '';
    default:
      return '';
  }
};
