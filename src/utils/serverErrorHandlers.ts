import { NextResponse } from 'next/server';

/**
 * Error response from the backend for insufficient credits
 */
interface InsufficientCreditsError {
  detail: {
    message: string;
    required_credits: number;
    available_credits: number;
    feature_key: string;
  };
}

/**
 * Check if the error is an insufficient credits error (402 Payment Required)
 */
export function isInsufficientCreditsError(
  statusCode: number,
  data: any
): data is InsufficientCreditsError {
  return (
    statusCode === 402 &&
    data &&
    data.detail &&
    typeof data.detail.required_credits === 'number' &&
    typeof data.detail.available_credits === 'number'
  );
}

/**
 * Handle API errors in serverless functions
 * This function will pass through the error details for 402 errors
 * and return a generic error for other errors
 */
export function handleServerError(
  error: any,
  genericErrorMessage = 'An error occurred while processing your request'
) {
  console.error('Server error:', error);

  // If it's a fetch error with a response
  if (error.response) {
    try {
      // Try to parse the response
      return error.response.json().then((data: any) => {
        const status = error.response.status;

        // If it's a 402 Payment Required error, pass it through
        if (isInsufficientCreditsError(status, data)) {
          return NextResponse.json(data, { status });
        }

        // For other errors, return a generic error
        return NextResponse.json(
          { error: data.detail?.message || genericErrorMessage },
          { status }
        );
      });
    } catch (parseError) {
      console.error(parseError);
      // If we can't parse the response, return a generic error
      return NextResponse.json(
        { error: genericErrorMessage },
        { status: error.response.status || 500 }
      );
    }
  }

  // For network errors or other errors without a response
  return NextResponse.json({ error: genericErrorMessage }, { status: 500 });
}

/**
 * Handle API responses in serverless functions
 * This function will check if the response is a 402 error and pass it through
 */
export async function handleServerResponse(response: Response) {
  const data = await response.json();
  console.log({ data });
  // If it's a 402 Payment Required error, pass it through
  if (isInsufficientCreditsError(response.status, data)) {
    return NextResponse.json(data, { status: response.status });
  }
  // If the response is not OK, return an error
  if (!response.ok) {
    return NextResponse.json(
      {
        error:
          data.detail?.message ||
          data?.validation_error_msg ||
          'An error occurred',
      },
      { status: response.status }
    );
  }
  console.log('data', data);
  return NextResponse.json(data, { status: 200 });
}
