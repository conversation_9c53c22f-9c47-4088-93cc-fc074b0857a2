'use client';

import { apiService } from '@/services/api';
import { ActionType, ContextSection } from '@/types';
import * as ActionTypes from '@/constants/actions/actions.constants';
import {
  LOADING_STATE_CONTEXT_GENERATION,
  API_ENDPOINT_GENERATE_CONTEXT,
  GENERATE_ERROR_MESSAGE,
  GENERATE_SUCCESS_MESSAGE,
} from '@/constants';

/**
 * Generates context for a project by making an API call
 * @param projectId - The ID of the project to generate context for
 * @param dispatch - The dispatch function from the global context
 * @returns Promise that resolves when the context generation is complete
 */
export async function generateContext(
  projectId: string,
  dispatch: React.Dispatch<ActionType>,
  showToast: (message: string, type: 'info' | 'error' | 'success' | 'warning') => void
) {
  try {
    // Set loading state
    dispatch({
      type: ActionTypes.SET_LOADING_STATE,
      payload: { key: LOADING_STATE_CONTEXT_GENERATION, value: true },
    });

    // Make API call to generate context
    const { data, success } = await apiService.post(
      API_ENDPOINT_GENERATE_CONTEXT,
      {
        projectId,
      }
    );
    if (success) {
      showToast(GENERATE_SUCCESS_MESSAGE, 'success');
    } else {
      showToast(GENERATE_ERROR_MESSAGE, 'error');
    }
    // Update project chapters with the generated context
    dispatch({
      type: ActionTypes.UPDATE_PROJECT_CHAPTERS,
      payload: data as Record<string, Record<string, ContextSection>>,
    });

    return data;
  } catch (error) {
    showToast(GENERATE_ERROR_MESSAGE, 'error');

    console.error('Error generating context:', error);
    throw error;
  } finally {
    // Reset loading state
    dispatch({
      type: ActionTypes.SET_LOADING_STATE,
      payload: { key: LOADING_STATE_CONTEXT_GENERATION, value: false },
    });
  }
}
