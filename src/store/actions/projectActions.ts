'use client';

import { apiService } from '@/services/api';
import { ActionType, ProjectData } from '@/types';
import * as ActionTypes from '@/constants/actions/actions.constants';
import {
  API_ENDPOINT_PROJECT_DETAILS,
  API_ENDPOINT_PROJECT_DETAILS_SECOND_FOLD,
} from '@/constants';

/**
 * Fetches the first fold of project details
 * @param projectId - The ID of the project to fetch details for
 * @param dispatch - The dispatch function from the global context
 * @param showToast - Function to display toast notifications
 * @returns Promise that resolves when the project details fetch is complete
 */
export async function fetchProjectDetails(
  projectId: string,
  dispatch: React.Dispatch<ActionType>,
  showToast: (
    message: string,
    type: 'info' | 'error' | 'success' | 'warning'
  ) => void,
  setFirstApiCompleted?: (completed: boolean) => void
) {
  // Check if project details data is already present and matches the current ID
  // This check should be done in the component before calling this function

  dispatch({ type: ActionTypes.FETCH_PROJECT_DETAILS_REQUEST });
  try {
    const response = await apiService.get(
      `${API_ENDPOINT_PROJECT_DETAILS}?id=${projectId}`
    );
    if (response.success) {
      const projectData = response.data as unknown as ProjectData;

      // Check if this is a sample project
      if (projectData.isSampleProject !== undefined) {
        // Set the isSampleProject flag in the global state
        dispatch({
          type: ActionTypes.SET_IS_SAMPLE_PROJECT,
          payload: projectData.isSampleProject,
        });
      }

      dispatch({
        type: ActionTypes.FETCH_PROJECT_DETAILS_SUCCESS,
        payload: projectData,
      });
    } else {
      dispatch({
        type: ActionTypes.FETCH_PROJECT_DETAILS_FAILURE,
        payload: response.message,
      });
      showToast('Failed to fetch project details', 'error');
    }
  } catch (error) {
    console.log(error);
    dispatch({
      type: ActionTypes.FETCH_PROJECT_DETAILS_FAILURE,
      payload: 'Failed to fetch project details',
    });
  } finally {
    // Mark this API call as completed regardless of success or failure
    if (setFirstApiCompleted) {
      setFirstApiCompleted(true);
    }
  }
}

/**
 * Fetches the second fold of project details including landing page, campaigns, and business meta
 * @param projectId - The ID of the project to fetch details for
 * @param dispatch - The dispatch function from the global context
 * @param showToast - Function to display toast notifications
 * @returns Promise that resolves when the second fold project details fetch is complete
 */
export async function fetchSecondFoldProjectDetails(
  projectId: string,
  dispatch: React.Dispatch<ActionType>,
  showToast: (
    message: string,
    type: 'info' | 'error' | 'success' | 'warning'
  ) => void,
  setSecondApiCompleted?: (completed: boolean) => void
) {
  // Check if project details data is already present and matches the current ID
  // This check should be done in the component before calling this function

  dispatch({ type: ActionTypes.FETCH_PROJECT_DETAILS_REQUEST });
  try {
    const response = await apiService.get(
      `${API_ENDPOINT_PROJECT_DETAILS_SECOND_FOLD}?id=${projectId}`
    );
    if (response.success) {
      // Extract data from the response
      const responseData = response.data as Record<string, any>;
      const { landingPageData, campaigns, projectInfo, ...otherData } =
        responseData;

      // Transform campaign data for calendar events
      let transformedCampaigns: Array<any> = [];
      if (campaigns && Array.isArray(campaigns)) {
        transformedCampaigns = campaigns.map((campaign: any) => ({
          id: campaign.id,
          name: campaign.name,
          user_input: campaign.user_input,
          platforms: campaign.platforms,
          status: campaign.status,
          post_types: campaign.post_types,
          number_of_days: campaign.number_of_days,
          posts_per_day: campaign.posts_per_day,
          created_at: campaign.created_at,
          updated_at: campaign.updated_at,
          schedule: campaign.schedule,
          title: campaign.name, // For FullCalendar display
          start: campaign.created_at.split('T')[0],
          end: campaign.created_at.split('T')[0], // If end date isn't available, use start date
          allDay: true,
          extendedProps: {
            calendar: 'primary',
          },
        }));
      }

      // Handle business meta data from projectInfo
      if (projectInfo) {
        // Dispatch business meta data to the global state
        dispatch({
          type: ActionTypes.FETCH_BUSINESS_META_SUCCESS,
          payload: projectInfo,
        });
      }

      // Combine all data for the global state
      const combinedData = {
        ...otherData,
        landingPage: landingPageData,
        campaigns: transformedCampaigns,
        business_meta: projectInfo, // Add business meta to project details
      } as unknown as ProjectData;

      // Check if this is a sample project
      if (combinedData.isSampleProject !== undefined) {
        // Set the isSampleProject flag in the global state
        dispatch({
          type: ActionTypes.SET_IS_SAMPLE_PROJECT,
          payload: combinedData.isSampleProject,
        });
      }

      dispatch({
        type: ActionTypes.FETCH_PROJECT_DETAILS_SUCCESS,
        payload: combinedData,
      });

      // Also update campaign data in the state
      dispatch({
        type: ActionTypes.FETCH_CAMPAIGN_DATA_SUCCESS,
        payload: { campaigns: transformedCampaigns },
      });
    } else {
      dispatch({
        type: ActionTypes.FETCH_PROJECT_DETAILS_FAILURE,
        payload: response.message,
      });
      showToast('Failed to fetch project details', 'error');
    }
  } catch (error) {
    console.log(error);
    dispatch({
      type: ActionTypes.FETCH_PROJECT_DETAILS_FAILURE,
      payload: 'Failed to fetch project details',
    });

    // Also handle business meta failure
    dispatch({
      type: ActionTypes.FETCH_BUSINESS_META_FAILURE,
      payload: 'Failed to fetch business meta',
    });
  } finally {
    // Mark this API call as completed regardless of success or failure
    if (setSecondApiCompleted) {
      setSecondApiCompleted(true);
    }
  }
}
