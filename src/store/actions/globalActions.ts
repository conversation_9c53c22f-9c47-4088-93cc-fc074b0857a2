'use client';

import { ActionType, User, ConnectedAccount } from '../../types';
import * as ActionTypes from '@/constants/actions/actions.constants';

export const setDarkMode = (isDarkMode: boolean): ActionType => ({
  type: ActionTypes.SET_DARK_MODE,
  payload: isDarkMode,
});

export const setUser = (user: User | null): ActionType => ({
  type: ActionTypes.SET_USER,
  payload: user,
});

export const updateUserPreferences = (
  preferences: Record<string, unknown>
): ActionType => ({
  type: ActionTypes.UPDATE_USER_PREFERENCES,
  payload: preferences,
});

export const updateTwitterAccount = (
  account: ConnectedAccount
): ActionType => ({
  type: ActionTypes.UPDATE_TWITTER_ACCOUNT,
  payload: account,
});

export const updateInstagramAccount = (
  account: ConnectedAccount
): ActionType => ({
  type: ActionTypes.UPDATE_INSTAGRAM_ACCOUNT,
  payload: account,
});

export const updateFacebookAccount = (
  account: ConnectedAccount
): ActionType => ({
  type: ActionTypes.UPDATE_FACEBOOK_ACCOUNT,
  payload: account,
});

export const updateLinkedInAccount = (
  account: ConnectedAccount
): ActionType => ({
  type: ActionTypes.UPDATE_LINKEDIN_ACCOUNT,
  payload: account,
});

export const updateProjectUserInput = (
  chapter: string,
  section: string,
  userInput: string[]
): ActionType => ({
  type: ActionTypes.UPDATE_PROJECT_USER_INPUT,
  payload: { chapter, section, userInput },
});

export const updateSocialMediaPosts = ({
  instagramPost,
  twitterPost,
  linkedinPost,
  facebookPost,
  whatsappPost,
}: {
  instagramPost: any[];
  twitterPost: any[];
  linkedinPost: any[];
  facebookPost: any[];
  whatsappPost: any[];
}): ActionType => ({
  type: ActionTypes.UPDATE_SOCIAL_MEDIA_POSTS,
  payload: {
    instagramPost,
    twitterPost,
    linkedinPost,
    facebookPost,
    whatsappPost,
  },
});

export const setIsSampleProject = (isSampleProject: boolean): ActionType => ({
  type: ActionTypes.SET_IS_SAMPLE_PROJECT,
  payload: isSampleProject,
});
