'use client';

import { GlobalState, ActionType, Message } from '../../types';
import * as ActionTypes from '@/constants/actions/actions.constants';

export const initialState: GlobalState = {
  isDarkMode: false,
  user: null,
  messages: [],
  isTyping: false,
  showButtons: false,
  proceedCount: 0,
  initProjects: {},
  chatIdeas: {
    ideas: {},
    loading: false,
    error: null,
  },
  projectDetails: {
    data: null,
    loading: false,
    error: null,
  },
  loadingStates: {
    contextGeneration: false,
    twitterPostGeneration: false,
    instaPostGeneration: false,
    linkedinPostGeneration: false,
    facebookPostGeneration: false,
    whatsappPostGeneration: false,
    landingPageGeneration: false,
    socialMediaGeneration: false,
    contentCalendarGeneration: false,
  },
  chatboxMessages: {
    messages: [],
    isOpen: false,
  },
};

export function globalReducer(
  state: GlobalState,
  action: ActionType
): GlobalState {
  switch (action.type) {
    case ActionTypes.FETCH_PROJECT_DETAILS_REQUEST:
      return {
        ...state,
        projectDetails: {
          ...state.projectDetails,
          loading: true,
          error: null,
        },
      };
    case ActionTypes.FETCH_PROJECT_DETAILS_SUCCESS:
      return {
        ...state,
        projectDetails: {
          data: {
            ...state.projectDetails.data,
            ...action.payload,
          },
          loading: false,
          error: null,
        },
      };
    case ActionTypes.SET_IS_SAMPLE_PROJECT:
      if (!state.projectDetails.data) return state;
      return {
        ...state,
        projectDetails: {
          ...state.projectDetails,
          data: {
            ...state.projectDetails.data,
            isSampleProject: action.payload,
          },
        },
      };
    case ActionTypes.FETCH_PROJECT_DETAILS_FAILURE:
      return {
        ...state,
        projectDetails: {
          ...state.projectDetails,
          loading: false,
          error: action.payload,
        },
      };
    case ActionTypes.FETCH_BUSINESS_META_SUCCESS:
      return {
        ...state,
        projectDetails: {
          ...state.projectDetails,
          data: {
            ...state.projectDetails.data,
            projectMetaData: action.payload, // Taking the first item from the array
          },
          loading: false,
          error: null,
        },
      };
    case ActionTypes.FETCH_BUSINESS_META_FAILURE:
      return {
        ...state,
        projectDetails: {
          ...state.projectDetails,
          error: action.payload,
        },
      };
    case ActionTypes.FETCH_IDEAS_REQUEST:
      return {
        ...state,
        chatIdeas: {
          ...state.chatIdeas,
          loading: true,
          error: null,
        },
      };
    case ActionTypes.FETCH_IDEAS_SUCCESS:
      return {
        ...state,
        chatIdeas: {
          ideas: action.payload,
          loading: false,
          error: null,
        },
      };
    case ActionTypes.FETCH_IDEAS_FAILURE:
      return {
        ...state,
        chatIdeas: {
          ...state.chatIdeas,
          loading: false,
          error: action.payload,
        },
      };
    case ActionTypes.SET_DARK_MODE:
      return {
        ...state,
        isDarkMode: action.payload,
      };
    case ActionTypes.SET_USER:
      return {
        ...state,
        user: action.payload,
      };
    case ActionTypes.UPDATE_USER_PREFERENCES:
      if (!state.user) return state;
      return {
        ...state,
        user: {
          ...state.user,
          preferences: { ...state.user.preferences, ...action.payload },
        },
      };
    case ActionTypes.UPDATE_TWITTER_ACCOUNT:
      if (!state.projectDetails.data) return state;
      return {
        ...state,
        projectDetails: {
          ...state.projectDetails,
          data: {
            ...state.projectDetails.data,
            connectedAccounts: {
              ...(state.projectDetails.data.connectedAccounts || {}),
              twitter: action.payload,
            },
          },
        },
      };
    case ActionTypes.UPDATE_INSTAGRAM_ACCOUNT:
      if (!state.projectDetails.data) return state;
      return {
        ...state,
        projectDetails: {
          ...state.projectDetails,
          data: {
            ...state.projectDetails.data,
            connectedAccounts: {
              ...(state.projectDetails.data.connectedAccounts || {}),
              instagram: action.payload,
            },
          },
        },
      };
    case ActionTypes.UPDATE_LINKEDIN_ACCOUNT:
      if (!state.projectDetails.data) return state;
      return {
        ...state,
        projectDetails: {
          ...state.projectDetails,
          data: {
            ...state.projectDetails.data,
            connectedAccounts: {
              ...(state.projectDetails.data.connectedAccounts || {}),
              linkedin: action.payload,
            },
          },
        },
      };
    case ActionTypes.SET_INIT_PROJECTS:
      return {
        ...state,
        initProjects: action.payload,
      };
    case ActionTypes.UPDATE_PROJECT_USER_INPUT:
      if (!state.projectDetails?.data?.projects?.context) return state;
      const { chapter, section, userInput } = action.payload;
      const updatedContext = { ...state.projectDetails.data.projects.context };
      if (updatedContext?.[chapter]?.[section]) {
        updatedContext[chapter][section] = {
          ...updatedContext[chapter][section],
          userInput: [
            ...(updatedContext[chapter][section]?.userInput || []),
            ...userInput,
          ],
        };
      }
      return {
        ...state,
        projectDetails: {
          ...state.projectDetails,
          data: {
            ...state.projectDetails.data,
            projects: {
              ...state.projectDetails.data.projects,
              context: updatedContext,
            },
          },
        },
      };
    case ActionTypes.UPDATE_PROJECT_CHAPTERS: {
      if (!state.projectDetails.data || !state.projectDetails.data.projects)
        return state;
      return {
        ...state,
        projectDetails: {
          ...state.projectDetails,
          data: {
            ...state.projectDetails.data,
            projects: {
              ...state.projectDetails.data.projects,
              context: action.payload,
              chapters: Object.keys(action.payload),
            },
          },
        },
      };
    }
    case ActionTypes.SET_MESSAGES:
      return {
        ...state,
        messages: action.payload,
      };
    case ActionTypes.ADD_MESSAGES:
      return {
        ...state,
        messages: [...state.messages, ...action.payload],
      };
    case ActionTypes.UPDATE_MESSAGE:
      return {
        ...state,
        messages: state.messages.map((msg: Message) =>
          msg.id === action.payload.messageId
            ? { ...msg, ...action.payload.updates }
            : msg
        ),
      };
    case ActionTypes.FETCH_CAMPAIGN_DATA_SUCCESS:
      return {
        ...state,
        projectDetails: {
          ...state.projectDetails,
          data: {
            ...state.projectDetails.data,
            campaigns: action.payload.campaigns,
          },
        },
      };
    case ActionTypes.SET_SHOW_BUTTONS:
      return {
        ...state,
        showButtons: action.payload,
      };
    case ActionTypes.SET_PROCEED_COUNT:
      return {
        ...state,
        proceedCount: action.payload,
      };
    case ActionTypes.UPDATE_SOCIAL_MEDIA_POSTS:
      if (
        !state.projectDetails.data ||
        !state.projectDetails.data.socialMediaPosts
      )
        return state;
      return {
        ...state,
        projectDetails: {
          ...state.projectDetails,
          data: {
            ...state.projectDetails.data,
            socialMediaPosts: {
              instagramPost: [
                ...action.payload.instagramPost,
                ...(state.projectDetails.data.socialMediaPosts.instagramPost ||
                  []),
              ],
              twitterPost: [
                ...action.payload.twitterPost,
                ...(state.projectDetails.data.socialMediaPosts.twitterPost ||
                  []),
              ],
              linkedinPost: [
                ...action.payload.linkedinPost,
                ...(state.projectDetails.data.socialMediaPosts.linkedinPost ||
                  []),
              ],
              facebookPost: [
                ...(action.payload.facebookPost || []),
                ...(state.projectDetails.data.socialMediaPosts?.facebookPost ||
                  []),
              ],
              whatsappPost: [
                ...(action.payload.whatsappPost || []),
                ...(state.projectDetails.data.socialMediaPosts?.whatsappPost ||
                  []),
              ],
            },
          },
        },
      };
    case ActionTypes.UPDATE_LANDING_PAGE_HTML:
      if (!state.projectDetails.data) return state;
      return {
        ...state,
        projectDetails: {
          ...state.projectDetails,
          data: {
            ...state.projectDetails.data,
            landingPage: {
              ...(state.projectDetails.data.landingPage || { public_url: '' }),
              html: action.payload,
            },
          },
        },
      };
    case ActionTypes.UPDATE_FACEBOOK_ACCOUNT:
      if (!state.projectDetails.data) return state;
      return {
        ...state,
        projectDetails: {
          ...state.projectDetails,
          data: {
            ...state.projectDetails.data,
            connectedAccounts: {
              ...(state.projectDetails.data.connectedAccounts || {}),
              facebook: action.payload,
            },
          },
        },
      };
    case ActionTypes.SET_LOADING_STATE:
      return {
        ...state,
        loadingStates: {
          ...state.loadingStates,
          [action.payload.key]: action.payload.value,
        },
      };
    case ActionTypes.SET_CHATBOX_MESSAGES:
      return {
        ...state,
        chatboxMessages: {
          ...state.chatboxMessages,
          messages: action.payload,
        },
      };
    case ActionTypes.ADD_CHATBOX_MESSAGE:
      return {
        ...state,
        chatboxMessages: {
          ...state.chatboxMessages,
          messages: [...state.chatboxMessages.messages, action.payload],
        },
      };
    case ActionTypes.CLEAR_CHATBOX_MESSAGES:
      return {
        ...state,
        chatboxMessages: {
          ...state.chatboxMessages,
          messages: [],
        },
      };

    case ActionTypes.RESET_PROJECT_DETAILS:
      return {
        ...state,
        projectDetails: initialState.projectDetails,
        loadingStates: initialState.loadingStates,
      };
    case ActionTypes.SET_CHATBOX_OPEN:
      return {
        ...state,
        chatboxMessages: {
          ...state.chatboxMessages,
          isOpen: action.payload,
        },
      };
    default:
      return state;
  }
}
